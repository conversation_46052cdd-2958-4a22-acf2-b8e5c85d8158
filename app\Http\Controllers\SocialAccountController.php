<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;

class SocialAccountController extends Controller
{
    /**
     * Redirect to social provider for account linking
     */
    public function connect(string $provider): RedirectResponse
    {
        // Check if provider is enabled
        if (!config("services.social_auth.providers.{$provider}.enabled", false)) {
            return redirect()->route('profile.show')
                ->with('error', ucfirst($provider) . ' login is not enabled.');
        }

        // Store the intent to connect (not login)
        session(['social_connect_intent' => true]);

        try {
            return Socialite::driver($provider)->redirect();
        } catch (\Exception $e) {
            return redirect()->route('profile.show')
                ->with('error', 'Unable to connect to ' . ucfirst($provider));
        }
    }

    /**
     * Handle social provider callback for account linking
     */
    public function callback(string $provider): RedirectResponse
    {
        try {
            $socialUser = Socialite::driver($provider)->user();
            $user = Auth::user();

            // Check if this social account is already connected to another user
            $existingUser = User::where('social_accounts->' . $provider . '.provider_id', $socialUser->getId())
                ->where('id', '!=', $user->id)
                ->first();

            if ($existingUser) {
                return redirect()->route('profile.show')
                    ->with('error', 'This ' . ucfirst($provider) . ' account is already connected to another user.');
            }

            // Add the social account to current user
            $user->addSocialAccount($provider, [
                'id' => $socialUser->getId(),
                'email' => $socialUser->getEmail(),
                'name' => $socialUser->getName(),
                'avatar' => $socialUser->getAvatar(),
            ]);

            // Clear the connection intent
            session()->forget('social_connect_intent');

            return redirect()->route('profile.show')
                ->with('success', ucfirst($provider) . ' account connected successfully!');

        } catch (\Exception $e) {
            session()->forget('social_connect_intent');

            return redirect()->route('profile.show')
                ->with('error', 'Failed to connect ' . ucfirst($provider) . ' account. Please try again.');
        }
    }

    /**
     * Disconnect a social account
     */
    public function disconnect(string $provider): RedirectResponse
    {
        $user = Auth::user();

        // Check if user has a password or other social accounts before disconnecting
        $connectedAccounts = $user->getConnectedSocialAccounts();
        $hasPassword = !empty($user->password);
        $hasOtherSocialAccounts = count($connectedAccounts) > 1;
        $hasPhoneAuth = !empty($user->phone) && !empty($user->phone_verified_at);

        if (!$hasPassword && !$hasOtherSocialAccounts && !$hasPhoneAuth) {
            return redirect()->route('profile.show')
                ->with('error', 'Cannot disconnect ' . ucfirst($provider) . '. You must have at least one way to login (password, phone, or another social account).');
        }

        if (!$user->hasSocialAccount($provider)) {
            return redirect()->route('profile.show')
                ->with('error', ucfirst($provider) . ' account is not connected.');
        }

        $user->removeSocialAccount($provider);

        return redirect()->route('profile.show')
            ->with('success', ucfirst($provider) . ' account disconnected successfully.');
    }
}
