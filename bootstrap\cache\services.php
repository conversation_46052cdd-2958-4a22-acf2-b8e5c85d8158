<?php return array (
  'providers' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    2 => 'Illuminate\\Bus\\BusServiceProvider',
    3 => 'Illuminate\\Cache\\CacheServiceProvider',
    4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    5 => 'Illuminate\\Cookie\\CookieServiceProvider',
    6 => 'Illuminate\\Database\\DatabaseServiceProvider',
    7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    10 => 'Illuminate\\Hashing\\HashServiceProvider',
    11 => 'Illuminate\\Mail\\MailServiceProvider',
    12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    15 => 'Illuminate\\Queue\\QueueServiceProvider',
    16 => 'Illuminate\\Redis\\RedisServiceProvider',
    17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    18 => 'Illuminate\\Session\\SessionServiceProvider',
    19 => 'Illuminate\\Translation\\TranslationServiceProvider',
    20 => 'Illuminate\\Validation\\ValidationServiceProvider',
    21 => 'Illuminate\\View\\ViewServiceProvider',
    22 => 'Akaunting\\Money\\Provider',
    23 => 'Arcanedev\\LogViewer\\LogViewerServiceProvider',
    24 => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    25 => 'Barryvdh\\Debugbar\\ServiceProvider',
    26 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    27 => 'BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider',
    28 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    29 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    30 => 'CWSPS154\\AppSettings\\AppSettingsServiceProvider',
    31 => 'Filament\\Actions\\ActionsServiceProvider',
    32 => 'Filament\\FilamentServiceProvider',
    33 => 'Filament\\Forms\\FormsServiceProvider',
    34 => 'Filament\\Infolists\\InfolistsServiceProvider',
    35 => 'Filament\\Notifications\\NotificationsServiceProvider',
    36 => 'Filament\\SpatieLaravelSettingsPluginServiceProvider',
    37 => 'Filament\\SpatieLaravelTranslatablePluginServiceProvider',
    38 => 'Filament\\Support\\SupportServiceProvider',
    39 => 'Filament\\Tables\\TablesServiceProvider',
    40 => 'Filament\\Widgets\\WidgetsServiceProvider',
    41 => 'Flowframe\\Trend\\TrendServiceProvider',
    42 => 'GeneaLabs\\LaravelModelCaching\\Providers\\Service',
    43 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    44 => 'Laravel\\Horizon\\HorizonServiceProvider',
    45 => 'Laravel\\Sail\\SailServiceProvider',
    46 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    47 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    48 => 'Laravel\\Tinker\\TinkerServiceProvider',
    49 => 'Laravelcm\\Subscriptions\\SubscriptionServiceProvider',
    50 => 'Livewire\\LivewireServiceProvider',
    51 => 'MixCode\\FilamentMulti2fa\\FilamentMulti2faServiceProvider',
    52 => 'Carbon\\Laravel\\ServiceProvider',
    53 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    54 => 'Termwind\\Laravel\\TermwindServiceProvider',
    55 => 'Plisio\\PlisioSdkLaravel\\Providers\\PlisioProvider',
    56 => 'Propaganistas\\LaravelPhone\\PhoneServiceProvider',
    57 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    58 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    59 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    60 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    61 => 'Spatie\\Permission\\PermissionServiceProvider',
    62 => 'Spatie\\LaravelSettings\\LaravelSettingsServiceProvider',
    63 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    64 => 'Spatie\\Tags\\TagsServiceProvider',
    65 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    66 => 'Squire\\CountriesServiceProvider',
    67 => 'Squire\\CountriesEnServiceProvider',
    68 => 'Squire\\CurrenciesServiceProvider',
    69 => 'Squire\\CurrenciesEnServiceProvider',
    70 => 'Squire\\ModelServiceProvider',
    71 => 'Squire\\RepositoryServiceProvider',
    72 => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    73 => 'Statikbe\\FilamentTranslationManager\\FilamentTranslationManagerServiceProvider',
    74 => 'TomatoPHP\\ConsoleHelpers\\ConsoleHelpersServiceProvider',
    75 => 'TomatoPHP\\FilamentIcons\\FilamentIconsServiceProvider',
    76 => 'TomatoPHP\\FilamentLocations\\FilamentLocationsServiceProvider',
    77 => 'TomatoPHP\\FilamentMediaManager\\FilamentMediaManagerServiceProvider',
    78 => 'TomatoPHP\\FilamentPayments\\FilamentPaymentsServiceProvider',
    79 => 'TomatoPHP\\FilamentSettingsHub\\FilamentSettingsHubServiceProvider',
    80 => 'TomatoPHP\\FilamentSubscriptions\\FilamentSubscriptionsServiceProvider',
    81 => 'TomatoPHP\\FilamentTranslationComponent\\FilamentTranslationComponentServiceProvider',
    82 => 'Ysfkaya\\FilamentPhoneInput\\FilamentPhoneInputServiceProvider',
    83 => 'App\\Providers\\AppServiceProvider',
    84 => 'App\\Providers\\AuthServiceProvider',
    85 => 'App\\Providers\\BroadcastServiceProvider',
    86 => 'App\\Providers\\EventServiceProvider',
    87 => 'App\\Providers\\HorizonServiceProvider',
    88 => 'App\\Providers\\MediaManagerFixServiceProvider',
    89 => 'App\\Providers\\Filament\\AdminPanelProvider',
    90 => 'App\\Providers\\Filament\\AppPanelProvider',
    91 => 'App\\Providers\\RouteServiceProvider',
  ),
  'eager' => 
  array (
    0 => 'Illuminate\\Auth\\AuthServiceProvider',
    1 => 'Illuminate\\Cookie\\CookieServiceProvider',
    2 => 'Illuminate\\Database\\DatabaseServiceProvider',
    3 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
    4 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
    5 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
    6 => 'Illuminate\\Notifications\\NotificationServiceProvider',
    7 => 'Illuminate\\Pagination\\PaginationServiceProvider',
    8 => 'Illuminate\\Session\\SessionServiceProvider',
    9 => 'Illuminate\\View\\ViewServiceProvider',
    10 => 'Akaunting\\Money\\Provider',
    11 => 'Arcanedev\\LogViewer\\LogViewerServiceProvider',
    12 => 'Barryvdh\\Debugbar\\ServiceProvider',
    13 => 'BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider',
    14 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    15 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    16 => 'CWSPS154\\AppSettings\\AppSettingsServiceProvider',
    17 => 'Filament\\Actions\\ActionsServiceProvider',
    18 => 'Filament\\FilamentServiceProvider',
    19 => 'Filament\\Forms\\FormsServiceProvider',
    20 => 'Filament\\Infolists\\InfolistsServiceProvider',
    21 => 'Filament\\Notifications\\NotificationsServiceProvider',
    22 => 'Filament\\SpatieLaravelSettingsPluginServiceProvider',
    23 => 'Filament\\SpatieLaravelTranslatablePluginServiceProvider',
    24 => 'Filament\\Support\\SupportServiceProvider',
    25 => 'Filament\\Tables\\TablesServiceProvider',
    26 => 'Filament\\Widgets\\WidgetsServiceProvider',
    27 => 'Flowframe\\Trend\\TrendServiceProvider',
    28 => 'GeneaLabs\\LaravelModelCaching\\Providers\\Service',
    29 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    30 => 'Laravel\\Horizon\\HorizonServiceProvider',
    31 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    32 => 'Laravelcm\\Subscriptions\\SubscriptionServiceProvider',
    33 => 'Livewire\\LivewireServiceProvider',
    34 => 'MixCode\\FilamentMulti2fa\\FilamentMulti2faServiceProvider',
    35 => 'Carbon\\Laravel\\ServiceProvider',
    36 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    37 => 'Termwind\\Laravel\\TermwindServiceProvider',
    38 => 'Plisio\\PlisioSdkLaravel\\Providers\\PlisioProvider',
    39 => 'Propaganistas\\LaravelPhone\\PhoneServiceProvider',
    40 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    41 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    42 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    43 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    44 => 'Spatie\\Permission\\PermissionServiceProvider',
    45 => 'Spatie\\LaravelSettings\\LaravelSettingsServiceProvider',
    46 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    47 => 'Spatie\\Tags\\TagsServiceProvider',
    48 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    49 => 'Squire\\CountriesServiceProvider',
    50 => 'Squire\\CountriesEnServiceProvider',
    51 => 'Squire\\CurrenciesServiceProvider',
    52 => 'Squire\\CurrenciesEnServiceProvider',
    53 => 'Squire\\ModelServiceProvider',
    54 => 'Squire\\RepositoryServiceProvider',
    55 => 'Statikbe\\FilamentTranslationManager\\FilamentTranslationManagerServiceProvider',
    56 => 'TomatoPHP\\ConsoleHelpers\\ConsoleHelpersServiceProvider',
    57 => 'TomatoPHP\\FilamentIcons\\FilamentIconsServiceProvider',
    58 => 'TomatoPHP\\FilamentLocations\\FilamentLocationsServiceProvider',
    59 => 'TomatoPHP\\FilamentMediaManager\\FilamentMediaManagerServiceProvider',
    60 => 'TomatoPHP\\FilamentPayments\\FilamentPaymentsServiceProvider',
    61 => 'TomatoPHP\\FilamentSettingsHub\\FilamentSettingsHubServiceProvider',
    62 => 'TomatoPHP\\FilamentSubscriptions\\FilamentSubscriptionsServiceProvider',
    63 => 'TomatoPHP\\FilamentTranslationComponent\\FilamentTranslationComponentServiceProvider',
    64 => 'Ysfkaya\\FilamentPhoneInput\\FilamentPhoneInputServiceProvider',
    65 => 'App\\Providers\\AppServiceProvider',
    66 => 'App\\Providers\\AuthServiceProvider',
    67 => 'App\\Providers\\BroadcastServiceProvider',
    68 => 'App\\Providers\\EventServiceProvider',
    69 => 'App\\Providers\\HorizonServiceProvider',
    70 => 'App\\Providers\\MediaManagerFixServiceProvider',
    71 => 'App\\Providers\\Filament\\AdminPanelProvider',
    72 => 'App\\Providers\\Filament\\AppPanelProvider',
    73 => 'App\\Providers\\RouteServiceProvider',
  ),
  'deferred' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastManager' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Factory' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Contracts\\Broadcasting\\Broadcaster' => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
    'Illuminate\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\Dispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Contracts\\Bus\\QueueingDispatcher' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\BatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'Illuminate\\Bus\\DatabaseBatchRepository' => 'Illuminate\\Bus\\BusServiceProvider',
    'cache' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.store' => 'Illuminate\\Cache\\CacheServiceProvider',
    'cache.psr6' => 'Illuminate\\Cache\\CacheServiceProvider',
    'memcached.connector' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Cache\\RateLimiter' => 'Illuminate\\Cache\\CacheServiceProvider',
    'Illuminate\\Foundation\\Console\\AboutCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\ForgetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ClearCompiledCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Auth\\Console\\ClearResetsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigShowCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DbCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\PruneCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\ShowCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\WipeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DownCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentDecryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnvironmentEncryptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Concurrency\\Console\\InvokeSerializedClosureCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\KeyGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\OptimizeClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PackageDiscoverCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\PruneStaleTagsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FlushFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ForgetFailedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\ListenCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\MonitorCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneBatchesCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\PruneFailedJobsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RestartCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\RetryBatchCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\WorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RouteListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\DumpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeedCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleFinishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleRunCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleClearCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleTestCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleWorkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Console\\Scheduling\\ScheduleInterruptCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\ShowModelCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StorageLinkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StorageUnlinkCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\UpCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewCacheCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewClearCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ApiInstallCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\BroadcastingInstallCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Cache\\Console\\CacheTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\CastMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ChannelListCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ChannelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ClassMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ComponentMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConfigPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ConsoleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\ControllerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\DocsCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EnumMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventGenerateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\EventMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ExceptionMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Factories\\FactoryMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\InterfaceMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\JobMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\JobMiddlewareMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\LangPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ListenerMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\MailMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Routing\\Console\\MiddlewareMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ModelMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\NotificationMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Notifications\\Console\\NotificationTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ObserverMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\PolicyMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ProviderMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\FailedTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\TableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Queue\\Console\\BatchesTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RequestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ResourceMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\RuleMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ScopeMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Seeds\\SeederMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Session\\Console\\SessionTableCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ServeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\StubPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\TestMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\TraitMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\VendorPublishCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Foundation\\Console\\ViewMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migrator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.repository' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'migration.creator' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\FreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\InstallCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RefreshCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\ResetCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\RollbackCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\StatusCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'Illuminate\\Database\\Console\\Migrations\\MigrateMakeCommand' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'composer' => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
    'hash' => 'Illuminate\\Hashing\\HashServiceProvider',
    'hash.driver' => 'Illuminate\\Hashing\\HashServiceProvider',
    'mail.manager' => 'Illuminate\\Mail\\MailServiceProvider',
    'mailer' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Mail\\Markdown' => 'Illuminate\\Mail\\MailServiceProvider',
    'Illuminate\\Contracts\\Pipeline\\Hub' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'pipeline' => 'Illuminate\\Pipeline\\PipelineServiceProvider',
    'queue' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.connection' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.failer' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.listener' => 'Illuminate\\Queue\\QueueServiceProvider',
    'queue.worker' => 'Illuminate\\Queue\\QueueServiceProvider',
    'redis' => 'Illuminate\\Redis\\RedisServiceProvider',
    'redis.connection' => 'Illuminate\\Redis\\RedisServiceProvider',
    'auth.password' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'auth.password.broker' => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
    'translator' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'translation.loader' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'validator' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'validation.presence' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Illuminate\\Contracts\\Validation\\UncompromisedVerifier' => 'Illuminate\\Validation\\ValidationServiceProvider',
    'Arcanedev\\LogViewer\\Contracts\\LogViewer' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogLevels' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogStyler' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogMenu' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\Filesystem' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\Factory' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Arcanedev\\LogViewer\\Contracts\\Utilities\\LogChecker' => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\GeneratorCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\ModelsCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\MetaCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Barryvdh\\LaravelIdeHelper\\Console\\EloquentCommand' => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    'Laravel\\Sail\\Console\\InstallCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Sail\\Console\\PublishCommand' => 'Laravel\\Sail\\SailServiceProvider',
    'Laravel\\Socialite\\Contracts\\Factory' => 'Laravel\\Socialite\\SocialiteServiceProvider',
    'command.tinker' => 'Laravel\\Tinker\\TinkerServiceProvider',
    'translation.loader.custom' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'translation.loader.default' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'translation.manager' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'chained-translator.path.lang.custom' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    'Statikbe\\LaravelChainedTranslator\\ChainedTranslationManager' => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
  ),
  'when' => 
  array (
    'Illuminate\\Broadcasting\\BroadcastServiceProvider' => 
    array (
    ),
    'Illuminate\\Bus\\BusServiceProvider' => 
    array (
    ),
    'Illuminate\\Cache\\CacheServiceProvider' => 
    array (
    ),
    'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider' => 
    array (
    ),
    'Illuminate\\Hashing\\HashServiceProvider' => 
    array (
    ),
    'Illuminate\\Mail\\MailServiceProvider' => 
    array (
    ),
    'Illuminate\\Pipeline\\PipelineServiceProvider' => 
    array (
    ),
    'Illuminate\\Queue\\QueueServiceProvider' => 
    array (
    ),
    'Illuminate\\Redis\\RedisServiceProvider' => 
    array (
    ),
    'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider' => 
    array (
    ),
    'Illuminate\\Translation\\TranslationServiceProvider' => 
    array (
    ),
    'Illuminate\\Validation\\ValidationServiceProvider' => 
    array (
    ),
    'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider' => 
    array (
    ),
    'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider' => 
    array (
    ),
    'Laravel\\Sail\\SailServiceProvider' => 
    array (
    ),
    'Laravel\\Socialite\\SocialiteServiceProvider' => 
    array (
    ),
    'Laravel\\Tinker\\TinkerServiceProvider' => 
    array (
    ),
    'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider' => 
    array (
    ),
  ),
);