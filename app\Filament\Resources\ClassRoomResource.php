<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ClassRoomResource\Pages;
use App\Models\ClassRoom;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ClassRoomResource extends Resource
{
    protected static ?string $model = ClassRoom::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationLabel = 'Classrooms';

    protected static ?string $modelLabel = 'Classroom';

    protected static ?string $pluralModelLabel = 'Classrooms';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'classRooms';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('room_name')
                            ->label('Room Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., Math Lab, Science Room, Class 1A')
                            ->columnSpan(2),
                        
                        Forms\Components\TextInput::make('room_number')
                            ->label('Room Number')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('e.g., 101, A-205, Lab-1')
                            ->unique(ignoreRecord: true)
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('grade_level')
                            ->label('Grade Level')
                            ->required()
                            ->options([
                                'Kindergarten' => 'Kindergarten',
                                'Pre-K' => 'Pre-K',
                                'Grade 1' => 'Grade 1',
                                'Grade 2' => 'Grade 2',
                                'Grade 3' => 'Grade 3',
                                'Grade 4' => 'Grade 4',
                                'Grade 5' => 'Grade 5',
                                'Grade 6' => 'Grade 6',
                                'Grade 7' => 'Grade 7',
                                'Grade 8' => 'Grade 8',
                                'Grade 9' => 'Grade 9',
                                'Grade 10' => 'Grade 10',
                                'Grade 11' => 'Grade 11',
                                'Grade 12' => 'Grade 12',
                                'Mixed' => 'Mixed Grades',
                                'Special' => 'Special Purpose',
                            ])
                            ->searchable()
                            ->columnSpan(1),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Details')
                    ->schema([
                        Forms\Components\TextInput::make('capacity')
                            ->label('Student Capacity')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->placeholder('Maximum number of students')
                            ->columnSpan(1),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('remark')
                            ->label('Remarks')
                            ->rows(3)
                            ->placeholder('Additional notes or comments about this classroom')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('room_number')
                    ->label('Room #')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->badge()
                    ->color('primary'),
                
                Tables\Columns\TextColumn::make('room_name')
                    ->label('Room Name')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),
                
                Tables\Columns\TextColumn::make('grade_level')
                    ->label('Grade Level')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        str_contains(strtolower($state), 'kindergarten') || str_contains(strtolower($state), 'pre-k') => 'success',
                        str_contains($state, 'Grade') && (int) filter_var($state, FILTER_SANITIZE_NUMBER_INT) <= 5 => 'info',
                        str_contains($state, 'Grade') && (int) filter_var($state, FILTER_SANITIZE_NUMBER_INT) <= 8 => 'warning',
                        str_contains($state, 'Grade') && (int) filter_var($state, FILTER_SANITIZE_NUMBER_INT) <= 12 => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('capacity')
                    ->label('Capacity')
                    ->numeric()
                    ->sortable()
                    ->suffix(' students')
                    ->placeholder('Not set'),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('remark')
                    ->label('Remarks')
                    ->limit(30)
                    ->tooltip(function (ClassRoom $record): ?string {
                        return $record->remark;
                    })
                    ->placeholder('No remarks'),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('grade_level')
                    ->label('Grade Level')
                    ->options([
                        'Kindergarten' => 'Kindergarten',
                        'Pre-K' => 'Pre-K',
                        'Grade 1' => 'Grade 1',
                        'Grade 2' => 'Grade 2',
                        'Grade 3' => 'Grade 3',
                        'Grade 4' => 'Grade 4',
                        'Grade 5' => 'Grade 5',
                        'Grade 6' => 'Grade 6',
                        'Grade 7' => 'Grade 7',
                        'Grade 8' => 'Grade 8',
                        'Grade 9' => 'Grade 9',
                        'Grade 10' => 'Grade 10',
                        'Grade 11' => 'Grade 11',
                        'Grade 12' => 'Grade 12',
                        'Mixed' => 'Mixed Grades',
                        'Special' => 'Special Purpose',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('room_number', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClassRooms::route('/'),
            'create' => Pages\CreateClassRoom::route('/create'),
            'view' => Pages\ViewClassRoom::route('/{record}'),
            'edit' => Pages\EditClassRoom::route('/{record}/edit'),
        ];
    }
}
