<?php

namespace App\Services;

use <PERSON><PERSON>\MediaLibrary\MediaCollections\Models\Media;
use Spatie\MediaLibrary\Support\PathGenerator\PathGenerator;

class MediaPathGenerator implements PathGenerator
{
    /**
     * Get the path for the given media, relative to the root storage path.
     */
    public function getPath(Media $media): string
    {
        return $this->getBasePath($media) . '/';
    }

    /**
     * Get the path for conversions of the given media, relative to the root storage path.
     */
    public function getPathForConversions(Media $media): string
    {
        return $this->getBasePath($media) . '/conversions/';
    }

    /**
     * Get the path for responsive images of the given media, relative to the root storage path.
     */
    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getBasePath($media) . '/responsive-images/';
    }

    /**
     * Get the base path for the media based on team and collection.
     */
    protected function getBasePath(Media $media): string
    {
        // Get team_id from the media record
        $teamId = $media->getCustomProperty('team_id') ?? $media->team_id ?? null;
        
        // If no team_id, use default structure
        if (!$teamId) {
            return $media->id;
        }

        // Determine subfolder based on collection name
        $subfolder = $this->getSubfolderFromCollection($media->collection_name);
        
        // Create team-based path: teams/{team_id}/{subfolder}/{media_id}
        return "teams/{$teamId}/{$subfolder}/{$media->id}";
    }

    /**
     * Determine the subfolder based on collection name.
     */
    protected function getSubfolderFromCollection(string $collectionName): string
    {
        // Map collection names to subfolders
        $collectionMap = [
            'product-images' => 'Products',
            'digital-files' => 'Products',
            'avatars' => 'Avatars',
            'profile-images' => 'Avatars',
            'user_avatars' => 'Avatars',
            'documents' => 'Documents',
            'files' => 'Documents',
            'media' => 'Media',
            'images' => 'Media',
            'default' => 'Media',
        ];

        // Check for exact match first
        if (isset($collectionMap[$collectionName])) {
            return $collectionMap[$collectionName];
        }

        // Check for partial matches
        foreach ($collectionMap as $pattern => $folder) {
            if (str_contains($collectionName, $pattern)) {
                return $folder;
            }
        }

        // Check for team-specific collections (e.g., team_1_products)
        if (preg_match('/team_\d+_(.+)/', $collectionName, $matches)) {
            $type = $matches[1];
            switch ($type) {
                case 'products':
                    return 'Products';
                case 'avatars':
                    return 'Avatars';
                case 'documents':
                    return 'Documents';
                case 'media':
                    return 'Media';
                default:
                    return 'Media';
            }
        }

        // Check for personal collections (e.g., user_123_personal)
        if (str_contains($collectionName, 'personal')) {
            return 'Personal';
        }

        // Default fallback
        return 'Media';
    }
}
