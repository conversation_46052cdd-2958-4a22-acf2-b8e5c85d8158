<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;

class CheckRoutes extends Command
{
    protected $signature = 'debug:routes {pattern?}';
    protected $description = 'Check available routes, optionally filter by pattern';

    public function handle()
    {
        $pattern = $this->argument('pattern');
        $routes = Route::getRoutes();

        $this->info("=== Available Routes ===");
        
        $adminRoutes = [];
        $tenantRoutes = [];
        $otherRoutes = [];

        foreach ($routes as $route) {
            $name = $route->getName();
            $uri = $route->uri();
            
            if (!$name) continue;
            
            if ($pattern && !str_contains($name, $pattern) && !str_contains($uri, $pattern)) {
                continue;
            }

            if (str_contains($name, 'filament.admin')) {
                $adminRoutes[] = ['name' => $name, 'uri' => $uri];
            } elseif (str_contains($uri, '{tenant}')) {
                $tenantRoutes[] = ['name' => $name, 'uri' => $uri];
            } else {
                $otherRoutes[] = ['name' => $name, 'uri' => $uri];
            }
        }

        if (!empty($adminRoutes)) {
            $this->info("\n=== Admin Routes ===");
            foreach ($adminRoutes as $route) {
                $this->line("{$route['name']} -> {$route['uri']}");
            }
        }

        if (!empty($tenantRoutes)) {
            $this->info("\n=== Tenant Routes ===");
            foreach ($tenantRoutes as $route) {
                $this->line("{$route['name']} -> {$route['uri']}");
            }
        }

        if (!empty($otherRoutes) && count($otherRoutes) < 20) {
            $this->info("\n=== Other Routes ===");
            foreach ($otherRoutes as $route) {
                $this->line("{$route['name']} -> {$route['uri']}");
            }
        }

        $this->info("\nTotal routes found: " . (count($adminRoutes) + count($tenantRoutes) + count($otherRoutes)));
        
        // Check specific admin URLs
        $this->info("\n=== Testing Admin URLs ===");
        $testUrls = [
            '/admin',
            '/admin/login',
            '/admin/dashboard',
        ];
        
        foreach ($testUrls as $url) {
            try {
                $route = Route::getRoutes()->match(
                    \Illuminate\Http\Request::create($url, 'GET')
                );
                $this->info("✅ {$url} -> {$route->getName()}");
            } catch (\Exception $e) {
                $this->error("❌ {$url} -> " . $e->getMessage());
            }
        }

        return 0;
    }
}
