<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /**
     * Handle a login request for the application.
     */
    public function authenticate(Request $request)
    {
        $credentials = $request->validate([
            'email' => ['required', 'email'],
            'password' => ['required'],
        ]);

        $remember = $request->boolean('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            $user = Auth::user();

            // Check if user needs to complete profile
            if ($user->needsProfileCompletion()) {
                return redirect()->route('profile.edit')
                    ->with('info', 'Please complete your profile to continue.');
            }

            // Redirect to appropriate dashboard
            return redirect($user->getDashboardRoute())
                ->with('success', 'Welcome back!');
        }

        throw ValidationException::withMessages([
            'email' => ['The provided credentials do not match our records.'],
        ]);
    }

    /**
     * Handle login via API (for AJAX requests)
     */
    public function apiAuthenticate(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => ['required', 'email'],
                'password' => ['required'],
            ]);

            $remember = $request->boolean('remember');

            if (Auth::attempt($credentials, $remember)) {
                $request->session()->regenerate();

                $user = Auth::user();

                // Check if user needs to complete profile
                if ($user->needsProfileCompletion()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Login successful! Please complete your profile.',
                        'redirect' => route('profile.edit')
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Welcome back!',
                    'redirect' => $user->getDashboardRoute()
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'The provided credentials do not match our records.',
                'errors' => ['email' => ['The provided credentials do not match our records.']]
            ], 422);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed.',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Login failed. Please try again.'
            ], 500);
        }
    }
}
