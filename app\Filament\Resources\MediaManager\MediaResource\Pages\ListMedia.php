<?php

namespace App\Filament\Resources\MediaManager\MediaResource\Pages;

use App\Models\User;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use TomatoPHP\FilamentIcons\Components\IconPicker;
use TomatoPHP\FilamentMediaManager\Models\Folder;
use TomatoPHP\FilamentMediaManager\Models\Media;
use App\Filament\Resources\MediaManager\Actions\CreateMediaAction;
use App\Filament\Resources\MediaManager\Actions\CreateSubFolderAction;
use App\Filament\Resources\MediaManager\Actions\DeleteFolderAction;
use App\Filament\Resources\MediaManager\Actions\EditCurrentFolderAction;
use App\Filament\Resources\MediaManager\MediaResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMedia extends ManageRecords
{
    protected static string $resource = MediaResource::class;
    protected static string $view = 'filament-media-manager::pages.list-records';
    // protected static string $view = 'vendor.filament-media-manager.components.list-records'; 
    public ?int $folder_id = null;
    public ?Folder $folder = null;


    public function getTitle(): string|Htmlable
    {
        return $this->folder->name; // TODO: Change the autogenerated stub
    }

    public function mount(): void
    {
        parent::mount(); // TODO: Change the autogenerated stub


        if(!request()->has('folder_id')){
            abort(404, 'Folder ID is required');
        }

        $folder = Folder::find(request()->get('folder_id'));
        if(!$folder){
            abort(404, 'Folder ID is required');
        }
        else {
            if($folder->is_protected && !session()->has('folder_password')){
                abort(403, 'You Cannot Access This Folder');
            }
        }

        $this->folder = $folder;
        $this->folder_id = request()->get('folder_id');
        session()->put('folder_id', $this->folder_id);
    }

    /**
     * Force show actions for debugging - you can call this method to bypass access control
     */
    public function forceShowActions(): array
    {
        $folder_id = $this->folder_id;

        return [
            CreateMediaAction::make($folder_id),
            CreateSubFolderAction::make($folder_id),
            DeleteFolderAction::make($folder_id),
            EditCurrentFolderAction::make($folder_id),
        ];
    }

    /**
     * Override getHeaderActions to temporarily bypass access control for debugging
     * Uncomment the return statement below to force show all actions
     */
    // protected function getHeaderActions(): array
    // {
    //     // TEMPORARY FIX: Uncomment this line to force show actions
    //     // return $this->forceShowActions();
    //
    //     // Call the original method
    //     return parent::getHeaderActions();
    // }

    protected function getHeaderActions(): array
    {
        $folder_id = $this->folder_id;
        $folder = config('filament-media-manager.model.folder')::find($folder_id);
        $user = auth()->user();

        // Check debug configuration
        $debugConfig = config('media-manager-debug', []);

        // Force show actions if debug config is set
        if ($debugConfig['force_show_actions'] ?? false) {
            return $this->forceShowActions();
        }
 
        // Check if user access control is disabled via debug config
        if ($debugConfig['disable_user_access_control'] ?? false) {
            return $this->forceShowActions();
        }

        // Check if user access control is enabled
        $allowUserAccess = config('filament-media-manager.allow_user_access', false);

        // If user access control is disabled, always show actions
        if (!$allowUserAccess) {
            return [
                CreateMediaAction::make($folder_id),
                CreateSubFolderAction::make($folder_id),
                DeleteFolderAction::make($folder_id),
                EditCurrentFolderAction::make($folder_id),
            ];
        }

        // If user access control is enabled, check permissions
        if ($allowUserAccess) {
            // Super admins should always have access to all folders
            if ($user->hasRole('super_admin')) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // Team admins can access folders in their team
            if ($user->hasRole('team_admin')) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // If folder has no user_id (shared/public folder), allow access
            if (empty($folder->user_id)) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // If folder is public, allow access
            if (isset($folder->is_public) && $folder->is_public) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // If user owns the folder, allow access
            if ($folder->user_id === $user->id && $folder->user_type === get_class($user)) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // If folder belongs to the same team, allow access
            if (isset($folder->team_id) && $folder->team_id === $user->team_id) {
                return [
                    CreateMediaAction::make($folder_id),
                    CreateSubFolderAction::make($folder_id),
                    DeleteFolderAction::make($folder_id),
                    EditCurrentFolderAction::make($folder_id),
                ];
            }

            // User doesn't have access to this folder
            return [];
        }

        // Default fallback - show actions
        return [
            CreateMediaAction::make($folder_id),
            CreateSubFolderAction::make($folder_id),
            DeleteFolderAction::make($folder_id),
            EditCurrentFolderAction::make($folder_id),
        ];
    }

    public function folderAction(?Folder $item=null){
        return Actions\Action::make('folderAction')
            ->requiresConfirmation(function (array $arguments){
                if($arguments['record']['is_protected']){
                    return true;
                }
                else {
                    return false;
                }
            })
            ->form(function (array $arguments){
                if($arguments['record']['is_protected']){
                    return [
                        TextInput::make('password')
                            ->password()
                            ->revealable()
                            ->required()
                            ->maxLength(255),
                    ];
                }
                else {
                    return null;
                }
            })
            ->action(function (array $arguments, array $data){
                if($arguments['record']['is_protected']){
                    if($arguments['record']['password'] != $data['password']){
                        Notification::make()
                            ->title('Password is incorrect')
                            ->danger()
                            ->send();

                        return ;
                    }
                    else {
                        session()->put('folder_password', $data['password']);
                    }
                }
                if(!$arguments['record']['model_type']){
                    // Use Filament's URL generation for MediaResource
                    $url = \App\Filament\Resources\MediaManager\MediaResource::getUrl('index', ['folder_id' => $arguments['record']['id']]);
                    return redirect()->to($url);
                }
                if(!$arguments['record']['model_id'] && !$arguments['record']['collection']){
                    // Use Filament's URL generation for FolderResource
                    $url = \App\Filament\Resources\MediaManager\FolderResource::getUrl('index', ['model_type' => $arguments['record']['model_type']]);
                    return redirect()->to($url);
                }
                else if(!$arguments['record']['model_id']){
                    // Use Filament's URL generation for FolderResource
                    $url = \App\Filament\Resources\MediaManager\FolderResource::getUrl('index', [
                        'model_type' => $arguments['record']['model_type'],
                        'collection' => $arguments['record']['collection']
                    ]);
                    return redirect()->to($url);
                }
                else {
                    // Use Filament's URL generation for MediaResource
                    $url = \App\Filament\Resources\MediaManager\MediaResource::getUrl('index', ['folder_id' => $arguments['record']['id']]);
                    return redirect()->to($url);
                }
            })
            ->view('filament-media-manager::pages.folder-action', ['item' => $item]);
    }


    public function deleteMedia()
    {
        return Actions\Action::make('deleteMedia')
            ->label(trans('filament-media-manager::messages.media.meta.delete-media'))
            ->icon('heroicon-s-trash')
            ->color('danger')
            ->requiresConfirmation()
            ->action(function (array $arguments) {
                $media = Media::find($arguments['record']['id']);
                $media->delete();

                Notification::make()
                    ->title(trans('filament-media-manager::messages.media.notifications.delete-folder'))
                    ->success()
                    ->send();
            });

    }
}
