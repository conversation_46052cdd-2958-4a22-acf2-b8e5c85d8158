<!DOCTYPE html>
<!-- saved from url=(0140)https://bd7w21mj77xnvae2.canva-hosted-embed.com/codelet/AAEAEGJkN3cyMW1qNzd4bnZhZTIAAAAAAZdOeCyUpKTL3iiCo9xMnw5S17paXQCWcH0GD-fwLpTJKv2-OSI/ -->
<html lang="th"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>มอบหมายงานให้นักเรียน</title>
    <script src="./saved_resource"></script>
    <link href="./css2" rel="stylesheet">
    <link rel="stylesheet" href="./all.min.css">
    <style>
        * {
            font-family: 'Prompt', sans-serif;
        }
        
        .card-shadow {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
        }
        
        .activity-card {
            transition: all 0.3s ease;
        }
        
        .activity-card:hover {
            transform: translateY(-5px);
        }
        
        .checkbox-item:checked + label {
            background-color: #EEF2FF;
            border-color: #4F46E5;
        }
        
        .checkbox-item:checked + label .check-icon {
            display: flex;
        }
        
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #E5E7EB;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.absolute{position:absolute}.relative{position:relative}.z-10{z-index:10}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mr-1{margin-right:0.25rem}.mr-2{margin-right:0.5rem}.mr-3{margin-right:0.75rem}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-6{height:1.5rem}.h-full{height:100%}.min-h-screen{min-height:100vh}.w-1\/4{width:25%}.w-10{width:2.5rem}.w-3\/4{width:75%}.w-6{width:1.5rem}.w-full{width:100%}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-3{gap:0.75rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.overflow-hidden{overflow:hidden}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-md{border-radius:0.375rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-2{border-width:2px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-gray-300{--tw-border-opacity:1;border-color:rgb(209 213 219 / var(--tw-border-opacity, 1))}.border-indigo-500{--tw-border-opacity:1;border-color:rgb(99 102 241 / var(--tw-border-opacity, 1))}.bg-blue-100{--tw-bg-opacity:1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1))}.bg-gray-100{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-gray-600{--tw-bg-opacity:1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.bg-green-100{--tw-bg-opacity:1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.bg-indigo-100{--tw-bg-opacity:1;background-color:rgb(224 231 255 / var(--tw-bg-opacity, 1))}.bg-indigo-50{--tw-bg-opacity:1;background-color:rgb(238 242 255 / var(--tw-bg-opacity, 1))}.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229 / var(--tw-bg-opacity, 1))}.bg-purple-100{--tw-bg-opacity:1;background-color:rgb(243 232 255 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-purple-600{--tw-bg-opacity:1;background-color:rgb(147 51 234 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/30{background-color:rgb(255 255 255 / 0.3)}.p-3{padding:0.75rem}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-2{padding-left:0.5rem;padding-right:0.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-1\.5{padding-top:0.375rem;padding-bottom:0.375rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.pt-3{padding-top:0.75rem}.text-2xl{font-size:1.5rem;line-height:2rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1))}.text-blue-700{--tw-text-opacity:1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.text-blue-800{--tw-text-opacity:1;color:rgb(30 64 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-green-700{--tw-text-opacity:1;color:rgb(21 128 61 / var(--tw-text-opacity, 1))}.text-indigo-500{--tw-text-opacity:1;color:rgb(99 102 241 / var(--tw-text-opacity, 1))}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.text-indigo-700{--tw-text-opacity:1;color:rgb(67 56 202 / var(--tw-text-opacity, 1))}.text-indigo-800{--tw-text-opacity:1;color:rgb(55 48 163 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-purple-700{--tw-text-opacity:1;color:rgb(126 34 206 / var(--tw-text-opacity, 1))}.text-purple-800{--tw-text-opacity:1;color:rgb(107 33 168 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.opacity-0{opacity:0}.shadow-md{--tw-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.hover\:bg-gray-300:hover{--tw-bg-opacity:1;background-color:rgb(209 213 219 / var(--tw-bg-opacity, 1))}.hover\:bg-gray-700:hover{--tw-bg-opacity:1;background-color:rgb(55 65 81 / var(--tw-bg-opacity, 1))}.hover\:bg-green-200:hover{--tw-bg-opacity:1;background-color:rgb(187 247 208 / var(--tw-bg-opacity, 1))}.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202 / var(--tw-bg-opacity, 1))}.hover\:bg-purple-700:hover{--tw-bg-opacity:1;background-color:rgb(126 34 206 / var(--tw-bg-opacity, 1))}.hover\:opacity-90:hover{opacity:0.9}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:ring-2:focus{--tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)}.focus\:ring-indigo-500:focus{--tw-ring-opacity:1;--tw-ring-color:rgb(99 102 241 / var(--tw-ring-opacity, 1))}@media (min-width: 768px){.md\:inline-block{display:inline-block}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:col-span-1{grid-column:span 1 / span 1}.lg\:col-span-2{grid-column:span 2 / span 2}.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}</style></head>
<body class="bg-gray-50">
    <div class="min-h-screen"> 
        <div class="container mx-auto px-4 py-6">
            <!-- Main Content -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Form Section -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-xl p-6 card-shadow">
                        <h2 class="text-xl font-bold mb-6 text-gray-800 flex items-center">
                            <i class="fas fa-tasks mr-2 text-indigo-600"></i>
                            มอบหมายกิจกรรมใหม่
                        </h2>
                        
                        <form id="assignmentForm">
                            <!-- Teaching Date -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="far fa-calendar-alt mr-1 text-indigo-500"></i>
                                    วันที่สอน
                                </label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" required="">
                            </div>
                            
                            <!-- Due Date -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="far fa-calendar-check mr-1 text-indigo-500"></i>
                                    วันกำหนดส่งงาน
                                </label>
                                <div class="flex space-x-2">
                                    <input type="date" class="w-3/4 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" required="">
                                    <select class="w-1/4 px-2 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" fdprocessedid="qv8w5e">
                                        <option>16:00</option>
                                        <option>17:00</option>
                                        <option>18:00</option>
                                        <option>19:00</option>
                                        <option>20:00</option>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Class Selection -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="fas fa-users mr-1 text-indigo-500"></i>
                                    ระดับชั้น / ห้องเรียน
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="classSelect" required="" fdprocessedid="rewy49o">
                                    <option value="" disabled="" selected="">เลือกระดับชั้น / ห้องเรียน</option>
                                    <option value="ป.5/1">ป.5/1</option>
                                    <option value="ป.5/2">ป.5/2</option>
                                    <option value="ป.6/1">ป.6/1</option>
                                    <option value="ม.1/1">ม.1/1</option>
                                    <option value="ม.2/2">ม.2/2</option>
                                </select>
                            </div>
                            
                            <!-- Subject Selection -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="fas fa-book mr-1 text-indigo-500"></i>
                                    วิชาที่สอน
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="subjectSelect" required="" fdprocessedid="tsfyoq">
                                    <option value="" disabled="" selected="">เลือกวิชา</option>
                                    <option value="ภาษาไทย">ภาษาไทย</option>
                                    <option value="คณิตศาสตร์">คณิตศาสตร์</option>
                                    <option value="วิทยาศาสตร์">วิทยาศาสตร์</option>
                                    <option value="สังคมศึกษา">สังคมศึกษา</option>
                                    <option value="ภาษาอังกฤษ">ภาษาอังกฤษ</option>
                                </select>
                            </div>
                            
                            <!-- Book Selection -->
                            <div class="mb-4">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="fas fa-book-open mr-1 text-indigo-500"></i>
                                    หนังสือ
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="bookSelect" fdprocessedid="fo6f6c">
                                    <option value="ไม่ระบุ">ไม่ระบุ</option>
                                    <option value="หนังสือเรียนคณิตศาสตร์ ป.5">หนังสือเรียนคณิตศาสตร์ ป.5</option>
                                    <option value="หนังสือเรียนภาษาไทย ป.5">หนังสือเรียนภาษาไทย ป.5</option>
                                    <option value="หนังสือเรียนวิทยาศาสตร์ ป.5">หนังสือเรียนวิทยาศาสตร์ ป.5</option>
                                </select>
                            </div>
                            
                            <!-- Lesson Selection (conditional) -->
                            <div class="mb-4" id="lessonSelectContainer" style="display: none;">
                                <label class="block text-gray-700 text-sm font-medium mb-2">
                                    <i class="fas fa-bookmark mr-1 text-indigo-500"></i>
                                    บทเรียน
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="lessonSelect">
                                    <option value="" disabled="" selected="">เลือกบทเรียน</option>
                                    <option value="บทที่ 1">บทที่ 1 - พื้นฐานการคำนวณ</option>
                                    <option value="บทที่ 2">บทที่ 2 - เศษส่วนและทศนิยม</option>
                                    <option value="บทที่ 3">บทที่ 3 - รูปเรขาคณิต</option>
                                    <option value="บทที่ 4">บทที่ 4 - การวัด</option>
                                </select>
                            </div>
                            
                            <!-- Activity Selection -->
                            <div class="mb-6">
                                <label class="block text-gray-700 text-sm font-medium mb-3">
                                    <i class="fas fa-clipboard-list mr-1 text-indigo-500"></i>
                                    กิจกรรมที่ต้องการมอบหมาย
                                </label>
                                
                                <div class="grid grid-cols-1 gap-3">
                                    <!-- Activity 1 -->
                                    <div class="relative">
                                        <input type="checkbox" id="activity1" class="checkbox-item absolute opacity-0 w-full h-full cursor-pointer z-10">
                                        <label for="activity1" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer">
                                            <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                                <div class="check-icon hidden text-indigo-500">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="font-medium">แบบทดสอบที่ครูสร้างเอง</span>
                                            </div>
                                        </label>
                                    </div>
                                    
                                    <!-- Activity 2 -->
                                    <div class="relative">
                                        <input type="checkbox" id="activity2" class="checkbox-item absolute opacity-0 w-full h-full cursor-pointer z-10">
                                        <label for="activity2" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer">
                                            <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                                <div class="check-icon hidden text-indigo-500">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="font-medium">แบบฝึกหัดจาก EduNest</span>
                                            </div>
                                        </label>
                                    </div>
                                    
                                    <!-- Activity 3 -->
                                    <div class="relative">
                                        <input type="checkbox" id="activity3" class="checkbox-item absolute opacity-0 w-full h-full cursor-pointer z-10">
                                        <label for="activity3" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer">
                                            <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                                <div class="check-icon hidden text-indigo-500">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="font-medium">เล่นเกมฝึกทักษะ</span>
                                            </div>
                                        </label>
                                    </div>
                                    
                                    <!-- Activity 4 -->
                                    <div class="relative">
                                        <input type="checkbox" id="activity4" class="checkbox-item absolute opacity-0 w-full h-full cursor-pointer z-10">
                                        <label for="activity4" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer">
                                            <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                                <div class="check-icon hidden text-indigo-500">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="font-medium">ดูวิดีโอการสอน</span>
                                            </div>
                                        </label>
                                    </div>
                                    
                                    <!-- Activity 5 -->
                                    <div class="relative">
                                        <input type="checkbox" id="activity5" class="checkbox-item absolute opacity-0 w-full h-full cursor-pointer z-10">
                                        <label for="activity5" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer">
                                            <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                                <div class="check-icon hidden text-indigo-500">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="font-medium">แบบทดสอบท้ายบท</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="flex justify-center">
                                <button type="submit" class="gradient-bg text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-all flex items-center" fdprocessedid="90tlz9">
                                    <i class="fas fa-paper-plane mr-2"></i>
                                    มอบหมายกิจกรรม
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Assigned Activities Section -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl p-6 card-shadow">
                        <h2 class="text-xl font-bold mb-6 text-gray-800 flex items-center">
                            <i class="fas fa-clipboard-check mr-2 text-indigo-600"></i>
                            กิจกรรมที่มอบหมายแล้ว
                        </h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Activity Card 1 -->
                            <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                                <div class="bg-indigo-100 px-4 py-3 border-b border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-indigo-800">ป.5/1 - วิชาคณิตศาสตร์</h3>
                                        <span class="bg-indigo-600 text-white text-xs px-2 py-1 rounded-full">กำลังดำเนินการ</span>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="flex items-center mb-2">
                                        <i class="far fa-calendar-alt text-indigo-500 mr-2"></i>
                                        <span class="text-sm">วันที่สอน: 4 มิ.ย. 2566</span>
                                    </div>
                                    
                                    <div class="flex items-center mb-3">
                                        <i class="far fa-calendar-check text-indigo-500 mr-2"></i>
                                        <span class="text-sm">กำหนดส่ง: 6 มิ.ย. 2566 (16:00 น.)</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="bg-indigo-50 text-indigo-700 text-xs px-2 py-1 rounded-full">แบบฝึกหัดจาก EduNest</span>
                                            <span class="bg-indigo-50 text-indigo-700 text-xs px-2 py-1 rounded-full">เกมฝึกทักษะ</span>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t border-gray-100 pt-3 mb-3">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium">
                                                <i class="fas fa-users text-indigo-500 mr-1"></i>
                                                จำนวนนักเรียนในห้อง: 38 คน
                                            </span>
                                            <span class="text-sm font-medium text-indigo-600">ส่งแล้ว: 22 คน</span>
                                        </div>
                                        
                                        <div class="progress-bar mb-2">
                                            <div class="progress-fill" style="width: 58%"></div>
                                        </div>
                                        
                                        <div class="text-sm text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>
                                            คะแนนไม่ผ่านเกณฑ์: 4 คน
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-2">
                                        <button class="bg-indigo-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-indigo-700 transition-colors flex items-center" fdprocessedid="9sgcip">
                                            <i class="fas fa-eye mr-1"></i>
                                            ดูรายละเอียด
                                        </button>
                                        <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center" fdprocessedid="q2ak3r">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            ปิดกิจกรรม
                                        </button>
                                        <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center" fdprocessedid="fcurly">
                                            <i class="fas fa-download mr-1"></i>
                                            เก็บข้อมูล
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Activity Card 2 -->
                            <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                                <div class="bg-blue-100 px-4 py-3 border-b border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-blue-800">ม.2/2 - วิชาวิทยาศาสตร์</h3>
                                        <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">กำลังดำเนินการ</span>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="flex items-center mb-2">
                                        <i class="far fa-calendar-alt text-blue-500 mr-2"></i>
                                        <span class="text-sm">วันที่สอน: 3 มิ.ย. 2566</span>
                                    </div>
                                    
                                    <div class="flex items-center mb-3">
                                        <i class="far fa-calendar-check text-blue-500 mr-2"></i>
                                        <span class="text-sm">กำหนดส่ง: 7 มิ.ย. 2566 (18:00 น.)</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full">แบบทดสอบที่ครูสร้างเอง</span>
                                            <span class="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full">ดูวิดีโอการสอน</span>
                                            <span class="bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full">แบบทดสอบท้ายบท</span>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t border-gray-100 pt-3 mb-3">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium">
                                                <i class="fas fa-users text-blue-500 mr-1"></i>
                                                จำนวนนักเรียนในห้อง: 42 คน
                                            </span>
                                            <span class="text-sm font-medium text-blue-600">ส่งแล้ว: 35 คน</span>
                                        </div>
                                        
                                        <div class="progress-bar mb-2">
                                            <div class="progress-fill" style="width: 83%; background: linear-gradient(90deg, #2563EB 0%, #3B82F6 100%);"></div>
                                        </div>
                                        
                                        <div class="text-sm text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>
                                            คะแนนไม่ผ่านเกณฑ์: 2 คน
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-2">
                                        <button class="bg-blue-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-colors flex items-center" fdprocessedid="ntiu7c">
                                            <i class="fas fa-eye mr-1"></i>
                                            ดูรายละเอียด
                                        </button>
                                        <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center" fdprocessedid="p51wlq">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            ปิดกิจกรรม
                                        </button>
                                        <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center" fdprocessedid="6sxvzg">
                                            <i class="fas fa-download mr-1"></i>
                                            เก็บข้อมูล
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Activity Card 3 -->
                            <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                                <div class="bg-purple-100 px-4 py-3 border-b border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-purple-800">ป.6/1 - วิชาภาษาไทย</h3>
                                        <span class="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">กำลังดำเนินการ</span>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="flex items-center mb-2">
                                        <i class="far fa-calendar-alt text-purple-500 mr-2"></i>
                                        <span class="text-sm">วันที่สอน: 2 มิ.ย. 2566</span>
                                    </div>
                                    
                                    <div class="flex items-center mb-3">
                                        <i class="far fa-calendar-check text-purple-500 mr-2"></i>
                                        <span class="text-sm">กำหนดส่ง: 5 มิ.ย. 2566 (17:00 น.)</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="bg-purple-50 text-purple-700 text-xs px-2 py-1 rounded-full">แบบฝึกหัดจาก EduNest</span>
                                            <span class="bg-purple-50 text-purple-700 text-xs px-2 py-1 rounded-full">แบบทดสอบท้ายบท</span>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t border-gray-100 pt-3 mb-3">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium">
                                                <i class="fas fa-users text-purple-500 mr-1"></i>
                                                จำนวนนักเรียนในห้อง: 40 คน
                                            </span>
                                            <span class="text-sm font-medium text-purple-600">ส่งแล้ว: 28 คน</span>
                                        </div>
                                        
                                        <div class="progress-bar mb-2">
                                            <div class="progress-fill" style="width: 70%; background: linear-gradient(90deg, #7C3AED 0%, #8B5CF6 100%);"></div>
                                        </div>
                                        
                                        <div class="text-sm text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>
                                            คะแนนไม่ผ่านเกณฑ์: 5 คน
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-2">
                                        <button class="bg-purple-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-purple-700 transition-colors flex items-center" fdprocessedid="7bv7ug">
                                            <i class="fas fa-eye mr-1"></i>
                                            ดูรายละเอียด
                                        </button>
                                        <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center" fdprocessedid="pqmt4s">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            ปิดกิจกรรม
                                        </button>
                                        <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center" fdprocessedid="etpfeb">
                                            <i class="fas fa-download mr-1"></i>
                                            เก็บข้อมูล
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Activity Card 4 -->
                            <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                                <div class="bg-gray-100 px-4 py-3 border-b border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-gray-800">ป.5/2 - วิชาภาษาอังกฤษ</h3>
                                        <span class="bg-gray-600 text-white text-xs px-2 py-1 rounded-full">เสร็จสิ้น</span>
                                    </div>
                                </div>
                                
                                <div class="p-4">
                                    <div class="flex items-center mb-2">
                                        <i class="far fa-calendar-alt text-gray-500 mr-2"></i>
                                        <span class="text-sm">วันที่สอน: 1 มิ.ย. 2566</span>
                                    </div>
                                    
                                    <div class="flex items-center mb-3">
                                        <i class="far fa-calendar-check text-gray-500 mr-2"></i>
                                        <span class="text-sm">กำหนดส่ง: 3 มิ.ย. 2566 (16:00 น.)</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="bg-gray-50 text-gray-700 text-xs px-2 py-1 rounded-full">เกมฝึกทักษะ</span>
                                            <span class="bg-gray-50 text-gray-700 text-xs px-2 py-1 rounded-full">ดูวิดีโอการสอน</span>
                                        </div>
                                    </div>
                                    
                                    <div class="border-t border-gray-100 pt-3 mb-3">
                                        <div class="flex items-center justify-between mb-1">
                                            <span class="text-sm font-medium">
                                                <i class="fas fa-users text-gray-500 mr-1"></i>
                                                จำนวนนักเรียนในห้อง: 36 คน
                                            </span>
                                            <span class="text-sm font-medium text-gray-600">ส่งแล้ว: 36 คน</span>
                                        </div>
                                        
                                        <div class="progress-bar mb-2">
                                            <div class="progress-fill" style="width: 100%; background: linear-gradient(90deg, #4B5563 0%, #6B7280 100%);"></div>
                                        </div>
                                        
                                        <div class="text-sm text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle mr-1"></i>
                                            คะแนนไม่ผ่านเกณฑ์: 3 คน
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-wrap gap-2">
                                        <button class="bg-gray-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-gray-700 transition-colors flex items-center" fdprocessedid="qyk5zp">
                                            <i class="fas fa-eye mr-1"></i>
                                            ดูรายละเอียด
                                        </button>
                                        <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center" fdprocessedid="m9l6k">
                                            <i class="fas fa-times-circle mr-1"></i>
                                            ปิดกิจกรรม
                                        </button>
                                        <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center" fdprocessedid="tql35">
                                            <i class="fas fa-download mr-1"></i>
                                            เก็บข้อมูล
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Show/hide lesson select based on book selection
        document.getElementById('bookSelect').addEventListener('change', function() {
            const lessonContainer = document.getElementById('lessonSelectContainer');
            if (this.value !== 'ไม่ระบุ') {
                lessonContainer.style.display = 'block';
            } else {
                lessonContainer.style.display = 'none';
            }
        });
        
        // Form submission
        document.getElementById('assignmentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Check if at least one activity is selected
            const activities = document.querySelectorAll('.checkbox-item:checked');
            if (activities.length === 0) {
                alert('กรุณาเลือกกิจกรรมที่ต้องการมอบหมายอย่างน้อย 1 รายการ');
                return;
            }
            
            alert('มอบหมายกิจกรรมสำเร็จ!');
            // In a real application, you would submit the form data to a server here
        });
        
        // Dynamic subject and book options based on class selection
        document.getElementById('classSelect').addEventListener('change', function() {
            const classValue = this.value;
            const subjectSelect = document.getElementById('subjectSelect');
            const bookSelect = document.getElementById('bookSelect');
            
            // Reset selections
            subjectSelect.selectedIndex = 0;
            bookSelect.selectedIndex = 0;
            document.getElementById('lessonSelectContainer').style.display = 'none';
            
            // You could dynamically update options based on class selection here
            // This is a simplified example
        });
    </script>
 
<span id="PING_IFRAME_FORM_DETECTION" style="display: none;"></span></body></html>