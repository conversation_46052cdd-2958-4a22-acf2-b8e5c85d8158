<?php

namespace Database\Factories;

use App\Models\ClassRoom;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClassRoomFactory extends Factory
{
    protected $model = ClassRoom::class;

    public function definition(): array
    {
        $gradeLevel = $this->faker->randomElement(['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12']);
        
        return [
            'grade_level' => $gradeLevel,
            'room_number' => $this->faker->numberBetween(100, 999),
            'room_name' => "Grade {$gradeLevel} Classroom " . $this->faker->randomLetter(),
            'remark' => $this->faker->sentence(),
            'is_active' => true,
            'capacity' => $this->faker->numberBetween(20, 35),
        ];
    }
}
