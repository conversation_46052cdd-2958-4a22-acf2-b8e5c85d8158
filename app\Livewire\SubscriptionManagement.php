<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use TomatoPHP\FilamentSubscriptions\Models\Plan;
use TomatoPHP\FilamentSubscriptions\Models\Subscription;
use Filament\Notifications\Notification;

class SubscriptionManagement extends Component
{
    public $currentSubscription;
    public $availablePlans;
    public $selectedPlan;

    public function mount()
    {
        $user = Auth::user();
        $this->currentSubscription = $user->subscriptions()->active()->first();
        $this->availablePlans = Plan::where('is_active', true)->get();
    }

    public function selectPlan($planId)
    {
        $this->selectedPlan = Plan::find($planId);
    }

    public function subscribeToPlan()
    {
        if (!$this->selectedPlan) {
            Notification::make()
                ->title('Please select a plan')
                ->danger()
                ->send();
            return;
        }

        $user = Auth::user();

        // Cancel current subscription if exists
        if ($this->currentSubscription) {
            $this->currentSubscription->cancel();
        }

        // Create new subscription
        $subscription = $user->subscriptions()->create([
            'plan_id' => $this->selectedPlan->id,
            'name' => $this->selectedPlan->name,
            'starts_at' => now(),
            'ends_at' => now()->addDays($this->selectedPlan->interval_count),
            'trial_ends_at' => $this->selectedPlan->trial_period_days ? now()->addDays($this->selectedPlan->trial_period_days) : null,
        ]);

        // Redirect to payment if not free
        if ($this->selectedPlan->price > 0) {
            return redirect()->route('payment.checkout', ['subscription' => $subscription->id]);
        }

        $planName = $this->selectedPlan->name;
        $this->currentSubscription = $subscription;
        $this->selectedPlan = null;

        Notification::make()
            ->title('Successfully subscribed to ' . $planName)
            ->success()
            ->send();
    }

    public function cancelSubscription()
    {
        if ($this->currentSubscription) {
            $this->currentSubscription->cancel();
            $this->currentSubscription = null;

            Notification::make()
                ->title('Subscription cancelled successfully')
                ->warning()
                ->send();
        }
    }

    public function renewSubscription()
    {
        if ($this->currentSubscription && $this->currentSubscription->plan) {
            return redirect()->route('payment.checkout', [
                'subscription' => $this->currentSubscription->id,
                'action' => 'renew'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.subscription-management', [
            'currentSubscription' => $this->currentSubscription,
            'availablePlans' => $this->availablePlans,
            'selectedPlan' => $this->selectedPlan,
        ]);
    }
}
