# SQL Error and Menu Sorting Fixes

## Issues Fixed

### 1. SQL Error: Column 'digital_files' not found

#### Problem
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'digital_files' in 'where clause'
```

#### Root Cause
The DigitalProductStats widget was trying to query `digital_files` as if it were a database column, but `digital_files` is actually a SpatieMediaLibrary collection name, not a database field.

#### Solution
Changed the query from:
```php
// BEFORE (Problematic)
$withFiles = $query->whereNotNull('digital_files')->count();
```

To:
```php
// AFTER (Fixed)
$withFiles = $query->whereNotNull('file_size_mb')->count(); // Use file_size_mb as proxy for having files
```

#### Explanation
- `digital_files` is a media collection managed by Spatie Media Library
- `file_size_mb` is an actual database column that indicates if a product has digital files
- Using `file_size_mb` as a proxy is logical since products with files should have a file size

### 2. Menu Sorting Issue

#### Problem
The navigation menu items were not in logical order:
- Digital Products appeared before Physical Products
- Categories appeared before Brands

#### Solution
Updated navigation sort order:

```php
// Physical Products
protected static ?int $navigationSort = 0;

// Digital Products  
protected static ?int $navigationSort = 1;

// Brands
protected static ?int $navigationSort = 2;

// Categories
protected static ?int $navigationSort = 3;
```

#### Result
New menu order:
1. **Physical Products** (Sort: 0)
2. **Digital Products** (Sort: 1)
3. **Brands** (Sort: 2)
4. **Categories** (Sort: 3)

## Technical Details

### Database Schema
The `shop_products` table contains these digital-related columns:
- `product_type` - enum('physical', 'digital')
- `digital_description` - text
- `digital_file_types` - json
- `download_limit` - integer
- `download_expiry_days` - integer
- `requires_license_key` - boolean
- `license_terms` - text
- `file_size_mb` - decimal(8,2) ✅ **This is the actual column**
- `digital_format` - string
- `system_requirements` - json
- `version` - string
- `last_updated` - date

### Media Collections
Digital files are stored using Spatie Media Library:
- Collection name: `'digital-files'`
- Managed through: `SpatieMediaLibraryFileUpload::make('digital_files')`
- Not a database column, but a relationship

### Widget Logic
The DigitalProductStats widget now correctly uses:
- `file_size_mb` to determine if products have files
- Actual database columns for all queries
- No more references to non-existent columns

## Files Modified

### 1. DigitalProductStats Widget
**File:** `app/Filament/Clusters/Products/Resources/DigitalProductResource/Widgets/DigitalProductStats.php`

**Change:** Line 32
```php
// Before
$withFiles = $query->whereNotNull('digital_files')->count();

// After  
$withFiles = $query->whereNotNull('file_size_mb')->count(); // Use file_size_mb as proxy for having files
```

### 2. Navigation Sort Orders

**PhysicalProductResource.php:**
```php
protected static ?int $navigationSort = 0; // Changed from 1
```

**DigitalProductResource.php:**
```php
protected static ?int $navigationSort = 1; // Changed from 2
```

**BrandResource.php:**
```php
protected static ?int $navigationSort = 2; // Unchanged
```

**CategoryResource.php:**
```php
protected static ?int $navigationSort = 3; // Changed from 1
```

## Testing

### SQL Error Fix
✅ **Before:** Widget caused SQL error when loading
✅ **After:** Widget loads correctly and shows file statistics

### Menu Sorting Fix
✅ **Before:** Illogical menu order
✅ **After:** Logical progression from products → brands → categories

### Widget Functionality
The DigitalProductStats widget now correctly shows:
- Total digital products
- Average price
- License-protected products
- Download-limited products
- **Total file size** (using actual database column)
- **Products with files** (using file_size_mb as indicator)
- Top format statistics

## Best Practices

### Media Library vs Database Columns
- **Media Collections:** Use for file uploads (`digital_files`)
- **Database Columns:** Use for metadata (`file_size_mb`, `digital_format`)
- **Widget Queries:** Only query actual database columns

### Navigation Sorting
- Use logical progression: Products → Supporting Data
- Lower numbers appear first in navigation
- Group related resources together

### Error Prevention
- Always verify column existence before querying
- Use database columns for statistics, not media collections
- Test widgets after schema changes

## Impact

### User Experience
- ✅ No more SQL errors when viewing digital products
- ✅ Logical menu navigation order
- ✅ Accurate file statistics in widgets

### Development
- ✅ Cleaner separation between media and database concerns
- ✅ Consistent navigation patterns
- ✅ Reliable widget functionality

This fix ensures the digital products system works correctly while maintaining proper separation between database columns and media library collections.
