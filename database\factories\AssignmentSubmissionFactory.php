<?php

namespace Database\Factories;

use App\Models\AssignmentSubmission;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssignmentSubmissionFactory extends Factory
{
    protected $model = AssignmentSubmission::class;

    public function definition(): array
    {
        $status = $this->faker->randomElement(['draft', 'submitted', 'graded', 'returned']);
        $isGraded = in_array($status, ['graded', 'returned']);
        
        return [
            'score' => $isGraded ? $this->faker->numberBetween(60, 100) : null,
            'submitted_at' => $status !== 'draft' ? $this->faker->dateTimeBetween('-1 week', 'now') : null,
            'status' => $status,
            'content' => $this->faker->paragraphs(3, true),
            'file_path' => $this->faker->boolean(30) ? 'submissions/' . $this->faker->uuid() . '.pdf' : null,
            'feedback' => $isGraded ? $this->faker->paragraph() : null,
            'graded_at' => $isGraded ? $this->faker->dateTimeBetween('-3 days', 'now') : null,
        ];
    }
}
