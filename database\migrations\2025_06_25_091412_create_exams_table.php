<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exams', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->string('title');
            $table->text('description')->nullable();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // teacher
            $table->foreignId('subject_id')->nullable()->constrained('subjects')->nullOnDelete();
            $table->enum('type', ['exercise', 'exam'])->default('exercise');
            $table->integer('total_questions')->default(10); // จำนวนข้อที่ต้องสุ่ม
            $table->integer('time_limit')->nullable(); // เวลาทำข้อสอบ (นาที)
            $table->float('total_score')->default(100); // คะแนนเต็ม
            $table->boolean('randomize_questions')->default(true); // สุ่มข้อสอบ
            $table->boolean('randomize_choices')->default(true); // สุ่มตัวเลือก
            $table->boolean('show_results_immediately')->default(false); // แสดงผลทันที
            $table->boolean('allow_multiple_attempts')->default(false); // ทำได้หลายครั้ง
            $table->datetime('available_from')->nullable(); // เปิดให้ทำตั้งแต่
            $table->datetime('available_until')->nullable(); // ปิดรับ
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['team_id', 'type']);
            $table->index(['user_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exams');
    }
};
