<?php

namespace App\Filament\App\Widgets\School;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class TeacherStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();
        
        if (!$user || !($user->hasRole('team_admin') || $user->hasRole('school'))) {
            return [];
        }

        // Placeholder data - replace with actual teacher data
        return [
            Stat::make('Active Teachers', '25')
                ->description('Currently teaching')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('info'),

            Stat::make('Teacher Attendance', '96%')
                ->description('This week')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
        ];
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && ($user->hasRole('team_admin') || $user->hasRole('school'));
    }
}
