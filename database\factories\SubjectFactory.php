<?php

namespace Database\Factories;

use App\Models\Subject;
use Illuminate\Database\Eloquent\Factories\Factory;

class SubjectFactory extends Factory
{
    protected $model = Subject::class;

    public function definition(): array
    {
        $subjects = [
            ['name' => 'Mathematics', 'code' => 'MATH', 'color' => '#3B82F6'],
            ['name' => 'English Language Arts', 'code' => 'ELA', 'color' => '#10B981'],
            ['name' => 'Science', 'code' => 'SCI', 'color' => '#F59E0B'],
            ['name' => 'History', 'code' => 'HIST', 'color' => '#8B5CF6'],
            ['name' => 'Physical Education', 'code' => 'PE', 'color' => '#EF4444'],
        ];

        $subject = $this->faker->randomElement($subjects);

        return [
            'name' => $subject['name'],
            'code' => $subject['code'],
            'description' => "Comprehensive {$subject['name']} curriculum",
            'color' => $subject['color'],
            'credits' => $this->faker->numberBetween(1, 4),
            'level' => $this->faker->randomElement(['elementary', 'middle', 'high', 'university']),
            'grade_levels' => $this->faker->randomElements(['K', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'], $this->faker->numberBetween(1, 4)),
            'is_active' => true,
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }
}
