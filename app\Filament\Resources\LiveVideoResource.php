<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LiveVideoResource\Pages;
use App\Models\LiveVideo;
use App\Models\Course;
use App\Models\Book;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;

class LiveVideoResource extends Resource
{
    protected static ?string $model = LiveVideo::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 7;

    protected static ?string $navigationLabel = 'Live Videos';

    protected static ?string $modelLabel = 'Live Video';

    protected static ?string $pluralModelLabel = 'Live Videos';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'liveVideos';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('liveable_type')
                            ->label('Parent Type')
                            ->options([
                                Course::class => 'Course',
                                Book::class => 'Book',
                            ])
                            ->required()
                            ->reactive()
                            ->columnSpan(1),

                        Forms\Components\Select::make('liveable_id')
                            ->label('Parent')
                            ->options(function (callable $get) {
                                $type = $get('liveable_type');
                                if (!$type) {
                                    return [];
                                }

                                $query = $type::query();

                                // Apply team filtering
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }

                                return $query->pluck('title', 'id')->toArray();
                            })
                            ->required()
                            ->reactive()
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),

                        Forms\Components\Textarea::make('description')
                            ->columnSpanFull(),

                        Forms\Components\Select::make('user_id')
                            ->label('Teacher')
                            ->relationship('user', 'name')
                            ->required()
                            ->columnSpan(1),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Schedule')
                    ->schema([
                        Forms\Components\DateTimePicker::make('scheduled_start_time')
                            ->label('Scheduled Start')
                            ->required()
                            ->columnSpan(1),

                        Forms\Components\DateTimePicker::make('scheduled_end_time')
                            ->label('Scheduled End')
                            ->required()
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Streaming Settings')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                'scheduled' => 'Scheduled',
                                'live' => 'Live',
                                'ended' => 'Ended',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('scheduled')
                            ->required()
                            ->columnSpan(1),

                        Forms\Components\Toggle::make('is_recording_enabled')
                            ->label('Enable Recording')
                            ->default(true)
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('stream_key')
                            ->label('Stream Key')
                            ->disabled()
                            ->dehydrated(false)
                            ->columnSpan(2),

                        Forms\Components\TextInput::make('stream_url')
                            ->label('Stream URL')
                            ->url()
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('watch_url')
                            ->label('Watch URL')
                            ->url()
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Access Control')
                    ->schema([
                        Forms\Components\Toggle::make('is_public')
                            ->label('Public Access')
                            ->default(false)
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('access_password')
                            ->label('Access Password')
                            ->password()
                            ->maxLength(255)
                            ->columnSpan(1),

                        Forms\Components\TagsInput::make('allowed_roles')
                            ->label('Allowed Roles')
                            ->placeholder('Enter roles (e.g., student, teacher)')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Statistics')
                    ->schema([
                        Forms\Components\TextInput::make('max_viewers')
                            ->label('Max Viewers')
                            ->numeric()
                            ->default(0)
                            ->disabled()
                            ->dehydrated(false)
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('total_views')
                            ->label('Total Views')
                            ->numeric()
                            ->default(0)
                            ->disabled()
                            ->dehydrated(false)
                            ->columnSpan(1),
                    ])
                    ->columns(2)
                    ->visibleOn('edit'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('liveable_type')
                    ->label('Type')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        Course::class => 'Course',
                        Book::class => 'Book',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        Course::class => 'success',
                        Book::class => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('liveable.title')
                    ->label('Parent')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Teacher')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('scheduled_start_time')
                    ->label('Scheduled Start')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'scheduled' => 'warning',
                        'live' => 'success',
                        'ended' => 'gray',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_recording_enabled')
                    ->label('Recording')
                    ->boolean(),

                Tables\Columns\TextColumn::make('total_views')
                    ->label('Views')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_public')
                    ->label('Public')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'live' => 'Live',
                        'ended' => 'Ended',
                        'cancelled' => 'Cancelled',
                    ]),

                Tables\Filters\SelectFilter::make('liveable_type')
                    ->label('Type')
                    ->options([
                        Course::class => 'Course',
                        Book::class => 'Book',
                    ]),

                Tables\Filters\TernaryFilter::make('is_public')
                    ->label('Public Access'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
            ])
            ->actions([
                Tables\Actions\Action::make('start_live')
                    ->label('Start Live')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn (LiveVideo $record): bool => $record->status === 'scheduled')
                    ->action(function (LiveVideo $record) {
                        $record->start();
                        // Redirect to streaming interface (to be implemented)
                    }),

                Tables\Actions\Action::make('end_live')
                    ->label('End Live')
                    ->icon('heroicon-o-stop')
                    ->color('danger')
                    ->visible(fn (LiveVideo $record): bool => $record->status === 'live')
                    ->requiresConfirmation()
                    ->action(function (LiveVideo $record) {
                        $record->end();
                    }),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('scheduled_start_time', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLiveVideos::route('/'),
            'create' => Pages\CreateLiveVideo::route('/create'),
            'edit' => Pages\EditLiveVideo::route('/{record}/edit'),
        ];
    }
}
