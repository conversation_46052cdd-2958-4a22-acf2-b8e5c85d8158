<?php

namespace App\Filament\Clusters\Products\Resources\DigitalProductResource\Widgets;

use App\Filament\Clusters\Products\Resources\DigitalProductResource\Pages\ListDigitalProducts;
use App\Models\Shop\Product;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class DigitalProductStats extends BaseWidget
{
    use InteractsWithPageTable;

    protected static ?string $pollingInterval = null;

    protected function getTablePage(): string
    {
        return ListDigitalProducts::class;
    }

    protected function getStats(): array
    {
        $query = $this->getPageTableQuery();
        $totalProducts = $query->count();
        $averagePrice = $query->avg('price');
        
        // Digital product specific stats
        $withLicenseKey = $query->where('requires_license_key', true)->count();
        $withDownloadLimit = $query->whereNotNull('download_limit')->count();
        $totalFileSize = $query->sum('file_size_mb');
        $withFiles = $query->whereNotNull('file_size_mb')->count(); // Use file_size_mb as proxy for having files
        
        // Format breakdown
        $formatCounts = $query->selectRaw('digital_format, COUNT(*) as count')
            ->whereNotNull('digital_format')
            ->groupBy('digital_format')
            ->pluck('count', 'digital_format')
            ->toArray();
        
        $topFormat = !empty($formatCounts) ? array_keys($formatCounts, max($formatCounts))[0] : 'N/A';
        $topFormatCount = !empty($formatCounts) ? max($formatCounts) : 0;

        return [
            Stat::make('Total Digital Products', $totalProducts)
                ->description('Digital downloads available')
                ->descriptionIcon('heroicon-m-cloud-arrow-down')
                ->color('primary')
                ->chart([3, 8, 15, 22, 18, 25, $totalProducts])
                ->chartColor('primary'),

            Stat::make('Average Price', '$' . number_format($averagePrice, 2))
                ->description('Average digital product price')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('License Protected', $withLicenseKey)
                ->description('Products requiring license keys')
                ->descriptionIcon('heroicon-m-key')
                ->color($withLicenseKey > 0 ? 'warning' : 'gray'),

            Stat::make('Download Limited', $withDownloadLimit)
                ->description('Products with download limits')
                ->descriptionIcon('heroicon-m-arrow-down-circle')
                ->color('info'),

            Stat::make('Total File Size', number_format($totalFileSize, 1) . ' MB')
                ->description("{$withFiles} products with files")
                ->descriptionIcon('heroicon-m-document')
                ->color('indigo'),

            Stat::make('Top Format', strtoupper($topFormat))
                ->description("{$topFormatCount} products")
                ->descriptionIcon('heroicon-m-document-text')
                ->color('purple'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
