<?php

namespace App\Filament\Resources\MediaManager\Actions;

use App\Models\MediaManager\Folder;
use App\Traits\MediaManagerHelpers;
use Illuminate\Support\Str;
use TomatoPHP\FilamentIcons\Components\IconPicker;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;

class CreateFolderAction
{
    use MediaManagerHelpers;
    public static function make(): Actions\Action
    {
        return Actions\Action::make('create_folder')
            ->label('New Folder')
            ->icon('heroicon-o-folder-plus')
            ->color('success')
            ->form([
                Forms\Components\TextInput::make('name')
                    ->label(trans('filament-media-manager::messages.folders.columns.name'))
                    ->columnSpanFull()
                    ->lazy()
                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                        $set('collection', Str::slug($get('name')));
                    })
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('collection')
                    ->label(trans('filament-media-manager::messages.folders.columns.collection'))
                    ->columnSpanFull()
                    ->unique(Folder::class)
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->label(trans('filament-media-manager::messages.folders.columns.description'))
                    ->columnSpanFull()
                    ->maxLength(255),
                IconPicker::make('icon')
                    ->label(trans('filament-media-manager::messages.folders.columns.icon'))
                    ->default('heroicon-o-folder'),
                Forms\Components\ColorPicker::make('color')
                    ->label(trans('filament-media-manager::messages.folders.columns.color'))
                    ->default('#3B82F6'),
                Forms\Components\Toggle::make('is_protected')
                    ->label(trans('filament-media-manager::messages.folders.columns.is_protected'))
                    ->live()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('password')
                    ->label(trans('filament-media-manager::messages.folders.columns.password'))
                    ->hidden(fn(Forms\Get $get) => !$get('is_protected'))
                    ->password()
                    ->revealable()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('password_confirmation')
                    ->label(trans('filament-media-manager::messages.folders.columns.password_confirmation'))
                    ->hidden(fn(Forms\Get $get) => !$get('is_protected'))
                    ->password()
                    ->required()
                    ->revealable()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_public')
                    ->label('Public Folder')
                    ->helperText('Public folders can be seen by all team members')
                    ->default(true)
                    ->columnSpanFull(),
            ])
            ->action(function (array $data) {
                $user = auth()->user();
                
                if ($user) {
                    // Set our custom fields
                    $data['created_by'] = $user->id;
                    $data['owner_id'] = $user->id;

                    // Debug team ID assignment
                    $teamId = self::determineTeamId($user); 

                    $data['team_id'] = $teamId;
                    $data['is_personal'] = false; // Main folders are not personal folders
                    $data['is_deleted_user'] = false;
                    $data['user_id'] = $user->id;
                    $data['user_type'] = get_class($user);
                    $data['model_id'] = null;
                    $data['model_type'] = null;
                    $data['parent_id'] = null;
                    
                    // Set default values if not provided
                    $data['is_hidden'] = false;
                    $data['is_favorite'] = false;
                    $data['has_user_access'] = false;
                    
                    Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->create($data);
                }

                Notification::make()
                    ->title('Folder Created')
                    ->body('Folder created successfully!')
                    ->success()
                    ->send();
            });
    }
}
