<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('class_rooms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->string('grade_level'); // e.g., "Grade 1", "Grade 2", "Kindergarten"
            $table->string('room_number'); // e.g., "101", "A-205", "Lab-1"
            $table->string('room_name'); // e.g., "Math Lab", "Science Room", "Class 1A"
            $table->text('remark')->nullable(); // Additional notes or comments
            $table->boolean('is_active')->default(true);
            $table->integer('capacity')->nullable(); // Maximum number of students
            $table->timestamps();

            // Indexes
            $table->index(['team_id', 'is_active']);
            $table->index(['team_id', 'grade_level']);
            $table->unique(['team_id', 'room_number']); // Unique room number per team
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('class_rooms');
    }
};
