<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subjects', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->string('name');
            $table->string('code')->nullable(); // Subject code like MATH101, ENG201
            $table->text('description')->nullable();
            $table->string('color')->nullable(); // Hex color for UI display
            $table->integer('credits')->nullable(); // Credit hours
            $table->enum('level', ['elementary', 'middle', 'high', 'university'])->nullable();
            $table->string('grade_levels')->nullable(); // JSON array of applicable grade levels
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['team_id', 'is_active']);
            $table->index(['team_id', 'level']);
            $table->unique(['team_id', 'code']); // Unique code per team
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subjects');
    }
};
