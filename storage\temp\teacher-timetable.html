<!DOCTYPE html>
<!-- saved from url=(0140)https://bd6m3vznvkj6s6w8.canva-hosted-embed.com/codelet/AAEAEGJkNm0zdnpudmtqNnM2dzgAAAAAAZdOdqcr85PeTv8f0Dt82SEez3kuul1IayWfuenhRIRCad_He7w/ -->
<html lang="th"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ระบบจัดการตารางสอนครู</title>
    <script src="./saved_resource"></script>
    <link href="./css2" rel="stylesheet">
    <style>
        :root {
            --primary: #7b2cbf;
            --primary-light: #9d4edd;
            --primary-dark: #5a189a;
            --secondary: #ff7597;
            --light: #f8f9fa;
            --dark: #212529;
            --success: #00c896;
            --warning: #ffbd12;
            --info: #3da9fc;
        }
        
        body {
            font-family: 'Prompt', sans-serif;
            background-color: #fafafa;
            color: #333;
        }
        
        .btn {
            transition: all 0.2s ease;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
        }
        
        .btn-outline {
            border: 1px solid #e0e0e0;
            color: #666;
        }
        
        .btn-outline:hover {
            background-color: #f5f5f5;
        }
        
        .card {
            border-radius: 12px;
            border: none;
            transition: transform 0.2s ease;
        }
        
        .input-field {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
        }
        
        .input-field:focus {
            border-color: var(--primary-light);
            box-shadow: 0 0 0 3px rgba(157, 78, 221, 0.1);
            outline: none;
        }
        
        .tab {
            position: relative;
            cursor: pointer;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
        }
        
        .tab-active {
            color: var(--primary);
        }
        
        .tab-active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary);
            border-radius: 3px 3px 0 0;
        }
        
        .media-item {
            transition: all 0.2s ease;
            border-radius: 10px;
            border: 1px solid #f0f0f0;
        }
        
        .media-item:hover {
            border-color: var(--primary-light);
            background-color: #faf5ff;
        }
        
        .media-item.selected {
            background-color: #f3e8ff;
            border-color: var(--primary);
        }
        
        .tag {
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .modal {
            transition: opacity 0.3s ease;
        }
        
        .modal-content {
            border-radius: 16px;
            border: none;
        }
        
        /* Canva-style scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c5c5c5;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a5a5a5;
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.fixed{position:fixed}.inset-0{inset:0px}.z-50{z-index:50}.mx-4{margin-left:1rem;margin-right:1rem}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mr-2{margin-right:0.5rem}.mr-3{margin-right:0.75rem}.mt-6{margin-top:1.5rem}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.min-h-screen{min-height:100vh}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-8{width:2rem}.w-full{width:100%}.max-w-4xl{max-width:56rem}.max-w-md{max-width:28rem}.max-w-sm{max-width:24rem}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.space-x-3 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.75rem * var(--tw-space-x-reverse));margin-left:calc(0.75rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.rounded-2xl{border-radius:1rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-purple-200{--tw-border-opacity:1;border-color:rgb(233 213 255 / var(--tw-border-opacity, 1))}.bg-black{--tw-bg-opacity:1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-red-50{--tw-bg-opacity:1;background-color:rgb(254 242 242 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-opacity-30{--tw-bg-opacity:0.3}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-purple-200{--tw-gradient-from:#e9d5ff var(--tw-gradient-from-position);--tw-gradient-to:rgb(233 213 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-purple-600{--tw-gradient-from:#9333ea var(--tw-gradient-from-position);--tw-gradient-to:rgb(147 51 234 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-pink-200{--tw-gradient-to:#fbcfe8 var(--tw-gradient-to-position)}.to-pink-500{--tw-gradient-to:#ec4899 var(--tw-gradient-to-position)}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-1\.5{padding-top:0.375rem;padding-bottom:0.375rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-2\.5{padding-top:0.625rem;padding-bottom:0.625rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.py-8{padding-top:2rem;padding-bottom:2rem}.pt-4{padding-top:1rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-purple-700{--tw-text-opacity:1;color:rgb(126 34 206 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:bg-purple-50:hover{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.hover\:text-purple-800:hover{--tw-text-opacity:1;color:rgb(107 33 168 / var(--tw-text-opacity, 1))}@media (min-width: 640px){.sm\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.sm\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}@media (min-width: 768px){.md\:inline-block{display:inline-block}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}}</style></head>
<body>
    <div class="min-h-screen bg-white">
        <header class="bg-white border-b border-gray-100">
            <div class="container mx-auto px-4 py-4 flex justify-between items-center">
                <div class="flex items-center">
                    <div class="w-10 h-10 rounded-lg bg-gradient-to-r from-purple-600 to-pink-500 flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-800">ระบบจัดการตารางสอนครู</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="hidden md:inline-block text-gray-600">ครูสมศรี ใจดี</span>
                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-purple-200 to-pink-200 flex items-center justify-center">
                        <span class="text-purple-700 font-medium">ส</span>
                    </div>
                </div>
            </div>
        </header>

        <main class="container mx-auto px-4 py-8">
            <div class="max-w-4xl mx-auto">
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-800 mb-2">เพิ่มตารางสอนใหม่</h2>
                    <p class="text-gray-500">กรอกข้อมูลตารางสอนและเลือกสื่อการสอนที่ต้องการ</p>
                </div>
                
                <div class="bg-white rounded-xl p-6 mb-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="date">วันที่สอน</label>
                            <input type="date" id="date" class="w-full input-field">
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="time">เวลาที่สอน</label>
                            <select id="time" class="w-full input-field" fdprocessedid="bc32tk">
                                <option value="" disabled="" selected="">เลือกเวลาที่สอน</option>
                                <option value="08:30-09:30">08:30 - 09:30</option>
                                <option value="09:30-10:30">09:30 - 10:30</option>
                                <option value="10:30-11:30">10:30 - 11:30</option>
                                <option value="11:30-12:30">11:30 - 12:30</option>
                                <option value="13:00-14:00">13:00 - 14:00</option>
                                <option value="14:00-15:00">14:00 - 15:00</option>
                                <option value="15:00-16:00">15:00 - 16:00</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="class">ระดับชั้น / ห้อง</label>
                            <select id="class" class="w-full input-field" fdprocessedid="tgyp2n">
                                <option value="" disabled="" selected="">เลือกระดับชั้น / ห้อง</option>
                                <option value="ป.5/1">ป.5/1</option>
                                <option value="ป.5/2">ป.5/2</option>
                                <option value="ป.6/1">ป.6/1</option>
                                <option value="ป.6/2">ป.6/2</option>
                                <option value="ม.1/1">ม.1/1</option>
                                <option value="ม.1/2">ม.1/2</option>
                                <option value="ม.2/1">ม.2/1</option>
                                <option value="ม.2/2">ม.2/2</option>
                                <option value="ม.3/1">ม.3/1</option>
                                <option value="ม.3/2">ม.3/2</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="subject">วิชาที่สอน</label>
                            <select id="subject" class="w-full input-field" fdprocessedid="p2exwf">
                                <option value="" disabled="" selected="">เลือกวิชาที่สอน</option>
                                <option value="คณิตศาสตร์">คณิตศาสตร์</option>
                                <option value="วิทยาศาสตร์">วิทยาศาสตร์</option>
                                <option value="ภาษาไทย">ภาษาไทย</option>
                                <option value="ภาษาอังกฤษ">ภาษาอังกฤษ</option>
                                <option value="สังคมศึกษา">สังคมศึกษา</option>
                                <option value="ประวัติศาสตร์">ประวัติศาสตร์</option>
                                <option value="สุขศึกษา">สุขศึกษา</option>
                                <option value="ศิลปะ">ศิลปะ</option>
                                <option value="การงานอาชีพ">การงานอาชีพ</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">จัดการสื่อการสอน</h3>
                        
                        <div class="flex border-b border-gray-200 mb-6">
                            <button id="tab-default" class="tab tab-active" onclick="switchTab(&#39;default&#39;)" fdprocessedid="imff2a">ใช้สื่อตามบทเรียน</button>
                            <button id="tab-custom" class="tab" onclick="switchTab(&#39;custom&#39;)" fdprocessedid="0fjj1e">เลือกจัดการสื่อเอง</button>
                        </div>
                        
                        <div id="content-default" class="py-3">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label class="block text-gray-700 font-medium mb-2" for="book">หนังสือเรียน</label>
                                    <select id="book" class="w-full input-field" fdprocessedid="sp2naw">
                                        <option value="" disabled="" selected="">เลือกหนังสือเรียน</option>
                                        <option value="คณิตศาสตร์ ป.5">คณิตศาสตร์ ป.5</option>
                                        <option value="คณิตศาสตร์ ป.6">คณิตศาสตร์ ป.6</option>
                                        <option value="คณิตศาสตร์ ม.1">คณิตศาสตร์ ม.1</option>
                                        <option value="คณิตศาสตร์ ม.2">คณิตศาสตร์ ม.2</option>
                                        <option value="คณิตศาสตร์ ม.3">คณิตศาสตร์ ม.3</option>
                                        <option value="วิทยาศาสตร์ ป.5">วิทยาศาสตร์ ป.5</option>
                                        <option value="วิทยาศาสตร์ ป.6">วิทยาศาสตร์ ป.6</option>
                                        <option value="วิทยาศาสตร์ ม.1">วิทยาศาสตร์ ม.1</option>
                                        <option value="วิทยาศาสตร์ ม.2">วิทยาศาสตร์ ม.2</option>
                                        <option value="วิทยาศาสตร์ ม.3">วิทยาศาสตร์ ม.3</option>
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-gray-700 font-medium mb-2" for="chapter">บทเรียน</label>
                                    <select id="chapter" class="w-full input-field" fdprocessedid="92fbe8">
                                        <option value="" disabled="" selected="">เลือกบทเรียน</option>
                                        <option value="บทที่ 1">บทที่ 1 - จำนวนและการดำเนินการ</option>
                                        <option value="บทที่ 2">บทที่ 2 - การวัด</option>
                                        <option value="บทที่ 3">บทที่ 3 - เรขาคณิต</option>
                                        <option value="บทที่ 4">บทที่ 4 - พีชคณิต</option>
                                        <option value="บทที่ 5">บทที่ 5 - การวิเคราะห์ข้อมูลและความน่าจะเป็น</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div id="materials-section" class="hidden bg-gray-50 rounded-xl p-6 mb-6">
                                <h4 class="font-medium text-gray-700 mb-4">สื่อการสอนที่มีให้</h4>
                                
                                <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                                    <div class="bg-white rounded-xl p-4 flex flex-col items-center text-center">
                                        <div class="w-12 h-12 rounded-full bg-red-50 flex items-center justify-center mb-3">
                                            <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">แผนการสอน</span>
                                        <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PDF</button>
                                    </div>
                                    
                                    <div class="bg-white rounded-xl p-4 flex flex-col items-center text-center">
                                        <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-3">
                                            <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">ใบงาน</span>
                                        <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PDF</button>
                                    </div>
                                    
                                    <div class="bg-white rounded-xl p-4 flex flex-col items-center text-center">
                                        <div class="w-12 h-12 rounded-full bg-green-50 flex items-center justify-center mb-3">
                                            <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">PowerPoint</span>
                                        <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PPTX</button>
                                    </div>
                                    
                                    <div class="bg-white rounded-xl p-4 flex flex-col items-center text-center">
                                        <div class="w-12 h-12 rounded-full bg-purple-50 flex items-center justify-center mb-3">
                                            <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <span class="text-sm text-gray-600 mb-3">วิดีโอ</span>
                                        <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดูตัวอย่าง</button>
                                    </div>
                                </div>
                                
                                <div class="mt-6">
                                    <button id="add-homework-btn" class="flex items-center text-sm font-medium text-purple-600 hover:text-purple-800 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        เพิ่มการบ้านหรือกิจกรรม
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="content-custom" class="py-3 hidden">
                            <h4 class="font-medium text-gray-700 mb-4">คลังสื่อของฉัน</h4>
                            
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                                <div class="media-item p-4 flex items-center cursor-pointer">
                                    <div class="w-10 h-10 rounded-lg bg-red-50 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium">แผนการสอนคณิตศาสตร์</p>
                                        <p class="text-xs text-gray-500">PDF • 2.3 MB</p>
                                    </div>
                                </div>
                                
                                <div class="media-item p-4 flex items-center cursor-pointer">
                                    <div class="w-10 h-10 rounded-lg bg-green-50 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium">สไลด์บทที่ 3</p>
                                        <p class="text-xs text-gray-500">PPTX • 5.7 MB</p>
                                    </div>
                                </div>
                                
                                <div class="media-item p-4 flex items-center cursor-pointer">
                                    <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                                        <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium">ใบงานเรื่องเศษส่วน</p>
                                        <p class="text-xs text-gray-500">PDF • 1.1 MB</p>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="flex items-center text-sm font-medium text-purple-600 hover:text-purple-800 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                เพิ่มสื่อใหม่
                            </button>
                        </div>
                    </div>
                    
                    <div id="preview-section" class="hidden bg-gray-50 rounded-xl p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">ตัวอย่างตารางสอน</h3>
                        
                        <div class="bg-white rounded-xl p-6">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">วันที่สอน</p>
                                    <p class="font-medium" id="preview-date">15 มิถุนายน 2566</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">เวลาที่สอน</p>
                                    <p class="font-medium" id="preview-time">09:30 - 10:30</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">ระดับชั้น / ห้อง</p>
                                    <p class="font-medium" id="preview-class">ม.2/1</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500 mb-1">วิชาที่สอน</p>
                                    <p class="font-medium" id="preview-subject">คณิตศาสตร์</p>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-100 pt-4">
                                <p class="text-sm text-gray-500 mb-3">สื่อการสอนที่เลือก</p>
                                <div class="flex flex-wrap gap-2" id="preview-materials">
                                    <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-xs">แผนการสอน</span>
                                    <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-xs">ใบงาน</span>
                                    <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-xs">PowerPoint</span>
                                    <span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-xs">วิดีโอประกอบการสอน</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button id="save-btn" class="btn btn-primary px-6 py-2.5" fdprocessedid="2czz7w">
                            บันทึกตารางสอน
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal สำหรับเพิ่มการบ้าน -->
    <div id="homework-modal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden modal">
        <div class="bg-white rounded-2xl w-full max-w-md mx-4 modal-content">
            <div class="px-6 py-4 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800">เพิ่มการบ้านหรือกิจกรรม</h3>
            </div>
            
            <div class="px-6 py-6">
                <div class="mb-4">
                    <label class="block text-gray-700 font-medium mb-2" for="homework-title">หัวข้อการบ้าน</label>
                    <input type="text" id="homework-title" class="w-full input-field" placeholder="เช่น แบบฝึกหัดบทที่ 3">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 font-medium mb-2" for="homework-desc">รายละเอียด</label>
                    <textarea id="homework-desc" rows="4" class="w-full input-field" placeholder="รายละเอียดการบ้านหรือกิจกรรม"></textarea>
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 font-medium mb-2" for="homework-due">กำหนดส่ง</label>
                    <input type="date" id="homework-due" class="w-full input-field">
                </div>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-100 flex justify-end space-x-3">
                <button id="close-modal-btn" class="btn btn-outline px-4 py-2">ยกเลิก</button>
                <button id="add-homework-confirm-btn" class="btn btn-primary px-4 py-2">เพิ่มการบ้าน</button>
            </div>
        </div>
    </div>

    <!-- Modal แจ้งเตือนบันทึกสำเร็จ -->
    <div id="success-modal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden modal">
        <div class="bg-white rounded-2xl w-full max-w-sm mx-4 p-6 text-center modal-content">
            <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-green-50 flex items-center justify-center">
                <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-800 mb-2">บันทึกสำเร็จ</h3>
            <p class="text-gray-600 mb-6">บันทึกตารางสอนเรียบร้อยแล้ว</p>
            
            <button id="success-close-btn" class="w-full btn btn-primary py-2.5">ตกลง</button>
        </div>
    </div>

    <script>
        // สลับแท็บระหว่างใช้สื่อตามบทเรียนและเลือกจัดการสื่อเอง
        function switchTab(tab) {
            const defaultTab = document.getElementById('tab-default');
            const customTab = document.getElementById('tab-custom');
            const defaultContent = document.getElementById('content-default');
            const customContent = document.getElementById('content-custom');
            
            if (tab === 'default') {
                defaultTab.classList.add('tab-active');
                customTab.classList.remove('tab-active');
                defaultContent.classList.remove('hidden');
                customContent.classList.add('hidden');
            } else {
                defaultTab.classList.remove('tab-active');
                customTab.classList.add('tab-active');
                defaultContent.classList.add('hidden');
                customContent.classList.remove('hidden');
            }
        }
        
        // เลือกสื่อการสอน
        document.querySelectorAll('.media-item').forEach(item => {
            item.addEventListener('click', function() {
                this.classList.toggle('selected');
            });
        });
        
        // แสดงสื่อการสอนเมื่อเลือกบทเรียน
        document.getElementById('chapter').addEventListener('change', function() {
            if (this.value) {
                document.getElementById('materials-section').classList.remove('hidden');
                updatePreview();
            }
        });
        
        // เปิด Modal เพิ่มการบ้าน
        document.getElementById('add-homework-btn').addEventListener('click', function() {
            document.getElementById('homework-modal').classList.remove('hidden');
        });
        
        // ปิด Modal เพิ่มการบ้าน
        document.getElementById('close-modal-btn').addEventListener('click', function() {
            document.getElementById('homework-modal').classList.add('hidden');
        });
        
        // เพิ่มการบ้าน
        document.getElementById('add-homework-confirm-btn').addEventListener('click', function() {
            const title = document.getElementById('homework-title').value;
            if (title) {
                // เพิ่มการบ้านลงในพรีวิว
                const homeworkTag = document.createElement('span');
                homeworkTag.className = 'bg-orange-50 text-orange-600 px-3 py-1 rounded-full text-xs';
                homeworkTag.textContent = 'การบ้าน: ' + title;
                document.getElementById('preview-materials').appendChild(homeworkTag);
                
                // ปิด Modal
                document.getElementById('homework-modal').classList.add('hidden');
                
                // รีเซ็ตฟอร์ม
                document.getElementById('homework-title').value = '';
                document.getElementById('homework-desc').value = '';
                document.getElementById('homework-due').value = '';
            }
        });
        
        // อัพเดตพรีวิว
        function updatePreview() {
            const date = document.getElementById('date').value;
            const time = document.getElementById('time').value;
            const classRoom = document.getElementById('class').value;
            const subject = document.getElementById('subject').value;
            
            if (date && time && classRoom && subject) {
                document.getElementById('preview-section').classList.remove('hidden');
                
                // แปลงรูปแบบวันที่
                const dateObj = new Date(date);
                const thaiMonths = [
                    'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
                    'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
                ];
                const thaiDate = `${dateObj.getDate()} ${thaiMonths[dateObj.getMonth()]} ${dateObj.getFullYear() + 543}`;
                
                document.getElementById('preview-date').textContent = thaiDate;
                document.getElementById('preview-time').textContent = time;
                document.getElementById('preview-class').textContent = classRoom;
                document.getElementById('preview-subject').textContent = subject;
            }
        }
        
        // ติดตามการเปลี่ยนแปลงของฟอร์ม
        document.getElementById('date').addEventListener('change', updatePreview);
        document.getElementById('time').addEventListener('change', updatePreview);
        document.getElementById('class').addEventListener('change', updatePreview);
        document.getElementById('subject').addEventListener('change', updatePreview);
        
        // บันทึกตารางสอน
        document.getElementById('save-btn').addEventListener('click', function() {
            const date = document.getElementById('date').value;
            const time = document.getElementById('time').value;
            const classRoom = document.getElementById('class').value;
            const subject = document.getElementById('subject').value;
            
            if (date && time && classRoom && subject) {
                document.getElementById('success-modal').classList.remove('hidden');
            } else {
                alert('กรุณากรอกข้อมูลให้ครบถ้วน');
            }
        });
        
        // ปิด Modal แจ้งเตือนบันทึกสำเร็จ
        document.getElementById('success-close-btn').addEventListener('click', function() {
            document.getElementById('success-modal').classList.add('hidden');
            
            // รีเซ็ตฟอร์ม
            document.getElementById('date').value = '';
            document.getElementById('time').value = '';
            document.getElementById('class').value = '';
            document.getElementById('subject').value = '';
            document.getElementById('book').value = '';
            document.getElementById('chapter').value = '';
            document.getElementById('materials-section').classList.add('hidden');
            document.getElementById('preview-section').classList.add('hidden');
            
            // ยกเลิกการเลือกสื่อ
            document.querySelectorAll('.media-item.selected').forEach(item => {
                item.classList.remove('selected');
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'94be781cd22bd01f',t:'MTc0OTI4MTY4OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;" src="./saved_resource.html"></iframe>

<span id="PING_IFRAME_FORM_DETECTION" style="display: none;"></span></body></html>