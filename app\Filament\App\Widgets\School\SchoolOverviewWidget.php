<?php

namespace App\Filament\App\Widgets\School;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class SchoolOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();
        
        if (!$user || !($user->hasRole('team_admin') || $user->hasRole('school'))) {
            return [];
        }

        // Placeholder data - replace with actual data from database
        $totalStudents = 450; // Total students in the school
        $totalTeachers = 25;  // Total teachers
        $totalParents = 380;  // Total parents
        $activeUsers = 120;   // Currently active users

        return [
            Stat::make('Total Students', $totalStudents)
                ->description('Enrolled students')
                ->descriptionIcon('heroicon-m-users')
                ->color('success')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Total Teachers', $totalTeachers)
                ->description('Active teaching staff')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('info')
                ->chart([15, 4, 10, 2, 12, 4, 12]),

            Stat::make('Total Parents', $totalParents)
                ->description('Registered parents')
                ->descriptionIcon('heroicon-m-heart')
                ->color('warning')
                ->chart([2, 10, 1, 15, 4, 17, 2]),

            Stat::make('Active Users', $activeUsers)
                ->description('Currently active')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('primary')
                ->chart([17, 16, 14, 15, 14, 13, 12]),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && ($user->hasRole('team_admin') || $user->hasRole('school'));
    }
}
