<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Basic Information
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            $table->string('phone_number')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Thailand');

            // Profile Image
            $table->string('profile_image')->nullable();
            $table->text('bio')->nullable();

            // Emergency Contact (for students)
            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();
            $table->string('emergency_contact_relationship')->nullable();

            // Academic Information
            $table->string('student_id')->nullable(); // For students
            $table->string('employee_id')->nullable(); // For teachers/staff
            $table->string('grade_level')->nullable(); // For students
            $table->string('class_section')->nullable(); // For students
            $table->json('subjects')->nullable(); // For teachers (array of subjects they teach)
            $table->string('qualification')->nullable(); // For teachers
            $table->integer('years_of_experience')->nullable(); // For teachers

            // Parent-specific fields
            $table->string('occupation')->nullable();
            $table->string('workplace')->nullable();
            $table->json('children_ids')->nullable(); // For parents (array of student IDs)

            // School-specific fields
            $table->string('school_name')->nullable();
            $table->string('school_code')->nullable();
            $table->text('school_address')->nullable();
            $table->string('principal_name')->nullable();
            $table->string('school_phone')->nullable();
            $table->string('school_email')->nullable();
            $table->integer('total_students')->nullable();
            $table->integer('total_teachers')->nullable();
            $table->date('established_date')->nullable();

            // Profile completion tracking
            $table->boolean('profile_completed')->default(false);
            $table->timestamp('profile_completed_at')->nullable();
            $table->json('completed_sections')->nullable(); // Track which sections are completed

            // Preferences
            $table->string('language')->default('th');
            $table->string('timezone')->default('Asia/Bangkok');
            $table->json('notification_preferences')->nullable();

            // Social Links
            $table->string('facebook_url')->nullable();
            $table->string('line_id')->nullable();
            $table->string('instagram_url')->nullable();

            $table->timestamps();

            // Indexes
            $table->index('user_id');
            $table->index('student_id');
            $table->index('employee_id');
            $table->index('school_code');
            $table->index('profile_completed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_profiles');
    }
};
