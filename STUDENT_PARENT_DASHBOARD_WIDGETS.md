# Student & Parent Dashboard Widgets

This document describes the enhanced Filament widgets created for Student and Parent dashboards based on the student dashboard HTML template. These widgets are designed to **look alike but have different content** for each role.

## 🎯 Design Philosophy

The Student and Parent dashboards share similar visual design patterns but display role-appropriate content:
- **Students** see their own progress, assignments, and learning activities
- **Parents** see their children's progress, school communications, and family-related activities

## 👨‍🎓 Student Dashboard Widgets

### 1. StudentOverviewWidget
**File:** `app/Filament/App/Widgets/Student/StudentOverviewWidget.php`
**Type:** Stats Overview Widget
**Features:**
- เกรดเฉลี่ย (Current GPA)
- การบ้านที่ต้องส่ง (Assignments Due)
- วิชาที่เรียนจบ (Completed Courses)
- อัตราการเข้าเรียน (Attendance Rate)

### 2. MyGradesWidget (Enhanced Subject Progress)
**Files:** 
- `app/Filament/App/Widgets/Student/MyGradesWidget.php`
- `resources/views/filament/app/widgets/student/my-grades.blade.php`

**Features:**
- Circular progress indicators for each subject
- Color-coded subject cards with gradients
- Progress percentage and completion tracking
- Average scores and exercise completion rates
- Subjects: คณิตศาสตร์, วิทยาศาสตร์, ภาษาไทย, ภาษาอังกฤษ, สังคมศึกษา, ศิลปะ

### 3. StudentCalendarWidget
**Files:**
- `app/Filament/App/Widgets/Student/StudentCalendarWidget.php`
- `resources/views/filament/app/widgets/student/student-calendar.blade.php`

**Features:**
- Monthly calendar view with navigation
- Student-specific events (Live Classes, Activity Boxes, Tests)
- Today's activities sidebar with action buttons
- Event types: Live Class, กล่องกิจกรรม, แบบทดสอบ
- Interactive action buttons for each activity

### 4. StudentActivitySummaryWidget
**Files:**
- `app/Filament/App/Widgets/Student/StudentActivitySummaryWidget.php`
- `resources/views/filament/app/widgets/student/student-activity-summary.blade.php`

**Features:**
- Table format showing recent exercise completion
- Date, subject, time spent, scores, and review status
- Color-coded subjects and review status indicators
- Thai language interface

### 5. StudentDailyPracticeWidget
**Files:**
- `app/Filament/App/Widgets/Student/StudentDailyPracticeWidget.php`
- `resources/views/filament/app/widgets/student/student-daily-practice.blade.php`

**Features:**
- Progress tracking for current subject (คณิตศาสตร์)
- Chapter-based learning progression
- Lock/unlock system for sequential learning
- Recommended resources section
- Resource types: วิดีโอ, ใบงาน, เกม, เคล็ดลับ

## 👨‍👩‍👧‍👦 Parent Dashboard Widgets

### 1. ParentOverviewWidget (Enhanced)
**File:** `app/Filament/App/Widgets/Parent/ParentOverviewWidget.php`
**Features:**
- ลูกของฉัน (My Children count)
- ค่าใช้จ่ายค้างชำระ (Pending Payments)
- กิจกรรมที่จะมาถึง (Upcoming Events)
- ข้อความ (Unread Messages from teachers)

### 2. ChildrenProgressWidget (Enhanced)
**Files:**
- `app/Filament/App/Widgets/Parent/ChildrenProgressWidget.php`
- `resources/views/filament/app/widgets/parent/children-progress.blade.php`

**Features:**
- Multiple children support (น้องมายด์, น้องมิกกี้)
- Same visual design as student progress but shows parent perspective
- Circular progress indicators for each child's subjects
- Overall performance indicators
- Quick action buttons for parents (ส่งข้อความหาครู, ดูรายงานผลการเรียน, ดูตารางเรียน)

### 3. ParentCalendarWidget
**Files:**
- `app/Filament/App/Widgets/Parent/ParentCalendarWidget.php`
- `resources/views/filament/app/widgets/parent/parent-calendar.blade.php`

**Features:**
- Family-oriented calendar view
- Parent-specific events (ประชุมผู้ปกครอง, ครบกำหนดชำระค่าเทอม, งานแสดงผลงานนักเรียน)
- Today's notifications sidebar
- Notification types: Child Progress, Teacher Messages, Payment Reminders
- Action buttons for parent responses

## 🎨 Visual Design Similarities

### Shared Design Elements:
1. **Circular Progress Indicators** - Both use same SVG-based progress circles
2. **Color-Coded Subjects** - Consistent color scheme across roles
3. **Card-Based Layout** - Similar card designs with gradients
4. **Calendar Grid** - Same calendar structure and navigation
5. **Action Buttons** - Consistent button styling and interactions
6. **Thai Language Interface** - Both use Thai text and formatting

### Role-Specific Differences:

| Element | Student View | Parent View |
|---------|-------------|-------------|
| **Progress Cards** | Own subject progress | Children's progress overview |
| **Calendar Events** | Live classes, tests, activities | Parent meetings, payments, school events |
| **Sidebar Content** | Today's learning activities | Family notifications and alerts |
| **Action Buttons** | Join class, take quiz, view resources | Message teacher, view reports, make payments |
| **Statistics** | Personal academic metrics | Family and payment metrics |

## 🔧 Technical Implementation

### Widget Structure:
```php
// Student widgets use student-specific data
public static function canView(): bool
{
    $user = Auth::user();
    return $user && $user->hasRole('student');
}

// Parent widgets use parent-specific data
public static function canView(): bool
{
    $user = Auth::user();
    return $user && $user->hasRole('parent');
}
```

### View Organization:
```
resources/views/filament/app/widgets/
├── student/
│   ├── my-grades.blade.php (subject progress)
│   ├── student-calendar.blade.php
│   ├── student-activity-summary.blade.php
│   └── student-daily-practice.blade.php
└── parent/
    ├── children-progress.blade.php (children's progress)
    ├── parent-calendar.blade.php
    └── [other parent views]
```

## 📱 Responsive Design

Both dashboards are fully responsive with:
- **Mobile-first approach** - Optimized for mobile devices
- **Flexible grid layouts** - Adapts to different screen sizes
- **Touch-friendly interactions** - Large buttons and touch targets
- **Collapsible sidebars** - Space-efficient on smaller screens

## 🚀 Key Features

### Student Dashboard:
- ✅ Personal learning progress tracking
- ✅ Interactive calendar with learning activities
- ✅ Daily practice progression system
- ✅ Exercise completion tracking
- ✅ Resource recommendations

### Parent Dashboard:
- ✅ Multiple children progress monitoring
- ✅ School communication center
- ✅ Payment tracking and reminders
- ✅ Family calendar with school events
- ✅ Teacher interaction tools

## 🔄 Data Integration

Currently using placeholder data. To integrate with real data:

1. **Replace placeholder arrays** with database queries
2. **Add Eloquent relationships** between users, students, and progress
3. **Implement real-time updates** for progress tracking
4. **Connect payment systems** for parent dashboard
5. **Integrate messaging system** for teacher-parent communication

This implementation successfully creates dashboards that **look alike but are not the same**, providing role-appropriate content while maintaining visual consistency across the platform.
