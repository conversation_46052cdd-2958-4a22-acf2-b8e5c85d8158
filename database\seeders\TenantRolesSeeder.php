<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use <PERSON><PERSON>\Permission\Models\Permission;

class TenantRolesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create tenant-safe roles
        $tenantRoles = [
            [
                'name' => 'team_admin',
                'guard_name' => 'web',
                'permissions' => [
                    // User management
                    'view_any_user',
                    'view_user',
                    'create_user',
                    'update_user',
                    'delete_user',
                    
                    // Content management
                    'view_any_blog::post',
                    'view_blog::post',
                    'create_blog::post',
                    'update_blog::post',
                    'delete_blog::post',
                    
                    'view_any_blog::category',
                    'view_blog::category',
                    'create_blog::category',
                    'update_blog::category',
                    'delete_blog::category',
                    
                    // Shop management
                    'view_any_shop::customer',
                    'view_shop::customer',
                    'create_shop::customer',
                    'update_shop::customer',
                    'delete_shop::customer',
                    
                    'view_any_shop::order',
                    'view_shop::order',
                    'create_shop::order',
                    'update_shop::order',
                    'delete_shop::order',
                    
                    'view_any_product',
                    'view_product',
                    'create_product',
                    'update_product',
                    'delete_product',
                ]
            ],
            [
                'name' => 'team_editor',
                'guard_name' => 'web',
                'permissions' => [
                    // Content management only
                    'view_any_blog::post',
                    'view_blog::post',
                    'create_blog::post',
                    'update_blog::post',
                    
                    'view_any_blog::category',
                    'view_blog::category',
                    'create_blog::category',
                    'update_blog::category',
                    
                    // Limited shop access
                    'view_any_shop::customer',
                    'view_shop::customer',
                    'create_shop::customer',
                    'update_shop::customer',
                    
                    'view_any_product',
                    'view_product',
                    'create_product',
                    'update_product',
                ]
            ],
            [
                'name' => 'team_member',
                'guard_name' => 'web',
                'permissions' => [
                    // Read-only access
                    'view_any_blog::post',
                    'view_blog::post',
                    
                    'view_any_blog::category',
                    'view_blog::category',
                    
                    'view_any_shop::customer',
                    'view_shop::customer',
                    
                    'view_any_product',
                    'view_product',
                ]
            ]
        ];

        foreach ($tenantRoles as $roleData) {
            // Create role
            $role = Role::firstOrCreate([
                'name' => $roleData['name'],
                'guard_name' => $roleData['guard_name']
            ]);

            // Create permissions if they don't exist and assign to role
            $permissions = [];
            foreach ($roleData['permissions'] as $permissionName) {
                $permission = Permission::firstOrCreate([
                    'name' => $permissionName,
                    'guard_name' => 'web'
                ]);
                $permissions[] = $permission;
            }

            // Sync permissions to role
            $role->syncPermissions($permissions);
        }

        // Create super admin role if it doesn't exist
        $superAdminRole = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web'
        ]);

        // Super admin gets all permissions
        $superAdminRole->syncPermissions(Permission::all());

        $this->command->info('Tenant roles and permissions created successfully!');
    }
}
