<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Console\Command;

class TestUserFiltering extends Command
{
    protected $signature = 'test:user-filtering {user_email} {tenant_slug?}';
    protected $description = 'Test user filtering for a specific user and tenant';

    public function handle()
    {
        $userEmail = $this->argument('user_email');
        $tenantSlug = $this->argument('tenant_slug');

        $user = User::withoutGlobalScope('team')->where('email', $userEmail)->first();
        if (!$user) {
            $this->error("User with email {$userEmail} not found.");
            return 1;
        }

        $this->info("=== Testing User Filtering ===");
        $this->info("User: {$user->name} ({$user->email})");
        $this->info("User Team ID: " . ($user->team_id ?? 'NULL (Super Admin)'));

        // Set the user context
        auth()->login($user);

        if ($tenantSlug) {
            $tenant = Team::where('slug', $tenantSlug)->first();
            if (!$tenant) {
                $this->error("Tenant with slug {$tenantSlug} not found.");
                return 1;
            }

            $this->info("Setting tenant context: {$tenant->name} (ID: {$tenant->id})");
            
            // Simulate Filament tenant context
            app()->instance('filament.tenant', $tenant);
            
            $this->info("\n=== With Tenant Context: {$tenant->name} ===");
        } else {
            $this->info("\n=== Without Tenant Context ===");
        }

        // Test user filtering
        $this->testUserFiltering();

        return 0;
    }

    private function testUserFiltering()
    {
        try {
            $visibleUsers = User::all();
            $this->info("📊 Visible Users: {$visibleUsers->count()}");
            
            if ($visibleUsers->count() > 0) {
                $this->info("Users visible:");
                foreach ($visibleUsers->take(5) as $user) {
                    $teamName = $user->team ? $user->team->name : 'No Team (Super Admin)';
                    $this->line("   - {$user->name} ({$user->email}) - Team: {$teamName} (ID: {$user->team_id})");
                }
                
                if ($visibleUsers->count() > 5) {
                    $this->line("   ... and " . ($visibleUsers->count() - 5) . " more");
                }
            } else {
                $this->warn("No users visible with current filtering.");
            }
        } catch (\Exception $e) {
            $this->error("❌ Error getting users: " . $e->getMessage());
        }

        // Test raw queries to see actual data
        $this->info("\n=== Raw Data (bypassing scopes) ===");
        $totalUsers = User::withoutGlobalScope('team')->count();
        $this->info("📊 Total Users (raw): {$totalUsers}");

        // Show team distribution
        $teamDistribution = User::withoutGlobalScope('team')
            ->select('team_id', \DB::raw('count(*) as count'))
            ->groupBy('team_id')
            ->get();
        
        $this->info("\n=== User Team Distribution (Raw) ===");
        foreach ($teamDistribution as $dist) {
            $teamName = $dist->team_id ? Team::find($dist->team_id)?->name ?? "Team {$dist->team_id}" : 'Super Admins';
            $this->line("   - {$teamName}: {$dist->count} users");
        }
    }
}
