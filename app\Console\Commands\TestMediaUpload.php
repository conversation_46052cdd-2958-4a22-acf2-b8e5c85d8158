<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Models\Shop\Product;
use App\Models\MediaManager\Media;
use App\Models\MediaManager\Folder;
use Illuminate\Console\Command;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class TestMediaUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:test-upload {team_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test media upload functionality with team-based organization';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $teamId = $this->argument('team_id') ?? 1;
        
        $team = Team::find($teamId);
        if (!$team) {
            $this->error("Team with ID {$teamId} not found.");
            return;
        }

        $this->info("Testing media upload for team: {$team->name} (ID: {$team->id})");

        // Test 1: Create a test product and upload an image
        $this->testProductImageUpload($team);

        // Test 2: Check media path generation
        $this->testMediaPathGeneration($team);

        // Test 3: Verify folder structure
        $this->testFolderStructure($team);

        $this->info('Media upload test completed!');
    }

    /**
     * Test product image upload.
     */
    protected function testProductImageUpload(Team $team)
    {
        $this->line("\n--- Testing Product Image Upload ---");

        // Simulate authentication with a user from this team
        $user = \App\Models\User::where('team_id', $team->id)->first();
        if (!$user) {
            $user = \App\Models\User::first(); // Fallback to any user
        }

        if ($user) {
            auth()->login($user);
            $this->info("Authenticated as: {$user->name}");
        }

        // Find or create a test product for this team
        $product = Product::where('team_id', $team->id)->first();

        if (!$product) {
            $product = Product::create([
                'name' => 'Test Product for ' . $team->name,
                'slug' => 'test-product-' . $team->id,
                'sku' => 'TEST-' . $team->id,
                'description' => 'Test product for media upload testing',
                'price' => 99.99,
                'team_id' => $team->id,
                'is_visible' => true,
            ]);
            $this->info("Created test product: {$product->name}");
        } else {
            $this->info("Using existing product: {$product->name}");
        }

        // Create a test image file
        $testImagePath = $this->createTestImage();

        if ($testImagePath) {
            // Add media to the product with custom properties
            $media = $product->addMedia($testImagePath)
                ->withCustomProperties([
                    'team_id' => $team->id,
                    'created_by_user' => $user ? $user->name : 'System',
                    'team_name' => $team->name,
                ])
                ->toMediaCollection('product-images');

            // Manually update the media record to ensure team_id is set
            $media->update([
                'team_id' => $team->id,
                'created_by' => $user?->id,
                'user_id' => $user?->id,
            ]);

            $this->info("Uploaded test image to product");
            $this->displayMediaInfo($media->fresh());

            // Clean up test file
            if (file_exists($testImagePath)) {
                unlink($testImagePath);
            }
        }
    }

    /**
     * Test media path generation.
     */
    protected function testMediaPathGeneration(Team $team)
    {
        $this->line("\n--- Testing Media Path Generation ---");

        $media = Media::where('team_id', $team->id)->latest()->first();

        if ($media) {
            $this->info("Latest media for team {$team->name}:");
            $this->displayMediaInfo($media);

            // Test path generation
            $pathGenerator = new \App\Services\MediaPathGenerator();
            $path = $pathGenerator->getPath($media);
            $this->info("Generated path: {$path}");

            // Check if file exists at the generated path
            $fullPath = storage_path("app/public/{$path}{$media->file_name}");
            if (file_exists($fullPath)) {
                $this->info("✓ File exists at generated path");
            } else {
                $this->warn("✗ File not found at generated path: {$fullPath}");
            }
        } else {
            $this->warn("No media found for team {$team->name}");
        }
    }

    /**
     * Test folder structure.
     */
    protected function testFolderStructure(Team $team)
    {
        $this->line("\n--- Testing Folder Structure ---");

        $folders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
            ->where('team_id', $team->id)
            ->orderBy('parent_id')
            ->orderBy('name')
            ->get();

        $this->info("Folders for team {$team->name}:");
        foreach ($folders as $folder) {
            $indent = $folder->parent_id ? '  ' : '';
            $this->line("{$indent}- {$folder->name} (Collection: {$folder->collection})");
        }

        // Check physical directories
        $teamPath = "teams/{$team->id}";
        if (Storage::disk('public')->exists($teamPath)) {
            $this->info("\nPhysical directories:");
            $directories = Storage::disk('public')->directories($teamPath);
            foreach ($directories as $dir) {
                $dirName = basename($dir);
                $this->line("  - {$dirName}");
            }
        }
    }

    /**
     * Display media information.
     */
    protected function displayMediaInfo(Media $media)
    {
        $this->table(
            ['Property', 'Value'],
            [
                ['ID', $media->id],
                ['Name', $media->name],
                ['File Name', $media->file_name],
                ['Collection', $media->collection_name],
                ['Team ID', $media->team_id],
                ['Created By', $media->created_by],
                ['User ID', $media->user_id],
                ['Model Type', $media->model_type],
                ['Model ID', $media->model_id],
                ['Custom Properties', json_encode($media->custom_properties, JSON_PRETTY_PRINT)],
            ]
        );
    }

    /**
     * Create a test image file.
     */
    protected function createTestImage(): ?string
    {
        $tempPath = storage_path('app/temp');
        if (!is_dir($tempPath)) {
            mkdir($tempPath, 0755, true);
        }

        $imagePath = $tempPath . '/test-image-' . time() . '.jpg';

        // Create a simple 100x100 red image
        $image = imagecreate(100, 100);
        $red = imagecolorallocate($image, 255, 0, 0);
        imagefill($image, 0, 0, $red);

        if (imagejpeg($image, $imagePath)) {
            imagedestroy($image);
            return $imagePath;
        }

        return null;
    }
}
