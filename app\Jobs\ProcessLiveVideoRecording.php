<?php

namespace App\Jobs;

use App\Models\LiveVideo;
use App\Services\LiveVideoRecordingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessLiveVideoRecording implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public LiveVideo $liveVideo
    ) {
        // Set queue delay to allow streaming service to finalize recording
        $this->delay(now()->addMinutes(2));
    }

    /**
     * Execute the job.
     */
    public function handle(LiveVideoRecordingService $recordingService): void
    {
        try {
            Log::info('Processing live video recording', [
                'live_video_id' => $this->liveVideo->id,
                'status' => $this->liveVideo->status
            ]);

            // Only process if the live video has ended and recording was enabled
            if ($this->liveVideo->status !== 'ended' || !$this->liveVideo->is_recording_enabled) {
                Log::info('Skipping recording processing - conditions not met', [
                    'live_video_id' => $this->liveVideo->id,
                    'status' => $this->liveVideo->status,
                    'recording_enabled' => $this->liveVideo->is_recording_enabled
                ]);
                return;
            }

            // Check if recording already exists
            if ($this->liveVideo->recorded_media_id) {
                Log::info('Recording already exists, skipping processing', [
                    'live_video_id' => $this->liveVideo->id,
                    'media_id' => $this->liveVideo->recorded_media_id
                ]);
                return;
            }

            // Process the recording
            $recordingService->autoProcessRecording($this->liveVideo);

            Log::info('Live video recording processing completed', [
                'live_video_id' => $this->liveVideo->id
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process live video recording', [
                'live_video_id' => $this->liveVideo->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Live video recording processing job failed permanently', [
            'live_video_id' => $this->liveVideo->id,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);

        // Update live video metadata to indicate processing failed
        $this->liveVideo->update([
            'metadata' => array_merge($this->liveVideo->metadata ?? [], [
                'recording_processing_failed' => true,
                'recording_processing_failed_at' => now()->toISOString(),
                'recording_processing_error' => $exception->getMessage()
            ])
        ]);
    }
}
