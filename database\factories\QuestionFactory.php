<?php

namespace Database\Factories;

use App\Models\Exam\Question;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuestionFactory extends Factory
{
    protected $model = Question::class;

    public function definition(): array
    {
        return [
            'question_text' => $this->faker->sentence() . '?',
            'question_type' => $this->faker->randomElement(['multiple_choice', 'short_answer', 'essay']),
            'points' => $this->faker->numberBetween(1, 10),
            'explanation' => $this->faker->optional(0.7)->paragraph(),
            'difficulty_level' => $this->faker->randomElement(['easy', 'medium', 'hard']),
            'tags' => $this->faker->optional(0.5)->words(3),
            'is_active' => true,
        ];
    }

    public function multipleChoice(): static
    {
        return $this->state(fn (array $attributes) => [
            'question_type' => 'multiple_choice',
        ]);
    }

    public function shortAnswer(): static
    {
        return $this->state(fn (array $attributes) => [
            'question_type' => 'short_answer',
        ]);
    }

    public function essay(): static
    {
        return $this->state(fn (array $attributes) => [
            'question_type' => 'essay',
        ]);
    }

    public function easy(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'easy',
            'points' => $this->faker->numberBetween(1, 3),
        ]);
    }

    public function medium(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'medium',
            'points' => $this->faker->numberBetween(3, 6),
        ]);
    }

    public function hard(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'hard',
            'points' => $this->faker->numberBetween(6, 10),
        ]);
    }
}
