<?php

namespace Database\Factories;

use App\Models\Assignment;
use Illuminate\Database\Eloquent\Factories\Factory;

class AssignmentFactory extends Factory
{
    protected $model = Assignment::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'type' => $this->faker->randomElement(['homework', 'quiz', 'test', 'project', 'essay']),
            'total_score' => $this->faker->numberBetween(50, 100),
            'due_date' => $this->faker->dateTimeBetween('now', '+2 weeks'),
            'description' => $this->faker->paragraph(),
            'instructions' => $this->faker->paragraphs(2, true),
            'is_active' => true,
        ];
    }
}
