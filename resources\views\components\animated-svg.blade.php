{{-- Animated SVG Components --}}

{{-- Floating Particles Background --}}
@if($type === 'floating-particles')
<div class="floating-particles">
    @for($i = 1; $i <= 9; $i++)
    <div class="particle" style="
        width: {{ rand(4, 12) }}px; 
        height: {{ rand(4, 12) }}px; 
        left: {{ rand(0, 100) }}%; 
        animation-delay: {{ rand(0, 5) }}s;
        animation-duration: {{ rand(6, 10) }}s;
    "></div>
    @endfor
</div>
@endif

{{-- Animated Wave Divider --}}
@if($type === 'wave-divider')
<svg class="w-full h-16" viewBox="0 0 1200 120" preserveAspectRatio="none">
    <path class="wave-svg" d="M0,50 Q300,10 600,50 T1200,50 L1200,120 L0,120 Z" 
          fill="{{ $color ?? 'rgba(6, 182, 212, 0.1)' }}"/>
</svg>
@endif

{{-- Pulsing Orb --}}
@if($type === 'pulsing-orb')
<svg class="w-16 h-16 pulse-glow-svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="30" 
            fill="{{ $color ?? '#06b6d4' }}" 
            opacity="0.7"/>
    <circle cx="50" cy="50" r="20" 
            fill="{{ $color ?? '#3b82f6' }}" 
            opacity="0.9"/>
    <circle cx="50" cy="50" r="10" 
            fill="white" 
            opacity="1"/>
</svg>
@endif

{{-- Orbital System --}}
@if($type === 'orbital-system')
<div class="relative w-32 h-32">
    <svg class="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        <!-- Central orb -->
        <circle cx="50" cy="50" r="8" fill="{{ $color ?? '#06b6d4' }}" class="pulse-glow-svg"/>
        
        <!-- Orbital rings -->
        <circle cx="50" cy="50" r="25" fill="none" stroke="{{ $color ?? '#06b6d4' }}" 
                stroke-width="1" opacity="0.3"/>
        <circle cx="50" cy="50" r="35" fill="none" stroke="{{ $color ?? '#3b82f6' }}" 
                stroke-width="1" opacity="0.2"/>
        
        <!-- Orbiting particles -->
        <g class="orbital-animation" style="animation-duration: 4s;">
            <circle cx="75" cy="50" r="3" fill="{{ $color ?? '#8b5cf6' }}"/>
        </g>
        <g class="orbital-animation" style="animation-duration: 6s; animation-direction: reverse;">
            <circle cx="85" cy="50" r="2" fill="{{ $color ?? '#ec4899' }}"/>
        </g>
    </svg>
</div>
@endif

{{-- Morphing Blob --}}
@if($type === 'morphing-blob')
<svg class="w-24 h-24" viewBox="0 0 100 100">
    <path class="morph-shape" 
          fill="{{ $color ?? 'rgba(6, 182, 212, 0.3)' }}"
          d="M20,20 C20,20 50,20 50,20 C80,20 80,50 80,50 C80,80 50,80 50,80 C20,80 20,50 20,50 Z"/>
</svg>
@endif

{{-- DNA Helix --}}
@if($type === 'dna-helix')
<svg class="w-16 h-32 dna-helix" viewBox="0 0 50 100">
    <path d="M10,10 Q25,25 40,40 Q25,55 10,70 Q25,85 40,100" 
          stroke="{{ $color ?? '#06b6d4' }}" stroke-width="2" fill="none"/>
    <path d="M40,10 Q25,25 10,40 Q25,55 40,70 Q25,85 10,100" 
          stroke="{{ $color ?? '#3b82f6' }}" stroke-width="2" fill="none"/>
    
    <!-- Connection lines -->
    @for($i = 0; $i < 5; $i++)
    <line x1="10" y1="{{ 20 + $i * 20 }}" x2="40" y2="{{ 20 + $i * 20 }}" 
          stroke="{{ $color ?? '#8b5cf6' }}" stroke-width="1" opacity="0.6"/>
    @endfor
</svg>
@endif

{{-- Geometric Spinner --}}
@if($type === 'geometric-spinner')
<svg class="w-16 h-16 spiral-svg" viewBox="0 0 100 100">
    <polygon points="50,15 65,35 85,35 70,50 75,70 50,60 25,70 30,50 15,35 35,35" 
             fill="{{ $color ?? '#06b6d4' }}" opacity="0.8"/>
    <polygon points="50,25 60,40 75,40 65,50 70,65 50,55 30,65 35,50 25,40 40,40" 
             fill="{{ $color ?? '#3b82f6' }}" opacity="0.6"/>
</svg>
@endif

{{-- Heartbeat Monitor --}}
@if($type === 'heartbeat')
<svg class="w-32 h-16 heartbeat-svg" viewBox="0 0 200 80">
    <path d="M10,40 L30,40 L35,20 L40,60 L45,30 L50,50 L55,40 L190,40" 
          stroke="{{ $color ?? '#06b6d4' }}" stroke-width="2" fill="none" class="draw-svg"/>
    <circle cx="100" cy="40" r="3" fill="{{ $color ?? '#ec4899' }}" class="pulse-glow-svg"/>
</svg>
@endif

{{-- Liquid Loading --}}
@if($type === 'liquid-loading')
<div class="relative w-16 h-16">
    <svg class="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="45" fill="none" stroke="{{ $color ?? '#e5e7eb' }}" stroke-width="4"/>
        <circle cx="50" cy="50" r="45" fill="none" stroke="{{ $color ?? '#06b6d4' }}" 
                stroke-width="4" stroke-linecap="round" 
                stroke-dasharray="283" stroke-dashoffset="75"
                class="spin-animation" style="animation-duration: 2s;"/>
    </svg>
    <div class="absolute inset-2 liquid-blob" 
         style="background: {{ $color ?? 'rgba(6, 182, 212, 0.2)' }};"></div>
</div>
@endif

{{-- Kaleidoscope Pattern --}}
@if($type === 'kaleidoscope')
<svg class="w-24 h-24 kaleidoscope-svg" viewBox="0 0 100 100">
    <g transform="translate(50,50)">
        @for($i = 0; $i < 6; $i++)
        <g transform="rotate({{ $i * 60 }})">
            <polygon points="0,0 20,10 15,25 5,20" 
                     fill="{{ $color ?? '#06b6d4' }}" opacity="0.7"/>
            <polygon points="0,0 15,15 10,30 0,25" 
                     fill="{{ $color ?? '#3b82f6' }}" opacity="0.5"/>
        </g>
        @endfor
    </g>
</svg>
@endif

{{-- Magnetic Field Lines --}}
@if($type === 'magnetic-field')
<svg class="w-32 h-24" viewBox="0 0 200 120">
    @for($i = 0; $i < 5; $i++)
    <path d="M20,{{ 20 + $i * 20 }} Q100,{{ 10 + $i * 20 }} 180,{{ 20 + $i * 20 }}" 
          stroke="{{ $color ?? '#06b6d4' }}" stroke-width="1" fill="none" 
          class="magnetic-field" style="animation-delay: {{ $i * 0.2 }}s;"/>
    @endfor
</svg>
@endif

{{-- Pendulum Clock --}}
@if($type === 'pendulum')
<svg class="w-16 h-24" viewBox="0 0 60 100">
    <line x1="30" y1="10" x2="30" y2="20" stroke="{{ $color ?? '#64748b' }}" stroke-width="2"/>
    <g class="pendulum-svg" transform-origin="30 20">
        <line x1="30" y1="20" x2="30" y2="80" stroke="{{ $color ?? '#06b6d4' }}" stroke-width="2"/>
        <circle cx="30" cy="80" r="8" fill="{{ $color ?? '#3b82f6' }}"/>
    </g>
</svg>
@endif

{{-- Glitch Text Effect --}}
@if($type === 'glitch-text')
<div class="relative">
    <svg class="w-32 h-8 glitch-svg" viewBox="0 0 200 40">
        <text x="100" y="25" text-anchor="middle" font-family="monospace" font-size="16" 
              fill="{{ $color ?? '#06b6d4' }}">{{ $text ?? 'LOADING' }}</text>
    </svg>
</div>
@endif

{{-- Breathing Circle --}}
@if($type === 'breathing-circle')
<svg class="w-20 h-20 breathing-svg" viewBox="0 0 100 100">
    <circle cx="50" cy="50" r="40" fill="none" 
            stroke="{{ $color ?? '#06b6d4' }}" stroke-width="2" opacity="0.8"/>
    <circle cx="50" cy="50" r="30" fill="none" 
            stroke="{{ $color ?? '#3b82f6' }}" stroke-width="2" opacity="0.6"/>
    <circle cx="50" cy="50" r="20" fill="none" 
            stroke="{{ $color ?? '#8b5cf6' }}" stroke-width="2" opacity="0.4"/>
    <circle cx="50" cy="50" r="10" 
            fill="{{ $color ?? '#ec4899' }}" opacity="0.8"/>
</svg>
@endif

{{-- Elastic Bounce Icon --}}
@if($type === 'elastic-bounce')
<svg class="w-12 h-12 elastic-bounce" viewBox="0 0 100 100">
    <rect x="25" y="25" width="50" height="50" rx="10"
          fill="{{ $color ?? '#06b6d4' }}" opacity="0.8"/>
    <rect x="35" y="35" width="30" height="30" rx="5"
          fill="{{ $color ?? '#3b82f6' }}" opacity="0.9"/>
    <circle cx="50" cy="50" r="8" fill="white"/>
</svg>
@endif

{{-- Custom Downloaded SVG Example --}}
@if($type === 'custom-icon')
<div class="w-{{ $size ?? '16' }} h-{{ $size ?? '16' }} {{ $animation ?? 'pulse-glow-svg' }}">
    @php
        $iconName = $icon ?? 'default';
        $iconColor = $color ?? 'currentColor';
    @endphp

    @if($iconName === 'default')
        <svg viewBox="0 0 24 24" fill="none" stroke="{{ $iconColor }}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <path d="m9 12 2 2 4-4"/>
        </svg>
    @elseif($iconName === 'education')
        <svg viewBox="0 0 24 24" fill="none" stroke="{{ $iconColor }}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
        </svg>
    @elseif($iconName === 'download')
        <svg viewBox="0 0 24 24" fill="none" stroke="{{ $iconColor }}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
        </svg>
    @else
        {{-- Try to include the file, fallback to default if not found --}}
        @php
            $iconPath = 'components.svg.icons.' . $iconName;
            $iconExists = view()->exists($iconPath);
        @endphp

        @if($iconExists)
            @include($iconPath, ['color' => $iconColor])
        @else
            {{-- Fallback icon --}}
            <svg viewBox="0 0 24 24" fill="none" stroke="{{ $iconColor }}" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="m9 12 2 2 4-4"/>
            </svg>
        @endif
    @endif
</div>
@endif

{{-- External SVG File --}}
@if($type === 'external-svg')
<div class="w-{{ $size ?? '16' }} h-{{ $size ?? '16' }} {{ $animation ?? '' }}">
    <img src="{{ asset('images/svg/' . ($file ?? 'default.svg')) }}"
         alt="{{ $alt ?? 'SVG Icon' }}"
         class="w-full h-full"
         style="filter: {{ $filter ?? 'none' }};">
</div>
@endif

{{-- Inline Custom SVG --}}
@if($type === 'inline-custom')
<svg class="w-{{ $size ?? '16' }} h-{{ $size ?? '16' }} {{ $animation ?? '' }}"
     viewBox="{{ $viewBox ?? '0 0 24 24' }}"
     fill="{{ $fill ?? 'none' }}"
     stroke="{{ $color ?? 'currentColor' }}">
    {!! $svgContent ?? '<circle cx="12" cy="12" r="10"/>' !!}
</svg>
@endif
