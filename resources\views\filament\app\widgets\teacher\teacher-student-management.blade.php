<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-users class="w-5 h-5 text-primary-600 mr-2" />
                จัดการจำนวนนักเรียน
            </div>
        </x-slot>

        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <!-- Left Column - Form -->
            <div class="md:col-span-1">
                <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                    <h3 class="font-medium mb-4 text-gray-900 dark:text-white">เพิ่ม/ลบนักเรียน</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ระดับชั้น/ห้อง</label>
                            <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                <option value="">เลือกระดับชั้น/ห้อง</option>
                                @foreach($this->getClasses() as $class)
                                    <option value="{{ $class['value'] }}" {{ $class['label'] === $this->getCurrentClass() ? 'selected' : '' }}>
                                        {{ $class['label'] }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div class="flex space-x-2">
                            <button class="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md transition-colors">
                                <x-heroicon-o-circle-stack class="w-4 h-4 mr-2 inline" />
                                ดึงรายชื่อ
                            </button>
                            <button class="flex-1 bg-success-600 hover:bg-success-700 text-white py-2 px-4 rounded-md transition-colors">
                                <x-heroicon-o-plus class="w-4 h-4 mr-2 inline" />
                                เพิ่มรายชื่อ
                            </button>
                        </div>
                        
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                            <h4 class="font-medium mb-3 text-gray-900 dark:text-white">เพิ่มนักเรียนใหม่</h4>
                            
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">เลขประจำตัว</label>
                                    <input type="text" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ชื่อ-นามสกุล</label>
                                    <input type="text" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">เลขที่</label>
                                    <input type="number" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                </div>
                                
                                <button class="w-full bg-success-600 hover:bg-success-700 text-white py-2 px-4 rounded-md transition-colors">
                                    <x-heroicon-o-user-plus class="w-4 h-4 mr-2 inline" />
                                    เพิ่มนักเรียน
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column - Student List -->
            <div class="md:col-span-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="font-medium text-gray-900 dark:text-white">รายชื่อนักเรียน {{ $this->getCurrentClass() }}</h3>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <input type="text" placeholder="ค้นหานักเรียน..." class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50 pl-8">
                            <x-heroicon-o-magnifying-glass class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        </div>
                        <button class="bg-primary-100 hover:bg-primary-200 text-primary-700 dark:bg-primary-900/50 dark:hover:bg-primary-900/70 dark:text-primary-300 py-1 px-3 rounded-md text-sm transition-colors">
                            <x-heroicon-o-funnel class="w-4 h-4 mr-1 inline" />
                            กรอง
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
                    <table class="min-w-full">
                        <thead>
                            <tr class="bg-gray-100 dark:bg-gray-700">
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">เลขที่</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">เลขประจำตัว</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">ชื่อ-นามสกุล</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">วิชาที่ลงทะเบียน</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">สถานะ</th>
                                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">จัดการ</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                            @foreach($this->getStudents() as $student)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $student['number'] }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $student['student_id'] }}</td>
                                    <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $student['name'] }}</td>
                                    <td class="py-3 px-4 text-sm">
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($student['subjects'] as $subject)
                                                <span class="text-xs px-2 py-0.5 rounded
                                                    @if($subject['color'] === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                                    @elseif($subject['color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                                    @elseif($subject['color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                                    @elseif($subject['color'] === 'purple') bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200
                                                    @endif">
                                                    {{ $subject['name'] }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </td>
                                    <td class="py-3 px-4 text-sm">
                                        <span class="text-xs px-2 py-0.5 rounded-full
                                            @if($student['status_color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                            @elseif($student['status_color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                            @endif">
                                            {{ $student['status_label'] }}
                                        </span>
                                    </td>
                                    <td class="py-3 px-4 text-sm">
                                        <div class="flex space-x-2">
                                            <button class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200">
                                                <x-heroicon-o-pencil class="w-4 h-4" />
                                            </button>
                                            <button class="text-danger-600 hover:text-danger-800 dark:text-danger-400 dark:hover:text-danger-200">
                                                <x-heroicon-o-trash class="w-4 h-4" />
                                            </button>
                                            <button class="text-success-600 hover:text-success-800 dark:text-success-400 dark:hover:text-success-200">
                                                <x-heroicon-o-eye class="w-4 h-4" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-600 dark:text-gray-400">แสดง 1-{{ $this->getTotalStudents() }} จาก {{ $this->getTotalStudents() }} รายการ</div>
                    <div class="flex space-x-1">
                        <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50" disabled>
                            <x-heroicon-o-chevron-left class="w-4 h-4" />
                        </button>
                        <button class="bg-primary-600 text-white px-3 py-1 rounded-md">1</button>
                        <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600">
                            <x-heroicon-o-chevron-right class="w-4 h-4" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
