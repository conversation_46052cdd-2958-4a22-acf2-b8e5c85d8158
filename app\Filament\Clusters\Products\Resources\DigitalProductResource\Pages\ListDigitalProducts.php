<?php

namespace App\Filament\Clusters\Products\Resources\DigitalProductResource\Pages;

use App\Filament\Clusters\Products\Resources\DigitalProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDigitalProducts extends ListRecords
{
    protected static string $resource = DigitalProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DigitalProductResource\Widgets\DigitalProductStats::class,
            DigitalProductResource\Widgets\BrandCategoryStats::class,
        ];
    }
}
