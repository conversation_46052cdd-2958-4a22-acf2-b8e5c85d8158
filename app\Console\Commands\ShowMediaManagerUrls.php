<?php

namespace App\Console\Commands;

use App\Models\MediaManager\Folder;
use App\Models\Team;
use Illuminate\Console\Command;

class ShowMediaManagerUrls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:show-urls';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show Media Manager URLs for accessing team folders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Media Manager URLs ===');

        $baseUrl = config('app.url');
        $panelPath = '/admin'; // Adjust if your admin panel has a different path

        $this->line("\n--- Main Media Manager ---");
        $this->info("Folders: {$baseUrl}{$panelPath}/folders");

        $this->line("\n--- Team Folders ---");
        $teams = Team::all();
        
        foreach ($teams as $team) {
            $this->line("\nTeam: {$team->name} (ID: {$team->id})");
            
            $teamFolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $team->id)
                ->where('parent_id', null) // Main folders only
                ->get();
            
            if ($teamFolders->isEmpty()) {
                $this->comment("  No main folders found for this team");
            } else {
                foreach ($teamFolders as $folder) {
                    $folderUrl = "{$baseUrl}{$panelPath}/media?folder_id={$folder->id}";
                    $this->info("  📁 {$folder->name}: {$folderUrl}");
                    
                    // Show subfolders
                    $subfolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                        ->where('parent_id', $folder->id)
                        ->get();
                    
                    foreach ($subfolders as $subfolder) {
                        $subfolderUrl = "{$baseUrl}{$panelPath}/media?folder_id={$subfolder->id}";
                        $this->line("    📂 {$subfolder->name}: {$subfolderUrl}");
                    }
                }
            }
        }

        $this->line("\n--- Quick Access Links ---");
        $this->info("To access Media Manager:");
        $this->line("1. Go to: {$baseUrl}{$panelPath}/folders");
        $this->line("2. Click on a team folder to see its contents");
        $this->line("3. Use the folder actions to upload files");

        $this->line("\n--- Direct Team Folder Access ---");
        foreach ($teams as $team) {
            $mainFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $team->id)
                ->where('parent_id', null)
                ->first();
            
            if ($mainFolder) {
                $directUrl = "{$baseUrl}{$panelPath}/media?folder_id={$mainFolder->id}";
                $this->info("Team {$team->name}: {$directUrl}");
            }
        }

        $this->line("\n--- Troubleshooting ---");
        $this->comment("If you don't see folders:");
        $this->line("1. Make sure you're logged in as a user with the correct team");
        $this->line("2. Check if team-based filtering is working");
        $this->line("3. Try accessing folders directly using the URLs above");
        $this->line("4. Run: php artisan media:check-data to verify data exists");
    }
}
