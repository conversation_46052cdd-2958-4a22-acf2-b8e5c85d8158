<?php

namespace App\Filament\App\Widgets\Parent;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class UpcomingEventsWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.parent.upcoming-events';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('parent');
    }

    public function getEvents(): array
    {
        // Placeholder data - replace with actual events
        return [
            [
                'title' => 'ประชุมผู้ปกครอง',
                'date' => '20 ม.ค. 2567',
                'time' => '14:00 น.',
                'location' => 'หอประชุมโรงเรียน',
                'type' => 'meeting',
                'priority' => 'high',
                'color' => 'danger'
            ],
            [
                'title' => 'งานแสดงผลงานวิทยาศาสตร์',
                'date' => '25 ม.ค. 2567',
                'time' => '10:00 น.',
                'location' => 'โรงยิม',
                'type' => 'event',
                'priority' => 'medium',
                'color' => 'info'
            ],
            [
                'title' => 'วันหยุดโรงเรียน',
                'date' => '30 ม.ค. 2567',
                'time' => 'ตลอดทั้งวัน',
                'location' => '-',
                'type' => 'holiday',
                'priority' => 'low',
                'color' => 'success'
            ],
        ];
    }
}
