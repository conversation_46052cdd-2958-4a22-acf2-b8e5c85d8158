<?php

namespace App\Filament\Resources\TaskResource\Pages;

use App\Filament\Resources\TaskResource;
use App\Models\TeachingSchedule;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Table;


class ListTasks extends ListRecords
{
    protected static string $resource = TaskResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('calendar')
                ->label('Calendar View')
                ->icon('heroicon-o-calendar-days')
                ->url(fn (): string => TaskResource::getUrl('index'))
                ->color('info'),
            Actions\CreateAction::make(),
            Actions\Action::make('create_schedule')
                ->label('New Teaching Schedule')
                ->icon('heroicon-o-calendar-days')
                ->url(fn (): string => \App\Filament\Resources\TeachingScheduleResource::getUrl('create'))
                ->color('success')
                ->visible(fn (): bool => $this->activeTab === 'schedules'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'tasks' => Tab::make('Tasks')
                ->icon('heroicon-o-clipboard-document-list')
                ->badge(fn () => \App\Models\Task::query()->where('team_id', filament()->getTenant()->id)->count()),
            'schedules' => Tab::make('Teaching Schedules')
                ->icon('heroicon-o-calendar-days')
                ->badge(fn () => TeachingSchedule::query()->where('team_id', filament()->getTenant()->id)->count()),
        ];
    }

    public function table(Table $table): Table
    {
        if ($this->activeTab === 'schedules') {
            return $this->getTeachingScheduleTable($table);
        }

        return parent::table($table);
    }

    protected function getTeachingScheduleTable(Table $table): Table
    {
        return $table
            ->query(TeachingSchedule::query()->where('team_id', filament()->getTenant()->id))
            ->columns([
                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('classroom.room_name')
                    ->label('Classroom')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('teacher.name')
                    ->label('Teacher')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('lesson.title')
                    ->label('Lesson')
                    ->sortable()
                    ->searchable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Start Time')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('End Time')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'scheduled' => 'warning',
                        'in_progress' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\IconColumn::make('is_recurring')
                    ->label('Recurring')
                    ->boolean(),
                Tables\Columns\TextColumn::make('notes')
                    ->label('Notes')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name'),
                Tables\Filters\SelectFilter::make('classroom_id')
                    ->label('Classroom')
                    ->relationship('classroom', 'room_name'),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\TernaryFilter::make('is_recurring')
                    ->label('Recurring'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn (TeachingSchedule $record): string => \App\Filament\Resources\TeachingScheduleResource::getUrl('view', ['record' => $record])),
                Tables\Actions\EditAction::make()
                    ->url(fn (TeachingSchedule $record): string => \App\Filament\Resources\TeachingScheduleResource::getUrl('edit', ['record' => $record])),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('start_time', 'asc');
    }
}
