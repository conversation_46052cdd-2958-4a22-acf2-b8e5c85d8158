<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\LiveVideo;
use App\Services\LiveVideoRecordingService;

class LiveStreamController extends Controller
{
    public function stream(LiveVideo $liveVideo)
    {
        // Check if user has permission to view this stream
        if (!$this->canViewStream($liveVideo)) {
            abort(403, 'You do not have permission to view this stream.');
        }

        return view('live-streaming.stream', compact('liveVideo'));
    }

    public function watch(LiveVideo $liveVideo)
    {
        // Check if user has permission to watch this stream
        if (!$this->canWatchStream($liveVideo)) {
            abort(403, 'You do not have permission to watch this stream.');
        }

        return view('live-streaming.watch', compact('liveVideo'));
    }

    public function updateStatus(Request $request, LiveVideo $liveVideo)
    {
        $request->validate([
            'status' => 'required|in:scheduled,live,ended,cancelled'
        ]);

        // Check if user has permission to control this stream
        if (!$this->canControlStream($liveVideo)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $updateData = ['status' => $request->status];

        if ($request->status === 'live' && !$liveVideo->actual_start_time) {
            $updateData['actual_start_time'] = now();
        } elseif ($request->status === 'ended' && !$liveVideo->actual_end_time) {
            $updateData['actual_end_time'] = now();
        }

        $liveVideo->update($updateData);

        return response()->json(['success' => true]);
    }

    public function uploadRecording(Request $request, LiveVideoRecordingService $recordingService)
    {
        $request->validate([
            'recording' => 'required|file|mimes:webm,mp4,avi|max:1048576', // 1GB max
            'live_video_id' => 'required|exists:live_videos,id'
        ]);

        $liveVideo = LiveVideo::findOrFail($request->live_video_id);

        // Check if user has permission to upload recording for this stream
        if (!$this->canControlStream($liveVideo)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        try {
            $media = $recordingService->processRecording($liveVideo, $request->file('recording'));

            return response()->json([
                'success' => true,
                'media_id' => $media->id,
                'path' => $liveVideo->recording_path
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to upload recording: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getViewerCount(LiveVideo $liveVideo)
    {
        // This would typically connect to a real-time service
        // For now, return a mock count
        return response()->json([
            'viewer_count' => 1, // This would be dynamic in a real implementation
            'status' => $liveVideo->status
        ]);
    }

    private function canViewStream(LiveVideo $liveVideo): bool
    {
        $user = auth()->user();

        // Teacher/creator can always view
        if ($liveVideo->user_id === $user->id) {
            return true;
        }

        // Team admin can view
        if ($user->hasRole('team_admin') && $user->team_id === $liveVideo->team_id) {
            return true;
        }

        // Super admin can view all
        if ($user->hasRole('super_admin')) {
            return true;
        }

        return false;
    }

    private function canWatchStream(LiveVideo $liveVideo): bool
    {
        $user = auth()->user();

        // Public streams can be watched by anyone
        if ($liveVideo->is_public) {
            return true;
        }

        // Check if user is in the same team
        if ($user->team_id === $liveVideo->team_id) {
            return true;
        }

        // Check allowed roles
        if ($liveVideo->allowed_roles && $user->hasAnyRole($liveVideo->allowed_roles)) {
            return true;
        }

        return $this->canViewStream($liveVideo);
    }

    private function canControlStream(LiveVideo $liveVideo): bool
    {
        $user = auth()->user();

        // Teacher/creator can control
        if ($liveVideo->user_id === $user->id) {
            return true;
        }

        // Team admin can control
        if ($user->hasRole('team_admin') && $user->team_id === $liveVideo->team_id) {
            return true;
        }

        // Super admin can control all
        if ($user->hasRole('super_admin')) {
            return true;
        }

        return false;
    }
}
