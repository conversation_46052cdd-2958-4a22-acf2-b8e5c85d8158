<?php

namespace App\Filament\App\Widgets\Parent;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ParentCalendarWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.parent.parent-calendar';
    
    protected int | string | array $columnSpan = 'full';

    public $currentMonth;
    public $currentYear;

    public function mount()
    {
        $this->currentMonth = Carbon::now()->month;
        $this->currentYear = Carbon::now()->year;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('parent');
    }

    public function getCalendarData(): array
    {
        $startOfMonth = Carbon::create($this->currentYear, $this->currentMonth, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        // Get first day of calendar (might be from previous month)
        $startOfCalendar = $startOfMonth->copy()->startOfWeek(Carbon::SUNDAY);
        
        // Get last day of calendar (might be from next month)
        $endOfCalendar = $endOfMonth->copy()->endOfWeek(Carbon::SATURDAY);
        
        $days = [];
        $current = $startOfCalendar->copy();
        
        while ($current <= $endOfCalendar) {
            $days[] = [
                'date' => $current->day,
                'is_current_month' => $current->month === $this->currentMonth,
                'is_today' => $current->isToday(),
                'events' => $this->getEventsForDate($current),
                'full_date' => $current->format('Y-m-d')
            ];
            $current->addDay();
        }
        
        return [
            'month_name' => $startOfMonth->locale('th')->translatedFormat('F Y'),
            'days' => $days,
            'day_names' => ['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส']
        ];
    }

    private function getEventsForDate(Carbon $date): array
    {
        // Placeholder data - replace with actual events
        $events = [];
        
        if ($date->day === 15) {
            $events = [
                ['type' => 'parent-meeting', 'title' => 'ประชุมผู้ปกครอง', 'time' => '19:00', 'color' => 'blue'],
            ];
        } elseif ($date->day === 18) {
            $events = [
                ['type' => 'payment-due', 'title' => 'ครบกำหนดชำระค่าเทอม', 'time' => 'ตลอดทั้งวัน', 'color' => 'red'],
            ];
        } elseif ($date->day === 20) {
            $events = [
                ['type' => 'school-event', 'title' => 'งานแสดงผลงานนักเรียน', 'time' => '10:00', 'color' => 'green'],
            ];
        } elseif ($date->day === 25) {
            $events = [
                ['type' => 'holiday', 'title' => 'วันหยุดโรงเรียน', 'time' => 'ตลอดทั้งวัน', 'color' => 'purple'],
            ];
        }
        
        return $events;
    }

    public function getTodayActivities(): array
    {
        // Placeholder data - replace with actual today's activities for parents
        return [
            [
                'type' => 'child-progress',
                'title' => 'น้องมายด์ทำแบบฝึกหัดคณิตศาสตร์เสร็จแล้ว',
                'time' => '2 ชั่วโมงที่แล้ว',
                'child' => 'น้องมายด์',
                'subject' => 'คณิตศาสตร์',
                'score' => '92/100',
                'color' => 'green',
                'actions' => [
                    ['label' => 'ดูผลงาน', 'color' => 'green', 'icon' => 'heroicon-o-eye'],
                    ['label' => 'ส่งกำลังใจ', 'color' => 'blue', 'icon' => 'heroicon-o-heart'],
                ]
            ],
            [
                'type' => 'teacher-message',
                'title' => 'ครูมีข้อความใหม่เกี่ยวกับน้องมิกกี้',
                'time' => '4 ชั่วโมงที่แล้ว',
                'child' => 'น้องมิกกี้',
                'teacher' => 'ครูสมหญิง',
                'subject' => 'ภาษาไทย',
                'color' => 'blue',
                'actions' => [
                    ['label' => 'อ่านข้อความ', 'color' => 'blue', 'icon' => 'heroicon-o-chat-bubble-left-right'],
                    ['label' => 'ตอบกลับ', 'color' => 'purple', 'icon' => 'heroicon-o-pencil'],
                ]
            ],
            [
                'type' => 'payment-reminder',
                'title' => 'แจ้งเตือน: ค่าเทอมครบกำหนดใน 3 วัน',
                'time' => 'วันนี้',
                'amount' => '2,500 บาท',
                'due_date' => '18 พ.ค. 2566',
                'color' => 'red',
                'actions' => [
                    ['label' => 'ชำระเงิน', 'color' => 'red', 'icon' => 'heroicon-o-credit-card'],
                    ['label' => 'ดูรายละเอียด', 'color' => 'gray', 'icon' => 'heroicon-o-document-text'],
                ]
            ],
        ];
    }

    public function previousMonth()
    {
        if ($this->currentMonth === 1) {
            $this->currentMonth = 12;
            $this->currentYear--;
        } else {
            $this->currentMonth--;
        }
    }

    public function nextMonth()
    {
        if ($this->currentMonth === 12) {
            $this->currentMonth = 1;
            $this->currentYear++;
        } else {
            $this->currentMonth++;
        }
    }
}
