<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add fields for multiple social account connections
            $table->json('social_accounts')->nullable()->after('provider_refresh_token');
            $table->timestamp('social_accounts_updated_at')->nullable()->after('social_accounts');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['social_accounts', 'social_accounts_updated_at']);
        });
    }
};
