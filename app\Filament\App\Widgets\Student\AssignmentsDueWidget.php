<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class AssignmentsDueWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.assignments-due';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getAssignments(): array
    {
        // Placeholder data - replace with actual assignments
        return [
            [
                'title' => 'การบ้านคณิตศาสตร์ บทที่ 5',
                'subject' => 'คณิตศาสตร์',
                'due_date' => '15 ม.ค. 2567',
                'status' => 'pending',
                'priority' => 'high',
                'color' => 'danger'
            ],
            [
                'title' => 'รายงานการทดลองวิทยาศาสตร์',
                'subject' => 'วิทยาศาสตร์',
                'due_date' => '18 ม.ค. 2567',
                'status' => 'in_progress',
                'priority' => 'medium',
                'color' => 'warning'
            ],
            [
                'title' => 'เรียงความภาษาอังกฤษ',
                'subject' => 'ภาษาอังกฤษ',
                'due_date' => '20 ม.ค. 2567',
                'status' => 'not_started',
                'priority' => 'low',
                'color' => 'info'
            ],
        ];
    }
}
