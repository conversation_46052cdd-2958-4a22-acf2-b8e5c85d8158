<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ClearSettingsCache extends Command
{
    protected $signature = 'settings:clear-cache {team_id?} {--all} {--nuclear} {--force}';
    protected $description = 'Clear settings cache for a specific team or all teams. Use --nuclear to clear all Laravel cache as well (requires --force for safety)';

    public function handle()
    {
        $teamId = $this->argument('team_id');
        $clearAll = $this->option('all');
        $nuclear = $this->option('nuclear');

        if ($nuclear) {
            $force = $this->option('force');

            if (!$force) {
                $this->error('⚠️  Nuclear option requires --force flag for safety!');
                $this->info('This will clear ALL application cache including:');
                $this->info('  - Config cache');
                $this->info('  - Route cache');
                $this->info('  - View cache');
                $this->info('  - Application cache');
                $this->info('  - All team settings cache');
                $this->info('  - OPcache (if available)');
                $this->info('');
                $this->info('Use: php artisan settings:clear-cache --nuclear --force');
                return 1;
            }

            $this->warn('🚀 Nuclear option: Clearing ALL cache (Laravel + Settings)...');

            // Clear Laravel caches
            $this->call('config:clear');
            $this->call('route:clear');
            $this->call('view:clear');
            $this->call('cache:clear');

            // Clear settings cache
            clear_all_settings_cache();

            // Clear OPcache if available
            if (function_exists('opcache_reset')) {
                opcache_reset();
                $this->info('✅ OPcache cleared!');
            }

            $this->info('✅ ALL cache cleared successfully! (Config, Routes, Views, Application Cache, Settings Cache)');
            return 0;
        }

        if ($clearAll) {
            $this->info('Clearing all settings cache...');
            clear_all_settings_cache();
            $this->info('✅ All settings cache cleared successfully!');
            return 0;
        }

        if ($teamId) {
            $this->info("Clearing settings cache for team ID: {$teamId}");
            clear_team_settings_cache($teamId);
            $this->info("✅ Settings cache cleared for team {$teamId}!");
        } else {
            $this->info('Clearing global settings cache...');
            clear_team_settings_cache(null);
            $this->info('✅ Global settings cache cleared!');
        }

        return 0;
    }
}
