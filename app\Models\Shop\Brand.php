<?php

namespace App\Models\Shop;

use App\Models\Address;
use App\Traits\BelongsToTeam;
use App\Traits\HasTeamScopedPolymorphicRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property string $name
 * @property string $slug
 * @property string|null $website
 * @property string|null $description
 * @property int $position
 * @property bool $is_visible
 * @property string|null $seo_title
 * @property string|null $seo_description
 * @property int|null $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Address> $addresses
 * @property-read int|null $addresses_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \App\Models\MediaManager\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Product> $products
 * @property-read int|null $products_count
 * @property-read \App\Models\Team|null $team
 * @method static \Database\Factories\Shop\BrandFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand forCurrentTeam()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand forTeam(\App\Models\Team $team)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereIsVisible($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereSeoDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereSeoTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Brand whereWebsite($value)
 * @mixin \Eloquent
 */
class Brand extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use BelongsToTeam;
    use HasTeamScopedPolymorphicRelations;

    /**
     * @var string
     */
    protected $table = 'shop_brands';

    /**
     * @var array<string, string>
     */
    protected $casts = [
        'is_visible' => 'boolean',
    ];

    /** @return MorphToMany<Address> */
    public function addresses(): MorphToMany
    {
        return $this->morphToManyWithoutTeamScope(Address::class, 'addressable', 'addressables');
    }

    /** @return HasMany<Product> */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'shop_brand_id');
    }
}
















    
    
    
    

    
     
     
    

    
     
     
    
        
    

    
    
    
        
    

    
    
    
        
    

use Illuminate\Database\Eloquent\Relations\BelongsTo;