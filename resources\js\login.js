// Login Page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all login page components
    initializeFormValidation();
    initializeSocialLogins();
    initializePhoneAuth();
    initializeFormSubmission();
    
    // Form validation
    function initializeFormValidation() {
        const form = document.getElementById('loginForm');
        const inputs = form.querySelectorAll('.form-input');
        
        inputs.forEach(input => {
            input.addEventListener('blur', validateField);
            input.addEventListener('input', clearErrors);
        });
        
        function validateField(e) {
            const field = e.target;
            const value = field.value.trim();
            const fieldName = field.name;
            
            clearFieldErrors(field);
            
            switch (fieldName) {
                case 'email':
                    if (!value) {
                        showFieldError(field, 'กรุณากรอกอีเมล');
                    } else if (!isValidEmail(value)) {
                        showFieldError(field, 'รูปแบบอีเมลไม่ถูกต้อง');
                    } else {
                        showFieldSuccess(field);
                    }
                    break;
                    
                case 'password':
                    if (!value) {
                        showFieldError(field, 'กรุณากรอกรหัสผ่าน');
                    } else if (value.length < 6) {
                        showFieldError(field, 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร');
                    } else {
                        showFieldSuccess(field);
                    }
                    break;
            }
        }
        
        function clearErrors(e) {
            const field = e.target;
            if (field.classList.contains('error')) {
                clearFieldErrors(field);
            }
        }
        
        function showFieldError(field, message) {
            field.classList.add('error');
            field.classList.remove('success');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${message}
            `;
            
            field.parentNode.appendChild(errorDiv);
        }
        
        function showFieldSuccess(field) {
            field.classList.add('success');
            field.classList.remove('error');
        }
        
        function clearFieldErrors(field) {
            field.classList.remove('error', 'success');
            const errorMsg = field.parentNode.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.remove();
            }
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    }
    
    // Social login handlers
    function initializeSocialLogins() {
        // Get all social login buttons dynamically
        const socialButtons = document.querySelectorAll('.social-button:not([id="phoneLogin"])');

        socialButtons.forEach(button => {
            const provider = button.id.replace('Login', '');
            button.addEventListener('click', () => handleSocialLogin(provider));
        });

        function handleSocialLogin(provider) {
            const button = document.getElementById(provider + 'Login');
            showButtonLoading(button, 'กำลังเชื่อมต่อ...');

            // Redirect to social auth route
            window.location.href = `/auth/${provider}`;
        }
    }
    
    // Phone authentication
    function initializePhoneAuth() {
        const phoneBtn = document.getElementById('phoneLogin');
        const phoneModal = document.getElementById('phoneModal');
        const closeModalBtn = document.getElementById('closePhoneModal');
        const sendOtpBtn = document.getElementById('sendOtp');
        const verifyOtpBtn = document.getElementById('verifyOtp');
        const phoneInput = document.getElementById('phoneNumber');
        const otpInputs = document.querySelectorAll('.otp-input');
        
        if (phoneBtn) {
            phoneBtn.addEventListener('click', function() {
                phoneModal.classList.remove('hidden');
                phoneInput.focus();
            });
        }
        
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                phoneModal.classList.add('hidden');
                resetPhoneModal();
            });
        }
        
        // Close modal when clicking outside
        if (phoneModal) {
            phoneModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                    resetPhoneModal();
                }
            });
        }
        
        if (sendOtpBtn) {
            sendOtpBtn.addEventListener('click', handleSendOtp);
        }
        
        if (verifyOtpBtn) {
            verifyOtpBtn.addEventListener('click', handleVerifyOtp);
        }
        
        // OTP input handling
        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                if (e.target.value.length === 1 && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            });
            
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Backspace' && e.target.value === '' && index > 0) {
                    otpInputs[index - 1].focus();
                }
            });
        });
        
        async function handleSendOtp() {
            const phoneNumber = phoneInput.value.trim();
            const countryCode = document.getElementById('countryCode').value;
            
            if (!phoneNumber) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('กรุณากรอกหมายเลขโทรศัพท์', 'error');
                }
                return;
            }
            
            if (!isValidPhoneNumber(phoneNumber)) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('หมายเลขโทรศัพท์ไม่ถูกต้อง', 'error');
                }
                return;
            }
            
            showButtonLoading(sendOtpBtn, 'กำลังส่ง OTP...');
            
            try {
                const response = await fetch('/api/auth/phone/send-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        phone: phoneNumber,
                        country_code: countryCode
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('phoneStep').classList.add('hidden');
                    document.getElementById('otpStep').classList.remove('hidden');
                    
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification('ส่ง OTP ไปยัง ' + countryCode + phoneNumber + ' แล้ว', 'success');
                    }
                    
                    otpInputs[0].focus();
                } else {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification(data.message || 'เกิดข้อผิดพลาด', 'error');
                    }
                }
            } catch (error) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('เกิดข้อผิดพลาดในการส่ง OTP', 'error');
                }
            }
            
            resetButtonLoading(sendOtpBtn, 'ส่ง OTP');
        }
        
        async function handleVerifyOtp() {
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            const phoneNumber = phoneInput.value.trim();
            const countryCode = document.getElementById('countryCode').value;
            
            if (otp.length !== 6) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('กรุณากรอก OTP ให้ครบ 6 หลัก', 'error');
                }
                return;
            }
            
            showButtonLoading(verifyOtpBtn, 'กำลังตรวจสอบ...');
            
            try {
                const response = await fetch('/api/auth/phone/verify-otp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        otp: otp,
                        phone: countryCode + phoneNumber
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification(data.message, 'success');
                    }
                    
                    setTimeout(() => {
                        window.location.href = data.redirect || '/dashboard';
                    }, 1500);
                } else {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification(data.message || 'รหัส OTP ไม่ถูกต้อง', 'error');
                    }
                    otpInputs.forEach(input => input.value = '');
                    otpInputs[0].focus();
                }
            } catch (error) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('เกิดข้อผิดพลาดในการตรวจสอบ OTP', 'error');
                }
            }
            
            resetButtonLoading(verifyOtpBtn, 'ยืนยัน OTP');
        }
        
        function resetPhoneModal() {
            document.getElementById('phoneStep').classList.remove('hidden');
            document.getElementById('otpStep').classList.add('hidden');
            phoneInput.value = '';
            otpInputs.forEach(input => input.value = '');
        }
        
        function isValidPhoneNumber(phone) {
            const phoneRegex = /^[0-9]{9,10}$/;
            return phoneRegex.test(phone);
        }
    }
    
    // Form submission
    function initializeFormSubmission() {
        const form = document.getElementById('loginForm');
        const submitBtn = document.getElementById('submitBtn');
        
        if (form) {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (validateForm()) {
                    handleFormSubmission();
                }
            });
        }
        
        function validateForm() {
            const inputs = form.querySelectorAll('.form-input[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                const event = new Event('blur');
                input.dispatchEvent(event);
                
                if (input.classList.contains('error') || !input.value.trim()) {
                    isValid = false;
                }
            });
            
            return isValid;
        }
        
        async function handleFormSubmission() {
            showButtonLoading(submitBtn, 'กำลังเข้าสู่ระบบ...');

            try {
                const formData = new FormData(form);

                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification(data.message, 'success');
                    }

                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1500);
                } else {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification(data.message || 'เกิดข้อผิดพลาด', 'error');
                    }

                    // Show validation errors
                    if (data.errors) {
                        Object.keys(data.errors).forEach(field => {
                            const input = form.querySelector(`[name="${field}"]`);
                            if (input) {
                                showFieldError(input, data.errors[field][0]);
                            }
                        });
                    }
                }
            } catch (error) {
                if (window.LayoutUtils) {
                    window.LayoutUtils.showNotification('เกิดข้อผิดพลาดในการเข้าสู่ระบบ', 'error');
                }
            }

            resetButtonLoading(submitBtn, 'เข้าสู่ระบบ');
        }
    }
    
    // Utility functions
    function showButtonLoading(button, text) {
        button.disabled = true;
        button.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                ${text}
            </div>
        `;
    }
    
    function resetButtonLoading(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }
});
