# Team-Based File Organization Implementation

## Overview

This implementation provides automatic team-based file organization where:
- Each team gets its own physical folder structure when created
- All file uploads automatically get `team_id` and `parent_id` metadata
- Files are organized in team-specific paths with proper folder structure

## Features Implemented

### 1. Automatic Team Folder Creation

When a team is created, the system automatically:
- Creates physical directories: `storage/app/public/teams/{team_id}/`
- Creates subfolders: `Products/`, `Avatars/`, `Documents/`, `Media/`
- Creates corresponding database folder records with proper metadata

### 2. Team-Based File Path Generation

Files are automatically stored in team-specific paths:
```
teams/{team_id}/{category}/{media_id}/filename.ext
```

Examples:
- Product images: `teams/3/Products/80/product-image.jpg`
- User avatars: `teams/3/Avatars/25/user-avatar.png`
- Documents: `teams/3/Documents/15/document.pdf`

### 3. Automatic Metadata Assignment

All uploaded files automatically receive:
- `team_id`: Based on current tenant or user's team
- `parent_id`: Reference to the appropriate folder
- Custom properties including team name, creator info, etc.

## Implementation Details

### Files Created/Modified

1. **Team Observer** (`app/Observers/TeamObserver.php`)
   - Automatically creates folder structure when teams are created
   - Handles team updates and deletions
   - Manages physical directory creation

2. **Media Path Generator** (`app/Services/MediaPathGenerator.php`)
   - Custom path generator for Spatie Media Library
   - Generates team-based paths based on collection and team_id
   - Maps collections to appropriate subfolders

3. **Media Library Event Listener** (`app/Listeners/MediaLibraryEventListener.php`)
   - Listens for media upload events
   - Automatically sets team_id and metadata for all uploads
   - Handles different model types (Products, Users, etc.)

4. **Enhanced Media Model** (`app/Models/MediaManager/Media.php`)
   - Auto-sets team_id and user fields during creation
   - Adds team_id to custom properties for path generation

5. **Updated Media Actions** (`app/Filament/Resources/MediaManager/Actions/CreateMediaAction.php`)
   - Enhanced to include parent folder references
   - Adds comprehensive metadata to uploaded files

### Configuration Changes

1. **Media Library Config** (`config/media-library.php`)
   - Registered custom path generator
   - Enables team-based file organization

2. **Event Service Provider** (`app/Providers/EventServiceProvider.php`)
   - Registered media library event listener

3. **App Service Provider** (`app/Providers/AppServiceProvider.php`)
   - Registered Team observer

### Folder Structure Mapping

Collections are automatically mapped to folders:
- `product-images`, `digital-files` → `Products/`
- `avatars`, `profile-images`, `user_avatars` → `Avatars/`
- `documents`, `files` → `Documents/`
- `media`, `images`, `default` → `Media/`
- Team-specific collections (e.g., `team_3_products`) → Appropriate subfolder

## Usage Examples

### Product Image Upload
When a product uploads an image:
1. File is stored in `teams/{team_id}/Products/{media_id}/`
2. Media record gets `team_id` from product
3. Custom properties include team name, product info, etc.

### User Avatar Upload
When a user uploads an avatar:
1. File is stored in `teams/{team_id}/Avatars/{media_id}/`
2. Media record gets `team_id` from user
3. Custom properties include user info, team name, etc.

### Media Manager Upload
When uploading through media manager:
1. File is stored based on selected folder
2. Parent folder ID is recorded in custom properties
3. Team context is automatically applied

## Commands Available

### Create Team Folders
```bash
php artisan media:create-team-folders [--force]
```
Creates folder structure for all existing teams.

### Test Media Upload
```bash
php artisan media:test-upload [team_id]
```
Tests the media upload functionality for a specific team.

## Benefits

1. **Organized Storage**: Files are logically organized by team and category
2. **Automatic Assignment**: No manual intervention needed for team/folder assignment
3. **Scalable**: Works with any number of teams and file types
4. **Traceable**: Full metadata tracking for all uploads
5. **Flexible**: Easy to extend for new file types or categories

## File Path Examples

```
storage/app/public/
├── teams/
│   ├── 1/                    # Team 1 (Default Team)
│   │   ├── Products/
│   │   ├── Avatars/
│   │   ├── Documents/
│   │   └── Media/
│   ├── 2/                    # Team 2 (Admin Team)
│   │   ├── Products/
│   │   │   └── 45/           # Media ID 45
│   │   │       └── product.jpg
│   │   ├── Avatars/
│   │   │   └── 23/           # Media ID 23
│   │   │       └── avatar.png
│   │   ├── Documents/
│   │   └── Media/
│   └── 3/                    # Team 3 (บดินเดชา)
│       ├── Products/
│       │   └── 80/           # Media ID 80
│       │       └── test-image.jpg
│       ├── Avatars/
│       ├── Documents/
│       └── Media/
```

This implementation ensures that all file uploads are properly organized, tracked, and associated with the correct team and folder structure automatically.
