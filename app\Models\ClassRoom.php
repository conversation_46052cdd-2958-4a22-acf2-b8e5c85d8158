<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class ClassRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'grade_level',
        'room_number',
        'room_name',
        'remark',
        'is_active',
        'capacity',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'capacity' => 'integer',
    ];

    /**
     * Get the team that owns the classroom
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Scope a query to only include active classrooms
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by grade level
     */
    public function scopeGradeLevel(Builder $query, string $gradeLevel): Builder
    {
        return $query->where('grade_level', $gradeLevel);
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get the display name combining room number and name
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->room_number} - {$this->room_name}";
    }

    /**
     * Get the full classroom identifier
     */
    public function getFullIdentifierAttribute(): string
    {
        return "{$this->grade_level} | {$this->room_number} - {$this->room_name}";
    }

    /**
     * Check if classroom is at capacity
     */
    public function isAtCapacity(int $currentStudents = 0): bool
    {
        return $this->capacity && $currentStudents >= $this->capacity;
    }
}
