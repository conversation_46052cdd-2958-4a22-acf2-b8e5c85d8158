<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SvgDemoEnabled
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if SVG demo is enabled via environment variable
        $enabled = env('SVG_DEMO_ENABLED', true);
        
        if (!$enabled) {
            abort(404, 'SVG Demo page is disabled');
        }

        return $next($request);
    }
}
