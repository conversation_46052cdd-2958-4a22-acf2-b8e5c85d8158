<?php

namespace App\Forms\Components;

use App\Models\MediaManager\Folder;
use App\Models\MediaManager\Media;
use Filament\Facades\Filament;
use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;
use Illuminate\Database\Eloquent\Builder;

class TeamMediaManagerInput extends MediaManagerInput
{
    /**
     * Override the default behavior to filter by team
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Add team filtering to the component
        $this->modifyQueryUsing(function (Builder $query) {
            return $this->applyTeamFiltering($query);
        });
    }

    /**
     * Apply team and user filtering to the query
     */
    protected function applyTeamFiltering(Builder $query): Builder
    {
        $user = auth()->user();

        if (!$user) {
            return $query->whereRaw('1 = 0'); // No results for unauthenticated users
        }

        // Super admins can see all media (no filtering)
        if ($user->hasRole('super_admin') && !Filament::getTenant()) {
            return $query;
        }

        // Apply team filtering first
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        } elseif ($user->team_id) {
            $query->where('team_id', $user->team_id);
        }

        // Apply user-level filtering
        if (!$user->hasRole('super_admin') && !$user->hasRole('team_admin')) {
            // Regular users can only see their own media
            $query->where(function ($query) use ($user) {
                $query->where('created_by', $user->id)
                      ->orWhere('user_id', $user->id);
            });
        }

        return $query;
    }

    /**
     * Get folders filtered by team and user
     */
    public function getTeamFolders(): \Illuminate\Database\Eloquent\Collection
    {
        $query = Folder::query();
        $user = auth()->user();

        if (!$user) {
            return collect(); // No folders for unauthenticated users
        }

        // Super admin without tenant can see all folders
        if ($user->hasRole('super_admin') && !Filament::getTenant()) {
            return $query->withoutGlobalScopes(['team_folder', 'user_folder'])->get();
        }

        // Team filtering is handled by global scopes
        // User filtering is also handled by global scopes
        return $query->get();
    }

    /**
     * Get media filtered by team and user
     */
    public function getTeamMedia(?int $folderId = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = Media::query();
        $user = auth()->user();

        if (!$user) {
            return collect(); // No media for unauthenticated users
        }

        // Super admin without tenant can see all media
        if ($user->hasRole('super_admin') && !Filament::getTenant()) {
            $query = $query->withoutGlobalScopes(['team_media', 'user_media']);
        }

        // Apply folder filtering if specified
        if ($folderId) {
            $folder = Folder::find($folderId);
            if ($folder) {
                if (!$folder->model_type) {
                    $query->where('collection_name', $folder->collection);
                } else {
                    $query->where('model_type', $folder->model_type)
                          ->where('model_id', $folder->model_id)
                          ->where('collection_name', $folder->collection);
                }
            }
        }

        return $query->get();
    }

    /**
     * Static method to create the component with team filtering
     */
    public static function make(string $name): static
    {
        $component = parent::make($name);
        
        // Ensure team_id is automatically set when creating new media
        $component->mutateDehydratedStateUsing(function ($state, $component) {
            if (is_array($state)) {
                foreach ($state as &$item) {
                    if (is_array($item) && !isset($item['team_id'])) {
                        $user = auth()->user();
                        
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $item['team_id'] = Filament::getTenant()->id;
                        } elseif ($user && $user->team_id) {
                            $item['team_id'] = $user->team_id;
                        }
                    }
                }
            }
            
            return $state;
        });

        return $component;
    }

    /**
     * Configure the component to work with team-based media
     */
    public function teamAware(): static
    {
        $this->configure();
        
        return $this;
    }

    /**
     * Configure team-specific settings
     */
    public function configure(): static
    {
        // Add any additional team-specific configuration here
        $this->afterStateUpdated(function ($state, $set, $get) {
            // Ensure team_id is set for new media items
            $user = auth()->user();
            $teamId = null;

            if (Filament::hasTenancy() && Filament::getTenant()) {
                $teamId = Filament::getTenant()->id;
            } elseif ($user && $user->team_id) {
                $teamId = $user->team_id;
            }

            if ($teamId && is_array($state)) {
                foreach ($state as $key => $item) {
                    if (is_array($item) && !isset($item['team_id'])) {
                        $state[$key]['team_id'] = $teamId;
                    }
                }
                $set($this->getName(), $state);
            }
        });

        return $this;
    }
}
