<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Subject extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'name',
        'code',
        'description',
        'color',
        'credits',
        'level',
        'grade_levels',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'grade_levels' => 'array',
        'credits' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the team that owns the subject
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the books for this subject
     */
    public function books(): HasMany
    {
        return $this->hasMany(\App\Models\Book::class);
    }

    /**
     * Get the courses for this subject
     */
    public function courses(): HasMany
    {
        return $this->hasMany(\App\Models\Course::class);
    }

    /**
     * Get the assignments for this subject
     */
    public function assignments(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Scope a query to only include active subjects
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by level
     */
    public function scopeLevel(Builder $query, string $level): Builder
    {
        return $query->where('level', $level);
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get the display name with code
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->code ? "{$this->code} - {$this->name}" : $this->name;
    }

    /**
     * Get formatted grade levels
     */
    public function getFormattedGradeLevelsAttribute(): string
    {
        if (!$this->grade_levels || !is_array($this->grade_levels)) {
            return '';
        }

        return implode(', ', $this->grade_levels);
    }
}
