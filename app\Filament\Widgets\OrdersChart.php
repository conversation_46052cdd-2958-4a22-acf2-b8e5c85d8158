<?php

namespace App\Filament\Widgets;

use App\Models\Shop\Order;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Widgets\ChartWidget;

class OrdersChart extends ChartWidget
{
    protected static ?string $heading = 'Orders per Month';

    protected static ?int $sort = 1;

    protected function getType(): string
    {
        return 'line';
    }

    protected function getData(): array
    {
        $currentTenant = Filament::getTenant();

        // Get order data for the last 12 months, filtered by current tenant
        $data = [];
        $labels = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $labels[] = $date->format('M');

            $query = Order::whereYear('created_at', $date->year)
                         ->whereMonth('created_at', $date->month);

            if ($currentTenant) {
                $query->where('team_id', $currentTenant->id);
            }

            $data[] = $query->count();
        }

        $teamName = $currentTenant ? $currentTenant->name : 'All Teams';

        return [
            'datasets' => [
                [
                    'label' => "Orders - {$teamName}",
                    'data' => $data,
                    'fill' => 'start',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'borderColor' => 'rgb(16, 185, 129)',
                ],
            ],
            'labels' => $labels,
        ];
    }

    public function getHeading(): ?string
    {
        $currentTenant = Filament::getTenant();
        $teamName = $currentTenant ? $currentTenant->name : 'All Teams';

        return "Orders per Month - {$teamName}";
    }
}
