<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class AddSpecificationsColumn extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:add-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add missing columns to shop_products table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Add specifications column if it doesn't exist
        if (!Schema::hasColumn('shop_products', 'specifications')) {
            Schema::table('shop_products', function ($table) {
                $table->json('specifications')->nullable()->after('system_requirements');
            });
            $this->info('Specifications column added successfully!');
        } else {
            $this->info('Specifications column already exists.');
        }

        // Add wholesale_pricing column if it doesn't exist
        if (!Schema::hasColumn('shop_products', 'wholesale_pricing')) {
            Schema::table('shop_products', function ($table) {
                $table->json('wholesale_pricing')->nullable()->after('cost');
            });
            $this->info('Wholesale pricing column added successfully!');
        } else {
            $this->info('Wholesale pricing column already exists.');
        }

        // Add product_variants column if it doesn't exist
        if (!Schema::hasColumn('shop_products', 'product_variants')) {
            Schema::table('shop_products', function ($table) {
                $table->json('product_variants')->nullable()->after('wholesale_pricing');
            });
            $this->info('Product variants column added successfully!');
        } else {
            $this->info('Product variants column already exists.');
        }

        // Add variant option label columns
        $labelColumns = ['variant_option1_label', 'variant_option2_label', 'variant_option3_label'];
        foreach ($labelColumns as $column) {
            if (!Schema::hasColumn('shop_products', $column)) {
                Schema::table('shop_products', function ($table) use ($column) {
                    $table->string($column)->nullable()->after('product_variants');
                });
                $this->info("$column column added successfully!");
            } else {
                $this->info("$column column already exists.");
            }
        }
    }
}
