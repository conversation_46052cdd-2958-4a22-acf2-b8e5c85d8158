<?php

namespace App\Filament\Plugins;

use TomatoPHP\FilamentMediaManager\FilamentMediaManagerPlugin;
use Filament\Panel;

class CustomMediaManagerPlugin extends FilamentMediaManagerPlugin
{
    public function register(Panel $panel): void
    {
        // Set the plugin as active but don't register resources
        if(class_exists(\Nwidart\Modules\Module::class) && \Nwidart\Modules\Facades\Module::find('FilamentMediaManager')?->isEnabled()){
            $this->isActive = true;
        }
        else {
            $this->isActive = true;
        }

        // Don't register the original resources - we'll use our custom ones
        // The resources will be discovered by the panel's discoverResources method
    }

    public static function make(): static
    {
        return new static();
    }
}
