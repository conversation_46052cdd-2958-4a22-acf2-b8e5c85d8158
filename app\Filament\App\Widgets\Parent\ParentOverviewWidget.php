<?php

namespace App\Filament\App\Widgets\Parent;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class ParentOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('ลูกของฉัน', '2 คน')
                ->description('นักเรียนที่ลงทะเบียน')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('ค่าใช้จ่ายค้างชำระ', '2,500 บาท')
                ->description('ครบกำหนดเดือนนี้')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('warning'),

            Stat::make('กิจกรรมที่จะมาถึง', '3 กิจกรรม')
                ->description('สัปดาห์นี้')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info'),

            Stat::make('ข้อความ', '5 ข้อความ')
                ->description('ยังไม่ได้อ่านจากครู')
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('primary'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('parent');
    }
}
