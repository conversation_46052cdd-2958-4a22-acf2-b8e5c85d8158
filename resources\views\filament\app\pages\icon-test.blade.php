<x-filament-panels::page>
    <div class="space-y-8">
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 dark:bg-yellow-900/20 dark:border-yellow-800">
            <div class="flex">
                <x-heroicon-o-exclamation-triangle class="h-5 w-5 text-yellow-400" />
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Development Tool
                    </h3>
                    <p class="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                        This page is only visible in development environments. Use it to test which Heroicons are available in your Filament installation.
                    </p>
                </div>
            </div>
        </div>

        @foreach($this->getIconList() as $category => $icons)
            <div class="bg-white rounded-lg shadow dark:bg-gray-800">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $category }}</h3>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        @foreach($icons as $icon)
                            <div class="flex flex-col items-center p-3 rounded-lg border border-gray-200 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700">
                                <div class="mb-2">
                                    @try
                                        <x-dynamic-component 
                                            :component="'heroicon-o-' . $icon" 
                                            class="h-8 w-8 text-gray-600 dark:text-gray-400" 
                                        />
                                        <div class="mt-1 text-xs text-green-600 dark:text-green-400">✓</div>
                                    @catch(Exception $e)
                                        <div class="h-8 w-8 bg-red-100 rounded flex items-center justify-center dark:bg-red-900">
                                            <x-heroicon-o-x-mark class="h-4 w-4 text-red-600 dark:text-red-400" />
                                        </div>
                                        <div class="mt-1 text-xs text-red-600 dark:text-red-400">✗</div>
                                    @endtry
                                </div>
                                
                                <div class="text-center">
                                    <p class="text-xs font-mono text-gray-900 dark:text-white break-all">
                                        {{ $icon }}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        heroicon-o-{{ $icon }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 dark:bg-blue-900/20 dark:border-blue-800">
            <div class="flex">
                <x-heroicon-o-information-circle class="h-5 w-5 text-blue-400" />
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Usage Instructions
                    </h3>
                    <div class="mt-1 text-sm text-blue-700 dark:text-blue-300">
                        <p class="mb-2">Icons with a green checkmark (✓) are available and safe to use.</p>
                        <p class="mb-2">Icons with a red X (✗) are not available in your current installation.</p>
                        <p class="mb-2">Copy the full component name (e.g., <code class="bg-blue-100 px-1 rounded dark:bg-blue-800">heroicon-o-home</code>) to use in your templates.</p>
                        <p>You can also use the solid variant by replacing <code class="bg-blue-100 px-1 rounded dark:bg-blue-800">heroicon-o-</code> with <code class="bg-blue-100 px-1 rounded dark:bg-blue-800">heroicon-s-</code></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-800">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Quick Copy Examples:</h4>
            <div class="space-y-1 text-sm font-mono">
                <div class="flex items-center space-x-2">
                    <x-heroicon-o-home class="h-4 w-4" />
                    <code class="bg-white px-2 py-1 rounded dark:bg-gray-700">&lt;x-heroicon-o-home class="h-6 w-6" /&gt;</code>
                </div>
                <div class="flex items-center space-x-2">
                    <x-heroicon-o-user class="h-4 w-4" />
                    <code class="bg-white px-2 py-1 rounded dark:bg-gray-700">&lt;x-heroicon-o-user class="h-6 w-6" /&gt;</code>
                </div>
                <div class="flex items-center space-x-2">
                    <x-heroicon-o-cog-6-tooth class="h-4 w-4" />
                    <code class="bg-white px-2 py-1 rounded dark:bg-gray-700">&lt;x-heroicon-o-cog-6-tooth class="h-6 w-6" /&gt;</code>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
