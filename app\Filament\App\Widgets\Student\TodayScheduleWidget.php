<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TodayScheduleWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.today-schedule';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getTodayClasses(): array
    {
        // Placeholder data - replace with actual schedule
        return [
            [
                'subject' => 'คณิตศาสตร์',
                'teacher' => 'ครูสมชาย',
                'time' => '08:00 - 09:00',
                'room' => 'ห้อง 101',
                'status' => 'upcoming',
                'color' => 'primary'
            ],
            [
                'subject' => 'วิทยาศาสตร์',
                'teacher' => 'ครูสมหญิง',
                'time' => '09:15 - 10:15',
                'room' => 'ห้องปฏิบัติการ 1',
                'status' => 'current',
                'color' => 'success'
            ],
            [
                'subject' => 'ภาษาอังกฤษ',
                'teacher' => 'ครูจิรา',
                'time' => '10:30 - 11:30',
                'room' => 'ห้อง 203',
                'status' => 'upcoming',
                'color' => 'info'
            ],
        ];
    }
}
