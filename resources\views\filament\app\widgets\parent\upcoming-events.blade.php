<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-calendar-days class="w-5 h-5 text-primary-600 mr-2" />
                📅 กิจกรรมที่จะมาถึง
            </div>
        </x-slot>

        <div class="space-y-4">
            @foreach($this->getEvents() as $event)
                <div class="p-4 rounded-lg border-l-4 transition-all duration-200 hover:shadow-md
                    @if($event['priority'] === 'high') border-red-500 bg-red-50 dark:bg-red-900/20
                    @elseif($event['priority'] === 'medium') border-blue-500 bg-blue-50 dark:bg-blue-900/20
                    @else border-green-500 bg-green-50 dark:bg-green-900/20
                    @endif">
                    
                    <div class="flex justify-between items-start">
                        <div class="flex items-start space-x-3">
                            <div class="p-2 rounded-lg
                                @if($event['type'] === 'meeting') bg-red-100 dark:bg-red-900/50
                                @elseif($event['type'] === 'event') bg-blue-100 dark:bg-blue-900/50
                                @else bg-green-100 dark:bg-green-900/50
                                @endif">
                                @if($event['type'] === 'meeting')
                                    <x-heroicon-o-users class="w-5 h-5 text-red-600 dark:text-red-400" />
                                @elseif($event['type'] === 'event')
                                    <x-heroicon-o-star class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                                @else
                                    <x-heroicon-o-sun class="w-5 h-5 text-green-600 dark:text-green-400" />
                                @endif
                            </div>
                            
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 dark:text-white">{{ $event['title'] }}</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                    📍 {{ $event['location'] }}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
                                    🕐 {{ $event['date'] }} เวลา {{ $event['time'] }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex flex-col items-end space-y-2">
                            <span class="px-3 py-1 rounded-full text-xs font-medium
                                @if($event['priority'] === 'high') bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200
                                @elseif($event['priority'] === 'medium') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                @else bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                @endif">
                                @if($event['priority'] === 'high') สำคัญมาก
                                @elseif($event['priority'] === 'medium') สำคัญ
                                @else ทั่วไป
                                @endif
                            </span>
                            
                            @if($event['type'] === 'meeting')
                                <button class="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-xs rounded-full transition-colors flex items-center">
                                    <x-heroicon-o-plus class="w-3 h-3 mr-1" />
                                    เพิ่มในปฏิทิน
                                </button>
                            @elseif($event['type'] === 'event')
                                <button class="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded-full transition-colors flex items-center">
                                    <x-heroicon-o-information-circle class="w-3 h-3 mr-1" />
                                    ดูรายละเอียด
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
