# Heroicons Reference for Filament

This is a comprehensive list of commonly available Heroicons that work reliably in Filament applications. All icons are available in both outline (`heroicon-o-`) and solid (`heroicon-s-`) variants.

## Usage in Blade Templates
```blade
<x-heroicon-o-home class="h-6 w-6" />
<x-heroicon-s-home class="h-6 w-6" />
```

## Navigation & Interface Icons

### Basic Navigation
- `heroicon-o-home` / `heroicon-s-home` - Home
- `heroicon-o-bars-3` / `heroicon-s-bars-3` - Menu/Hamburger
- `heroicon-o-x-mark` / `heroicon-s-x-mark` - Close/X
- `heroicon-o-chevron-left` / `heroicon-s-chevron-left` - Left arrow
- `heroicon-o-chevron-right` / `heroicon-s-chevron-right` - Right arrow
- `heroicon-o-chevron-up` / `heroicon-s-chevron-up` - Up arrow
- `heroicon-o-chevron-down` / `heroicon-s-chevron-down` - Down arrow
- `heroicon-o-arrow-left` / `heroicon-s-arrow-left` - Back arrow
- `heroicon-o-arrow-right` / `heroicon-s-arrow-right` - Forward arrow

### Actions & Controls
- `heroicon-o-plus` / `heroicon-s-plus` - Add/Create
- `heroicon-o-minus` / `heroicon-s-minus` - Remove/Subtract
- `heroicon-o-pencil` / `heroicon-s-pencil` - Edit
- `heroicon-o-trash` / `heroicon-s-trash` - Delete
- `heroicon-o-eye` / `heroicon-s-eye` - View/Show
- `heroicon-o-eye-slash` / `heroicon-s-eye-slash` - Hide
- `heroicon-o-cog-6-tooth` / `heroicon-s-cog-6-tooth` - Settings
- `heroicon-o-adjustments-horizontal` / `heroicon-s-adjustments-horizontal` - Filters

## User & People Icons

### Users
- `heroicon-o-user` / `heroicon-s-user` - Single user
- `heroicon-o-users` / `heroicon-s-users` - Multiple users
- `heroicon-o-user-plus` / `heroicon-s-user-plus` - Add user
- `heroicon-o-user-minus` / `heroicon-s-user-minus` - Remove user
- `heroicon-o-user-circle` / `heroicon-s-user-circle` - User profile
- `heroicon-o-user-group` / `heroicon-s-user-group` - Team/Group

### Authentication
- `heroicon-o-key` / `heroicon-s-key` - Password/Security
- `heroicon-o-lock-closed` / `heroicon-s-lock-closed` - Locked
- `heroicon-o-lock-open` / `heroicon-s-lock-open` - Unlocked
- `heroicon-o-shield-check` / `heroicon-s-shield-check` - Security verified

## Education & Academic Icons

### Learning
- `heroicon-o-academic-cap` / `heroicon-s-academic-cap` - Education/Graduation
- `heroicon-o-book-open` / `heroicon-s-book-open` - Reading/Study
- `heroicon-o-bookmark` / `heroicon-s-bookmark` - Save/Bookmark
- `heroicon-o-clipboard` / `heroicon-s-clipboard` - Notes/Tasks
- `heroicon-o-clipboard-document` / `heroicon-s-clipboard-document` - Documents
- `heroicon-o-clipboard-document-list` / `heroicon-s-clipboard-document-list` - Lists

### Assessment
- `heroicon-o-star` / `heroicon-s-star` - Rating/Grade
- `heroicon-o-check` / `heroicon-s-check` - Correct/Complete
- `heroicon-o-check-circle` / `heroicon-s-check-circle` - Verified/Approved
- `heroicon-o-x-circle` / `heroicon-s-x-circle` - Error/Incorrect

## Communication Icons

### Messaging
- `heroicon-o-envelope` / `heroicon-s-envelope` - Email/Message
- `heroicon-o-envelope-open` / `heroicon-s-envelope-open` - Read message
- `heroicon-o-chat-bubble-left` / `heroicon-s-chat-bubble-left` - Chat
- `heroicon-o-chat-bubble-left-right` / `heroicon-s-chat-bubble-left-right` - Conversation
- `heroicon-o-phone` / `heroicon-s-phone` - Phone call
- `heroicon-o-megaphone` / `heroicon-s-megaphone` - Announcement

### Notifications
- `heroicon-o-bell` / `heroicon-s-bell` - Notification
- `heroicon-o-bell-slash` / `heroicon-s-bell-slash` - Muted notifications
- `heroicon-o-exclamation-triangle` / `heroicon-s-exclamation-triangle` - Warning
- `heroicon-o-information-circle` / `heroicon-s-information-circle` - Info

## Document & File Icons

### Documents
- `heroicon-o-document` / `heroicon-s-document` - Document/File
- `heroicon-o-document-text` / `heroicon-s-document-text` - Text document
- `heroicon-o-document-plus` / `heroicon-s-document-plus` - New document
- `heroicon-o-document-duplicate` / `heroicon-s-document-duplicate` - Copy document
- `heroicon-o-folder` / `heroicon-s-folder` - Folder
- `heroicon-o-folder-open` / `heroicon-s-folder-open` - Open folder

### Media
- `heroicon-o-photo` / `heroicon-s-photo` - Image/Picture
- `heroicon-o-video-camera` / `heroicon-s-video-camera` - Video
- `heroicon-o-musical-note` / `heroicon-s-musical-note` - Audio/Music
- `heroicon-o-film` / `heroicon-s-film` - Video file

## Time & Calendar Icons

### Time
- `heroicon-o-clock` / `heroicon-s-clock` - Time/Schedule
- `heroicon-o-calendar` / `heroicon-s-calendar` - Calendar/Date
- `heroicon-o-calendar-days` / `heroicon-s-calendar-days` - Calendar view

### Status
- `heroicon-o-play` / `heroicon-s-play` - Start/Play
- `heroicon-o-pause` / `heroicon-s-pause` - Pause
- `heroicon-o-stop` / `heroicon-s-stop` - Stop

## Business & Finance Icons

### Money
- `heroicon-o-banknotes` / `heroicon-s-banknotes` - Money/Payment
- `heroicon-o-credit-card` / `heroicon-s-credit-card` - Credit card
- `heroicon-o-currency-dollar` / `heroicon-s-currency-dollar` - Dollar sign
- `heroicon-o-receipt-percent` / `heroicon-s-receipt-percent` - Discount/Tax

### Commerce
- `heroicon-o-shopping-cart` / `heroicon-s-shopping-cart` - Shopping
- `heroicon-o-shopping-bag` / `heroicon-s-shopping-bag` - Purchase
- `heroicon-o-gift` / `heroicon-s-gift` - Gift/Reward

## Data & Analytics Icons

### Charts
- `heroicon-o-chart-bar` / `heroicon-s-chart-bar` - Bar chart
- `heroicon-o-chart-pie` / `heroicon-s-chart-pie` - Pie chart
- `heroicon-o-presentation-chart-line` / `heroicon-s-presentation-chart-line` - Line chart

### Data
- `heroicon-o-table-cells` / `heroicon-s-table-cells` - Table/Grid
- `heroicon-o-list-bullet` / `heroicon-s-list-bullet` - List
- `heroicon-o-squares-2x2` / `heroicon-s-squares-2x2` - Grid view

## Status & Feedback Icons

### Success & Error
- `heroicon-o-check-badge` / `heroicon-s-check-badge` - Verified
- `heroicon-o-exclamation-circle` / `heroicon-s-exclamation-circle` - Alert
- `heroicon-o-question-mark-circle` / `heroicon-s-question-mark-circle` - Help
- `heroicon-o-heart` / `heroicon-s-heart` - Like/Favorite

### Progress
- `heroicon-o-arrow-path` / `heroicon-s-arrow-path` - Refresh/Reload
- `heroicon-o-arrow-up-tray` / `heroicon-s-arrow-up-tray` - Upload
- `heroicon-o-arrow-down-tray` / `heroicon-s-arrow-down-tray` - Download

## Technology Icons

### Devices
- `heroicon-o-computer-desktop` / `heroicon-s-computer-desktop` - Desktop
- `heroicon-o-device-phone-mobile` / `heroicon-s-device-phone-mobile` - Mobile
- `heroicon-o-device-tablet` / `heroicon-s-device-tablet` - Tablet

### Connectivity
- `heroicon-o-wifi` / `heroicon-s-wifi` - WiFi
- `heroicon-o-signal` / `heroicon-s-signal` - Signal strength
- `heroicon-o-globe-alt` / `heroicon-s-globe-alt` - Internet/Web

## Location & Map Icons

### Places
- `heroicon-o-map-pin` / `heroicon-s-map-pin` - Location marker
- `heroicon-o-map` / `heroicon-s-map` - Map
- `heroicon-o-building-office` / `heroicon-s-building-office` - Office building
- `heroicon-o-home-modern` / `heroicon-s-home-modern` - House

## Utility Icons

### Tools
- `heroicon-o-wrench-screwdriver` / `heroicon-s-wrench-screwdriver` - Tools
- `heroicon-o-magnifying-glass` / `heroicon-s-magnifying-glass` - Search
- `heroicon-o-funnel` / `heroicon-s-funnel` - Filter
- `heroicon-o-squares-plus` / `heroicon-s-squares-plus` - Add item

### Miscellaneous
- `heroicon-o-tag` / `heroicon-s-tag` - Tag/Label
- `heroicon-o-link` / `heroicon-s-link` - Link/URL
- `heroicon-o-share` / `heroicon-s-share` - Share
- `heroicon-o-printer` / `heroicon-s-printer` - Print

## Tips for Using Heroicons

### 1. Consistent Sizing
```blade
<!-- Standard sizes -->
<x-heroicon-o-home class="h-4 w-4" />  <!-- Small -->
<x-heroicon-o-home class="h-5 w-5" />  <!-- Medium -->
<x-heroicon-o-home class="h-6 w-6" />  <!-- Large -->
<x-heroicon-o-home class="h-8 w-8" />  <!-- Extra Large -->
```

### 2. Color Classes
```blade
<!-- Using Tailwind color classes -->
<x-heroicon-o-home class="h-6 w-6 text-blue-600" />
<x-heroicon-o-home class="h-6 w-6 text-green-500" />
<x-heroicon-o-home class="h-6 w-6 text-red-500" />
```

### 3. In Filament Resources
```php
// In navigation
protected static ?string $navigationIcon = 'heroicon-o-home';

// In actions
Action::make('edit')
    ->icon('heroicon-o-pencil')
```

### 4. Fallback Strategy
If an icon doesn't work, try these alternatives:
- `heroicon-o-square-3-stack-3d` → `heroicon-o-squares-2x2`
- `heroicon-o-building-office-2` → `heroicon-o-building-office`
- `heroicon-o-chat-bubble-left-right` → `heroicon-o-chat-bubble-left`

### 5. Testing Icons
Create a simple test page to verify icons work:
```blade
<div class="grid grid-cols-6 gap-4 p-4">
    @foreach(['home', 'user', 'cog-6-tooth', 'envelope'] as $icon)
        <div class="text-center">
            <x-dynamic-component :component="'heroicon-o-' . $icon" class="h-8 w-8 mx-auto" />
            <p class="text-xs mt-1">{{ $icon }}</p>
        </div>
    @endforeach
</div>
```

## Additional Tools Created

### 1. Icon Test Page
Visit `/app/{your-team}/icon-test` (only visible in development) to see which icons are available in your installation.

### 2. Icon Helper Class
```php
use App\Helpers\IconHelper;

// Get a safe icon with fallback
$safeIcon = IconHelper::getSafeIcon('building-office-2'); // Returns 'home'

// Get full component name
$component = IconHelper::getComponent('user', false); // Returns 'heroicon-o-user'

// Check if icon is safe
$isSafe = IconHelper::isSafe('home'); // Returns true

// Get random icon for testing
$randomIcon = IconHelper::random();
```

### 3. Safe Icon Component
```blade
<!-- Use the safe icon component with automatic fallbacks -->
<x-safe-icon icon="building-office-2" class="h-6 w-6" />
<x-safe-icon icon="user" class="h-8 w-8" solid />
```

### 4. Console Command
```bash
# List available icons in table format
php artisan heroicons:list

# Output as JSON
php artisan heroicons:list --format=json

# Output as Blade components
php artisan heroicons:list --format=blade
```

## Best Practices

### 1. Always Test New Icons
Before using a new icon in production, test it on the Icon Test page or use the IconHelper class.

### 2. Use the Safe Icon Component
For dynamic icons or when you're unsure about availability:
```blade
<x-safe-icon icon="{{ $dynamicIcon }}" class="h-6 w-6" />
```

### 3. Stick to the Safe List
Use icons from the safe list in this document to avoid errors.

### 4. Have Fallbacks Ready
When using icons programmatically, always have a fallback:
```php
$icon = IconHelper::getSafeIcon($userProvidedIcon);
```

This comprehensive reference and toolset should help you avoid icon-related errors in the future!
