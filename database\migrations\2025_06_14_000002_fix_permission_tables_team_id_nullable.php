<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $tableNames = config('permission.table_names');
        $columnNames = config('permission.column_names');
        $teams = config('permission.teams');

        if (!$teams) {
            return; // Skip if teams are not enabled
        }

        $teamColumn = $columnNames['team_foreign_key'] ?? 'team_id';

        // Fix model_has_permissions table
        if (Schema::hasTable($tableNames['model_has_permissions'])) {
            // Drop the primary key constraint first
            DB::statement("ALTER TABLE `{$tableNames['model_has_permissions']}` DROP PRIMARY KEY");
            
            // Modify the team_id column to allow NULL
            DB::statement("ALTER TABLE `{$tableNames['model_has_permissions']}` MODIFY `{$teamColumn}` BIGINT UNSIGNED NULL");
            
            // Recreate the primary key (now allowing NULL team_id)
            $pivotPermission = $columnNames['permission_pivot_key'] ?? 'permission_id';
            $modelMorphKey = $columnNames['model_morph_key'] ?? 'model_id';
            
            DB::statement("ALTER TABLE `{$tableNames['model_has_permissions']}` ADD PRIMARY KEY (`{$pivotPermission}`, `{$modelMorphKey}`, `model_type`)");
        }

        // Fix model_has_roles table
        if (Schema::hasTable($tableNames['model_has_roles'])) {
            // Drop the primary key constraint first
            DB::statement("ALTER TABLE `{$tableNames['model_has_roles']}` DROP PRIMARY KEY");
            
            // Modify the team_id column to allow NULL
            DB::statement("ALTER TABLE `{$tableNames['model_has_roles']}` MODIFY `{$teamColumn}` BIGINT UNSIGNED NULL");
            
            // Recreate the primary key (now allowing NULL team_id)
            $pivotRole = $columnNames['role_pivot_key'] ?? 'role_id';
            $modelMorphKey = $columnNames['model_morph_key'] ?? 'model_id';
            
            DB::statement("ALTER TABLE `{$tableNames['model_has_roles']}` ADD PRIMARY KEY (`{$pivotRole}`, `{$modelMorphKey}`, `model_type`)");
        }

        // Fix roles table
        if (Schema::hasTable($tableNames['roles'])) {
            // Drop the unique constraint first
            try {
                DB::statement("ALTER TABLE `{$tableNames['roles']}` DROP INDEX `roles_{$teamColumn}_name_guard_name_unique`");
            } catch (\Exception $e) {
                // Index might not exist, continue
            }
            
            // Modify the team_id column to allow NULL
            DB::statement("ALTER TABLE `{$tableNames['roles']}` MODIFY `{$teamColumn}` BIGINT UNSIGNED NULL");
            
            // Recreate the unique constraint (now allowing NULL team_id)
            DB::statement("ALTER TABLE `{$tableNames['roles']}` ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`, `guard_name`)");
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a destructive change, so we won't implement rollback
        // as it could cause data loss for super admin roles
        $this->command->warn('This migration cannot be safely rolled back as it would cause data loss for super admin roles.');
    }
};
