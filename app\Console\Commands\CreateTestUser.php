<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class CreateTestUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:test-user {role=student} {email=<EMAIL>}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test user with specified role';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $role = $this->argument('role');
        $email = $this->argument('email');

        // Check if role exists
        if (!Role::where('name', $role)->exists()) {
            $this->error("Role '{$role}' does not exist. Available roles: student, parent, teacher, school, super_admin");
            return 1;
        }

        // Create user
        $user = User::create([
            'name' => 'Test ' . ucfirst($role),
            'email' => $email,
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Assign role
        $user->assignRole($role);

        $this->info("Test user created successfully!");
        $this->info("Email: {$email}");
        $this->info("Password: password");
        $this->info("Role: {$role}");
        $this->info("Login at: " . url('/login'));

        return 0;
    }
}
