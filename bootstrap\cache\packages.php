<?php return array (
  'akaunting/laravel-money' => 
  array (
    'providers' => 
    array (
      0 => 'Akaunting\\Money\\Provider',
    ),
  ),
  'anourvalar/eloquent-serialize' => 
  array (
    'aliases' => 
    array (
      'EloquentSerialize' => 'AnourValar\\EloquentSerialize\\Facades\\EloquentSerializeFacade',
    ),
  ),
  'arcanedev/log-viewer' => 
  array (
    'providers' => 
    array (
      0 => 'Arcanedev\\LogViewer\\LogViewerServiceProvider',
      1 => 'Arcanedev\\LogViewer\\Providers\\DeferredServicesProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
  ),
  'barryvdh/laravel-ide-helper' => 
  array (
    'providers' => 
    array (
      0 => 'Barryvdh\\LaravelIdeHelper\\IdeHelperServiceProvider',
    ),
  ),
  'bezhansalleh/filament-shield' => 
  array (
    'aliases' => 
    array (
      'FilamentShield' => 'BezhanSalleh\\FilamentShield\\Facades\\FilamentShield',
    ),
    'providers' => 
    array (
      0 => 'BezhanSalleh\\FilamentShield\\FilamentShieldServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-heroicons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'cwsps154/app-settings' => 
  array (
    'providers' => 
    array (
      0 => 'CWSPS154\\AppSettings\\AppSettingsServiceProvider',
    ),
  ),
  'filament/actions' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Actions\\ActionsServiceProvider',
    ),
  ),
  'filament/filament' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\FilamentServiceProvider',
    ),
  ),
  'filament/forms' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Forms\\FormsServiceProvider',
    ),
  ),
  'filament/infolists' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Infolists\\InfolistsServiceProvider',
    ),
  ),
  'filament/notifications' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Notifications\\NotificationsServiceProvider',
    ),
  ),
  'filament/spatie-laravel-settings-plugin' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\SpatieLaravelSettingsPluginServiceProvider',
    ),
  ),
  'filament/spatie-laravel-translatable-plugin' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\SpatieLaravelTranslatablePluginServiceProvider',
    ),
  ),
  'filament/support' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Support\\SupportServiceProvider',
    ),
  ),
  'filament/tables' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Tables\\TablesServiceProvider',
    ),
  ),
  'filament/widgets' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Widgets\\WidgetsServiceProvider',
    ),
  ),
  'flowframe/laravel-trend' => 
  array (
    'providers' => 
    array (
      0 => 'Flowframe\\Trend\\TrendServiceProvider',
    ),
    'aliases' => 
    array (
      'Trend' => 'Flowframe\\Trend\\TrendFacade',
    ),
  ),
  'genealabs/laravel-model-caching' => 
  array (
    'providers' => 
    array (
      0 => 'GeneaLabs\\LaravelModelCaching\\Providers\\Service',
    ),
  ),
  'kirschbaum-development/eloquent-power-joins' => 
  array (
    'providers' => 
    array (
      0 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    ),
  ),
  'laravel/horizon' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Horizon\\HorizonServiceProvider',
    ),
    'aliases' => 
    array (
      'Horizon' => 'Laravel\\Horizon\\Horizon',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravelcm/laravel-subscriptions' => 
  array (
    'providers' => 
    array (
      0 => 'Laravelcm\\Subscriptions\\SubscriptionServiceProvider',
    ),
  ),
  'livewire/livewire' => 
  array (
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
  ),
  'mix-code/filament-multi-2fa' => 
  array (
    'aliases' => 
    array (
      'FilamentMulti2fa' => 'MixCode\\FilamentMulti2fa\\Facades\\FilamentMulti2fa',
    ),
    'providers' => 
    array (
      0 => 'MixCode\\FilamentMulti2fa\\FilamentMulti2faServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'plisio/plisio-sdk-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Plisio\\PlisioSdkLaravel\\Providers\\PlisioProvider',
    ),
  ),
  'propaganistas/laravel-phone' => 
  array (
    'providers' => 
    array (
      0 => 'Propaganistas\\LaravelPhone\\PhoneServiceProvider',
    ),
  ),
  'ryangjchandler/blade-capture-directive' => 
  array (
    'providers' => 
    array (
      0 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    ),
    'aliases' => 
    array (
      'BladeCaptureDirective' => 'RyanChandler\\BladeCaptureDirective\\Facades\\BladeCaptureDirective',
    ),
  ),
  'spatie/eloquent-sortable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\EloquentSortable\\EloquentSortableServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
  ),
  'spatie/laravel-medialibrary' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'spatie/laravel-settings' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\LaravelSettings\\LaravelSettingsServiceProvider',
    ),
  ),
  'spatie/laravel-sitemap' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Sitemap\\SitemapServiceProvider',
    ),
  ),
  'spatie/laravel-tags' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Tags\\TagsServiceProvider',
    ),
  ),
  'spatie/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'squirephp/countries' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\CountriesServiceProvider',
    ),
  ),
  'squirephp/countries-en' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\CountriesEnServiceProvider',
    ),
  ),
  'squirephp/currencies' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\CurrenciesServiceProvider',
    ),
  ),
  'squirephp/currencies-en' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\CurrenciesEnServiceProvider',
    ),
  ),
  'squirephp/model' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\ModelServiceProvider',
    ),
  ),
  'squirephp/repository' => 
  array (
    'providers' => 
    array (
      0 => 'Squire\\RepositoryServiceProvider',
    ),
    'aliases' => 
    array (
      'RepositoryManager' => 'Squire\\Repository\\Facades\\Repository',
    ),
  ),
  'statikbe/laravel-chained-translator' => 
  array (
    'providers' => 
    array (
      0 => 'Statikbe\\LaravelChainedTranslator\\TranslationServiceProvider',
    ),
  ),
  'statikbe/laravel-filament-chained-translation-manager' => 
  array (
    'aliases' => 
    array (
      'FilamentTranslationManager' => 'Statikbe\\FilamentTranslationManager\\FilamentTranslationManager',
    ),
    'providers' => 
    array (
      0 => 'Statikbe\\FilamentTranslationManager\\FilamentTranslationManagerServiceProvider',
    ),
  ),
  'tomatophp/console-helpers' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\ConsoleHelpers\\ConsoleHelpersServiceProvider',
    ),
  ),
  'tomatophp/filament-icons' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentIcons\\FilamentIconsServiceProvider',
    ),
  ),
  'tomatophp/filament-locations' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentLocations\\FilamentLocationsServiceProvider',
    ),
  ),
  'tomatophp/filament-media-manager' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentMediaManager\\FilamentMediaManagerServiceProvider',
    ),
  ),
  'tomatophp/filament-payments' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentPayments\\FilamentPaymentsServiceProvider',
    ),
  ),
  'tomatophp/filament-settings-hub' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentSettingsHub\\FilamentSettingsHubServiceProvider',
    ),
  ),
  'tomatophp/filament-subscriptions' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentSubscriptions\\FilamentSubscriptionsServiceProvider',
    ),
  ),
  'tomatophp/filament-translation-component' => 
  array (
    'providers' => 
    array (
      0 => 'TomatoPHP\\FilamentTranslationComponent\\FilamentTranslationComponentServiceProvider',
    ),
  ),
  'ysfkaya/filament-phone-input' => 
  array (
    'providers' => 
    array (
      0 => 'Ysfkaya\\FilamentPhoneInput\\FilamentPhoneInputServiceProvider',
    ),
  ),
);