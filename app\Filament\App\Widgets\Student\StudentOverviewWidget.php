<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class StudentOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('เกรดเฉลี่ย', '3.8')
                ->description('ภาคเรียนนี้')
                ->descriptionIcon('heroicon-m-academic-cap')
                ->color('success'),

            Stat::make('การบ้านที่ต้องส่ง', '3 ชิ้น')
                ->description('สัปดาห์นี้')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('warning'),

            Stat::make('วิชาที่เรียนจบ', '12 วิชา')
                ->description('ทั้งหมด')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('info'),

            Stat::make('อัตราการเข้าเรียน', '95%')
                ->description('เดือนนี้')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('primary'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }
}
