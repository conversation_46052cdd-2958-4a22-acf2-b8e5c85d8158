<?php

namespace App\Models\MediaManager;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Builder;
use TomatoPHP\FilamentMediaManager\Models\Media as BaseMedia;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property int|null $created_by
 * @property int|null $user_id
 * @property string $model_type
 * @property int $model_id
 * @property string|null $uuid
 * @property string $collection_name
 * @property string $name
 * @property string $file_name
 * @property string|null $mime_type
 * @property string $disk
 * @property string|null $conversions_disk
 * @property int $size
 * @property array<array-key, mixed> $manipulations
 * @property array<array-key, mixed> $custom_properties
 * @property array<array-key, mixed> $generated_conversions
 * @property array<array-key, mixed> $responsive_images
 * @property int|null $order_column
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $creator
 * @property-read mixed $extension
 * @property-read mixed $human_readable_size
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $model
 * @property-read mixed $original_url
 * @property-read \App\Models\User|null $owner
 * @property-read mixed $preview_url
 * @property-read \App\Models\Team|null $team
 * @property-read mixed $type
 * @method static \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, static> all($columns = ['*'])
 * @method static Builder<static>|Media forCurrentTeam()
 * @method static Builder<static>|Media forCurrentUser()
 * @method static Builder<static>|Media forFolder($folderId)
 * @method static Builder<static>|Media forTeam($teamId)
 * @method static Builder<static>|Media forUser($userId)
 * @method static \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, static> get($columns = ['*'])
 * @method static Builder<static>|Media newModelQuery()
 * @method static Builder<static>|Media newQuery()
 * @method static Builder<static>|Media ordered()
 * @method static Builder<static>|Media query()
 * @method static Builder<static>|Media whereCollectionName($value)
 * @method static Builder<static>|Media whereConversionsDisk($value)
 * @method static Builder<static>|Media whereCreatedAt($value)
 * @method static Builder<static>|Media whereCreatedBy($value)
 * @method static Builder<static>|Media whereCustomProperties($value)
 * @method static Builder<static>|Media whereDisk($value)
 * @method static Builder<static>|Media whereFileName($value)
 * @method static Builder<static>|Media whereGeneratedConversions($value)
 * @method static Builder<static>|Media whereId($value)
 * @method static Builder<static>|Media whereManipulations($value)
 * @method static Builder<static>|Media whereMimeType($value)
 * @method static Builder<static>|Media whereModelId($value)
 * @method static Builder<static>|Media whereModelType($value)
 * @method static Builder<static>|Media whereName($value)
 * @method static Builder<static>|Media whereOrderColumn($value)
 * @method static Builder<static>|Media whereResponsiveImages($value)
 * @method static Builder<static>|Media whereSize($value)
 * @method static Builder<static>|Media whereTeamId($value)
 * @method static Builder<static>|Media whereUpdatedAt($value)
 * @method static Builder<static>|Media whereUserId($value)
 * @method static Builder<static>|Media whereUuid($value)
 * @mixin \Eloquent
 */
class Media extends BaseMedia
{
    use BelongsToTeam;

    protected $fillable = [
        'model_type',
        'model_id',
        'uuid',
        'collection_name',
        'name',
        'file_name',
        'mime_type',
        'disk',
        'conversions_disk',
        'size',
        'manipulations',
        'custom_properties',
        'generated_conversions',
        'responsive_images',
        'order_column',
        'team_id',
        'created_by',
        'user_id',
    ];

    protected $casts = [
        'manipulations' => 'array',
        'custom_properties' => 'array',
        'generated_conversions' => 'array',
        'responsive_images' => 'array',
    ];

    /**
     * Handle manipulations attribute to ensure it's always an array
     */
    public function getManipulationsAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($value) ? $value : [];
    }

    /**
     * Handle custom_properties attribute to ensure it's always an array
     */
    public function getCustomPropertiesAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($value) ? $value : [];
    }

    /**
     * Handle generated_conversions attribute to ensure it's always an array
     */
    public function getGeneratedConversionsAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($value) ? $value : [];
    }

    /**
     * Handle responsive_images attribute to ensure it's always an array
     */
    public function getResponsiveImagesAttribute($value)
    {
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : [];
        }

        return is_array($value) ? $value : [];
    }

    protected static function booted(): void
    {
        // Call parent booted method to maintain original functionality
        parent::booted();

        // Add team-based global scope
        static::addGlobalScope('team_media', function (Builder $query) {
            $user = auth()->user();

            // Skip team filtering if we're in media manager context
            if (request()->routeIs('filament.*.resources.folders.*') ||
                request()->routeIs('filament.*.resources.media.*') ||
                request()->has('folder_id') ||
                request()->has('model_type')) {
                return;
            }

            // Skip team filtering for super admins - they should see all media
            if ($user && $user->hasRole('super_admin')) {
                return;
            }

            // Apply team filtering based on current tenant or user's team
            if (filament()->hasTenancy() && filament()->getTenant()) {
                $query->where('team_id', filament()->getTenant()->id);
            } elseif ($user && $user->team_id) {
                $query->where('team_id', $user->team_id);
            }
        });

        // Add user-level filtering scope - TEMPORARILY DISABLED FOR TESTING
        // static::addGlobalScope('user_media', function (Builder $query) {
        //     $user = auth()->user();
        //
        //     if (!$user) {
        //         return;
        //     }

        //     // Skip user filtering if we're in media manager context
        //     if (request()->routeIs('filament.*.resources.media.*') ||
        //         request()->routeIs('filament.*.resources.folders.*') ||
        //         request()->has('folder_id')) {
        //         return;
        //     }

        //     // Super admins can see all media (no filtering)
        //     if ($user->hasRole('super_admin')) {
        //         return;
        //     }

        //     // Team admins can see all media in their team
        //     if ($user->hasRole('team_admin')) {
        //         return; // Team filtering already applied above
        //     }

        //     // Regular users can only see their own media
        //     $query->where(function ($query) use ($user) {
        //         $query->where('created_by', $user->id)
        //               ->orWhere('user_id', $user->id);
        //     });
        // });

        // Auto-set user fields when creating media
        static::creating(function (Media $media) {
            $user = auth()->user();

            if ($user) {
                if (!$media->created_by) {
                    $media->created_by = $user->id;
                }

                if (!$media->user_id) {
                    $media->user_id = $user->id;
                }

                // Set team_id based on current tenant or user's team
                if (!$media->team_id) {
                    if (filament()->hasTenancy() && filament()->getTenant()) {
                        $media->team_id = filament()->getTenant()->id;
                    } elseif ($user->team_id) {
                        $media->team_id = $user->team_id;
                    }
                }

                // Add team_id to custom properties for path generation
                if ($media->team_id) {
                    $customProperties = $media->custom_properties ?? [];
                    $customProperties['team_id'] = $media->team_id;
                    $customProperties['created_by_user'] = $user->name;

                    // Add team name if available
                    if ($media->team_id) {
                        $team = \App\Models\Team::find($media->team_id);
                        if ($team) {
                            $customProperties['team_name'] = $team->name;
                        }
                    }

                    $media->custom_properties = $customProperties;
                }
            }
        });
    }

    /**
     * Override the folder scope to work with team filtering
     */
    public function scopeForFolder(Builder $query, $folderId): Builder
    {
        $folder = Folder::find($folderId);
        if ($folder) {
            if (!$folder->model_type) {
                $query->where('collection_name', $folder->collection);
            } else {
                $query
                    ->where('model_type', $folder->model_type)
                    ->where('model_id', $folder->model_id)
                    ->where('collection_name', $folder->collection);
            }
        }

        return $query;
    }

    /**
     * Scope to filter media by team
     */
    public function scopeForTeam(Builder $query, $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope to filter media by current team
     */
    public function scopeForCurrentTeam(Builder $query): Builder
    {
        if (filament()->hasTenancy() && filament()->getTenant()) {
            return $query->where('team_id', filament()->getTenant()->id);
        }

        $user = auth()->user();
        if ($user && $user->team_id) {
            return $query->where('team_id', $user->team_id);
        }

        return $query;
    }

    /**
     * Get the user who created this media
     */
    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who owns this media
     */
    public function owner()
    {
        return $this->belongsTo(\App\Models\User::class, 'user_id');
    }

    /**
     * Scope to filter media by user
     */
    public function scopeForUser(Builder $query, $userId): Builder
    {
        return $query->where(function ($query) use ($userId) {
            $query->where('created_by', $userId)
                  ->orWhere('user_id', $userId);
        });
    }

    /**
     * Scope to filter media by current user
     */
    public function scopeForCurrentUser(Builder $query): Builder
    {
        $user = auth()->user();
        if (!$user) {
            return $query->whereRaw('1 = 0'); // Return no results
        }

        return $query->forUser($user->id);
    }
}
