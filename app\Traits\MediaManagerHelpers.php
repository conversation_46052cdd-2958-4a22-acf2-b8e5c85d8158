<?php

namespace App\Traits;

use App\Models\Team;
use App\Models\User;
use Filament\Facades\Filament;

trait MediaManagerHelpers
{
    /**
     * Determine the appropriate team_id for media manager operations
     */
    protected static function determineTeamId(?User $user = null, $folder = null): ?int
    {
        $user = $user ?? auth()->user();

        if (!$user) {
            \Log::info('MediaManagerHelpers::determineTeamId - No user found');
            return null;
        }

        \Log::info('MediaManagerHelpers::determineTeamId - Starting', [
            'user_id' => $user->id,
            'user_team_id' => $user->team_id,
            'user_roles' => $user->roles?->pluck('name'),
            'folder_team_id' => $folder?->team_id ?? 'no_folder',
        ]);

        // If user has a team_id, use it (for regular users and team admins)
        if ($user->team_id) {
            \Log::info('MediaManagerHelpers::determineTeamId - Using user team_id', ['team_id' => $user->team_id]);
            return $user->team_id;
        }

        // For super admins without a team_id, try different approaches
        if ($user->hasRole('super_admin')) {
            \Log::info('MediaManagerHelpers::determineTeamId - User is super admin, trying fallbacks');

            // 1. Try to get team from current tenant context
            $tenant = Filament::getTenant();
            \Log::info('MediaManagerHelpers::determineTeamId - Tenant check', ['tenant' => $tenant]);
            if ($tenant && $tenant->id) {
                \Log::info('MediaManagerHelpers::determineTeamId - Using tenant ID ' . $tenant->slug, ['team_id' => $tenant->id]);
                return $tenant->id;
            }

            // 2. Try to get team from the folder being worked on
            if ($folder && $folder->team_id) {
                \Log::info('MediaManagerHelpers::determineTeamId - Using folder team_id', ['team_id' => $folder->team_id]);
                return $folder->team_id;
            }

            // 3. Try to get team from URL (if we're in a tenant context)
            $currentUrl = request()->url();
            \Log::info('MediaManagerHelpers::determineTeamId - Checking URL', ['url' => $currentUrl]);
            if (preg_match('/\/backend\/([^\/]+)\//', $currentUrl, $matches)) {
                $teamSlug = $matches[1];
                \Log::info('MediaManagerHelpers::determineTeamId - Found team slug in URL', ['slug' => $teamSlug]);
                $team = Team::where('slug', $teamSlug)->first();
                if ($team) {
                    \Log::info('MediaManagerHelpers::determineTeamId - Using team from URL', ['team_id' => $team->id]);
                    return $team->id;
                }
            }

            // 4. Get the first available team as fallback
            $firstTeam = Team::first();
            \Log::info('MediaManagerHelpers::determineTeamId - First team fallback', ['team' => $firstTeam?->only(['id', 'name', 'slug'])]);
            if ($firstTeam) {
                \Log::info('MediaManagerHelpers::determineTeamId - Using first team', ['team_id' => $firstTeam->id]);
                return $firstTeam->id;
            }
        }

        // Final fallback - return null if nothing works
        \Log::info('MediaManagerHelpers::determineTeamId - No team found, returning null');
        return null;
    }

    /**
     * Get team name for the given team_id
     */
    protected static function getTeamName(?int $teamId): string
    {
        if (!$teamId) {
            return 'Unknown Team';
        }

        $team = Team::find($teamId);
        return $team ? $team->name : 'Unknown Team';
    }
}
