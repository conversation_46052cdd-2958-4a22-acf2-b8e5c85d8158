<!-- Super Admin specific fields (minimal additional fields) -->

<!-- Administrative Information -->
<div class="form-section">
    <h2 class="section-title">Administrative Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="employee_id" class="form-label">Employee ID</label>
            <input type="text" id="employee_id" name="employee_id" class="form-input" 
                   value="<?php echo e(old('employee_id', $profile->employee_id)); ?>" 
                   placeholder="Enter your employee ID">
            <?php $__errorArgs = ['employee_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="error-message"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>

        <div class="form-group">
            <label for="qualification" class="form-label">Qualification</label>
            <input type="text" id="qualification" name="qualification" class="form-input" 
                   value="<?php echo e(old('qualification', $profile->qualification)); ?>" 
                   placeholder="Your educational background or certifications">
            <?php $__errorArgs = ['qualification'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="error-message"><?php echo e($message); ?></div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
</div>
<?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/profile/partials/super_admin-fields.blade.php ENDPATH**/ ?>