<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class TeachingSchedule extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'classroom_id',
        'subject_id',
        'lesson_id',
        'team_id',
        'start_time',
        'end_time',
        'notes',
        'status',
        'is_recurring',
        'recurring_pattern',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_recurring' => 'boolean',
        'recurring_pattern' => 'array',
    ];

    /**
     * Get the teacher (user) for this schedule
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the user for this schedule (alias for teacher)
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the classroom for this schedule
     */
    public function classroom(): BelongsTo
    {
        return $this->belongsTo(ClassRoom::class);
    }

    /**
     * Get the subject for this schedule
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the lesson for this schedule
     */
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the team for this schedule
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Scope a query to only include schedules for a specific team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope a query to only include schedules for a specific teacher
     */
    public function scopeForTeacher(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to only include schedules for a specific date
     */
    public function scopeForDate(Builder $query, string $date): Builder
    {
        return $query->whereDate('start_time', $date);
    }

    /**
     * Scope a query to only include schedules within a date range
     */
    public function scopeBetweenDates(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('start_time', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include upcoming schedules
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('start_time', '>', now());
    }

    /**
     * Scope a query to only include today's schedules
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('start_time', today());
    }

    /**
     * Scope a query to only include schedules by status
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Get the duration of the schedule in minutes
     */
    public function getDurationAttribute(): int
    {
        return $this->start_time->diffInMinutes($this->end_time);
    }

    /**
     * Get the formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $minutes = $this->duration;

        if ($minutes < 60) {
            return $minutes . ' minutes';
        }

        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        if ($remainingMinutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        return $hours . 'h ' . $remainingMinutes . 'm';
    }

    /**
     * Get the formatted time range
     */
    public function getTimeRangeAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    /**
     * Get the formatted date
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->start_time->format('M j, Y');
    }

    /**
     * Get the day of week
     */
    public function getDayOfWeekAttribute(): string
    {
        return $this->start_time->format('l');
    }

    /**
     * Check if the schedule is today
     */
    public function isToday(): bool
    {
        return $this->start_time->isToday();
    }

    /**
     * Check if the schedule is in the past
     */
    public function isPast(): bool
    {
        return $this->end_time->isPast();
    }

    /**
     * Check if the schedule is currently active
     */
    public function isActive(): bool
    {
        $now = now();
        return $now->between($this->start_time, $this->end_time);
    }

    /**
     * Check if the schedule conflicts with another schedule
     */
    public function conflictsWith(TeachingSchedule $other): bool
    {
        return $this->classroom_id === $other->classroom_id &&
               $this->start_time < $other->end_time &&
               $this->end_time > $other->start_time;
    }
}
