<?php

namespace App\Console\Commands;

use App\Models\Team;
use Illuminate\Console\Command;

class ClearTeamCache extends Command
{
    protected $signature = 'team:clear-cache {team_slug?} {--all-teams}';
    protected $description = 'Clear settings cache for a specific team (safe for regular users)';

    public function handle()
    {
        $teamSlug = $this->argument('team_slug');
        $allTeams = $this->option('all-teams');

        if ($allTeams) {
            $this->info('Clearing settings cache for all teams...');
            
            $teams = Team::all();
            foreach ($teams as $team) {
                clear_team_settings_cache($team->id);
                $this->info("✅ Cache cleared for team: {$team->name} (ID: {$team->id})");
            }
            
            // Also clear global settings
            clear_team_settings_cache(null);
            $this->info("✅ Global settings cache cleared");
            
            $this->info("✅ Settings cache cleared for all {$teams->count()} teams!");
            return 0;
        }

        if ($teamSlug) {
            $team = Team::where('slug', $teamSlug)->first();
            
            if (!$team) {
                $this->error("Team with slug '{$teamSlug}' not found.");
                $this->info('Available teams:');
                
                $teams = Team::all();
                foreach ($teams as $availableTeam) {
                    $this->line("  - {$availableTeam->slug} ({$availableTeam->name})");
                }
                
                return 1;
            }
            
            $this->info("Clearing settings cache for team: {$team->name}");
            clear_team_settings_cache($team->id);
            $this->info("✅ Settings cache cleared for {$team->name}!");
            
        } else {
            $this->info('Clearing global settings cache...');
            clear_team_settings_cache(null);
            $this->info('✅ Global settings cache cleared!');
        }

        return 0;
    }
}
