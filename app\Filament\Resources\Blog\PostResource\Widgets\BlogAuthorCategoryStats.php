<?php

namespace App\Filament\Resources\Blog\PostResource\Widgets;

use App\Models\Blog\Author;
use App\Models\Blog\Category;
use App\Models\Blog\Post;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BlogAuthorCategoryStats extends BaseWidget
{
    protected function getStats(): array
    {
        $totalAuthors = Author::count();
        $totalCategories = Category::count();
        $visibleCategories = Category::where('is_visible', true)->count();
        $totalPosts = Post::count();
        
        // Authors with posts
        $authorsWithPosts = Author::has('posts')->count();
        $authorsWithoutPosts = $totalAuthors - $authorsWithPosts;
        
        // Categories with posts
        $categoriesWithPosts = Category::has('posts')->count();
        $categoriesWithoutPosts = $totalCategories - $categoriesWithPosts;
        
        // Posts without author or category
        $postsWithoutAuthor = Post::whereNull('blog_author_id')->count();
        $postsWithoutCategory = Post::whereNull('blog_category_id')->count();

        return [
            Stat::make('Total Authors', $totalAuthors)
                ->description("{$authorsWithPosts} with posts, {$authorsWithoutPosts} without posts")
                ->descriptionIcon('heroicon-m-users')
                ->color('primary')
                ->chart([2, 5, 8, 12, 15, 18, $totalAuthors])
                ->chartColor('primary'),

            Stat::make('Total Categories', $totalCategories)
                ->description("{$categoriesWithPosts} with posts, {$categoriesWithoutPosts} without posts")
                ->descriptionIcon('heroicon-m-folder')
                ->color('success')
                ->chart([3, 7, 10, 8, 14, 12, $totalCategories])
                ->chartColor('success'),

            Stat::make('Visible Categories', $visibleCategories)
                ->description('Categories shown to readers')
                ->descriptionIcon('heroicon-m-eye')
                ->color('info'),

            Stat::make('Posts Missing Data', $postsWithoutAuthor + $postsWithoutCategory)
                ->description("{$postsWithoutAuthor} without author, {$postsWithoutCategory} without category")
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($postsWithoutAuthor + $postsWithoutCategory > 0 ? 'warning' : 'success'),

            Stat::make('Total Posts', $totalPosts)
                ->description('All blog posts in the system')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('gray'),

            Stat::make('Avg Posts per Author', $totalAuthors > 0 ? round($totalPosts / $totalAuthors, 1) : 0)
                ->description('Average posts per author')
                ->descriptionIcon('heroicon-m-chart-bar')
                ->color('indigo'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
