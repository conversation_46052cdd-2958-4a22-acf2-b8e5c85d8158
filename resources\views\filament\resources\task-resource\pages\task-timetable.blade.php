<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Timetable Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <!-- Date Navigation -->
            <div class="flex items-center gap-4">
                <div class="flex items-center gap-2">
                    <button
                        wire:click="changeDate('{{ now()->parse($selectedDate ?? now())->subDay()->format('Y-m-d') }}')"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                        <x-heroicon-o-chevron-left class="w-4 h-4" />
                    </button>

                    <input
                        type="date"
                        wire:model.live="selectedDate"
                        class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                    />

                    <button
                        wire:click="changeDate('{{ now()->parse($selectedDate ?? now())->addDay()->format('Y-m-d') }}')"
                        class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    >
                        <x-heroicon-o-chevron-right class="w-4 h-4" />
                    </button>
                </div>

                <button
                    wire:click="changeDate('{{ now()->format('Y-m-d') }}')"
                    class="px-3 py-2 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                >
                    Today
                </button>
            </div>

            <!-- View Mode Toggle -->
            <div class="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                    wire:click="changeViewMode('day')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'day' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Day
                </button>
                <button
                    wire:click="changeViewMode('week')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'week' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Week
                </button>
                <button
                    wire:click="changeViewMode('month')"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $viewMode === 'month' ? 'bg-white dark:bg-gray-600 shadow' : 'hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Month
                </button>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Show:</span>

                <button
                    wire:click="toggleMyTasks"
                    onclick="showLoadingOverlay()"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showMyTasks ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    My Tasks
                </button>

                <button
                    wire:click="toggleAssignedToMe"
                    onclick="showLoadingOverlay()"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showAssignedToMe ? 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Assigned to Me
                </button>

                <button
                    wire:click="toggleSchedules"
                    onclick="showLoadingOverlay()"
                    class="px-3 py-1 text-sm rounded-md transition-colors {{ $showSchedules ? 'bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300' : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600' }}"
                >
                    Teaching Schedules
                </button>
            </div>

            <div class="flex items-center gap-2">
                <button
                    wire:click="showAll"
                    class="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                    Show All
                </button>
            </div>
        </div>

        <!-- Legend -->
        {{-- <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">Legend</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-blue-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Tasks</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-green-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Teaching Schedules</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-red-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">High Priority</span>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                    <span class="text-sm text-gray-600 dark:text-gray-400">Medium Priority</span>
                </div>
            </div>
        </div> --}}

        <!-- Timetable Container -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div id="timetable-container" class="relative">
                @if($viewMode === 'week')
                    @include('filament.resources.task-resource.pages.partials.week-view', $this->getViewData())
                @elseif($viewMode === 'day')
                    @include('filament.resources.task-resource.pages.partials.day-view', $this->getViewData())
                @else
                    @include('filament.resources.task-resource.pages.partials.month-view', $this->getViewData())
                @endif
            </div>
        </div>

        <!-- Task/Schedule Popover -->
        <div id="task-popover" class="fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 p-3 hidden">
            <div class="flex items-center gap-2">
                <!-- Status Update Button -->
                <button
                    onclick="quickComplete()"
                    class="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
                    title="Update status">
                    <x-heroicon-o-arrow-path class="w-4 h-4" />
                </button>

                <!-- Edit Button -->
                <button
                    onclick="quickEdit()"
                    class="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
                    title="Edit">
                    <x-heroicon-o-pencil class="w-4 h-4" />
                </button>

                <!-- Delete Button -->
                <button
                    onclick="quickDelete()"
                    class="p-2 rounded-lg bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400 transition-colors"
                    title="Delete">
                    <x-heroicon-o-trash class="w-4 h-4" />
                </button>
            </div>
        </div>

        <!-- Help Button -->
        <div class="fixed bottom-6 left-6 z-50">
            <button
                type="button"
                onclick="toggleHelp()"
                class="bg-gray-600 hover:bg-gray-700 text-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-200"
                title="Show keyboard shortcuts">
                <x-heroicon-o-question-mark-circle class="w-5 h-5" />
            </button>
        </div>

        <!-- Help Modal -->
        <div id="help-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">Keyboard Shortcuts</h3>
                    <button onclick="toggleHelp()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <x-heroicon-o-x-mark class="w-5 h-5" />
                    </button>
                </div>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">New Task</span>
                        <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+N</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">New Schedule</span>
                        <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">Ctrl+Shift+N</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Quick Add</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Click empty slot</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">More Options</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Right-click empty slot</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Quick Actions</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Click task/schedule</span>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-2 pl-4">
                        • Status: Pending/In Progress/Completed/Cancelled<br>
                        • Edit: Open pre-filled edit form<br>
                        • Delete: Remove with confirmation
                    </div>
                </div>
            </div>
        </div>


    </div>

    @push('styles')
        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

        <!-- SweetAlert2 Custom Styling -->
        <style type="text/css">
            .swal2-popup {
                border-radius: 0.75rem !important;
                font-family: 'Inter', sans-serif !important;
            }

            .swal2-title {
                font-size: 1.25rem !important;
                font-weight: 600 !important;
            }

            .swal2-content {
                font-size: 0.875rem !important;
                color: #6B7280 !important;
            }

            .swal2-confirm {
                border-radius: 0.5rem !important;
                font-weight: 500 !important;
                padding: 0.5rem 1rem !important;
            }

            .swal2-cancel {
                border-radius: 0.5rem !important;
                font-weight: 500 !important;
                padding: 0.5rem 1rem !important;
            }

            .swal2-icon {
                border: none !important;
            }

            .swal2-icon.swal2-success {
                color: #10B981 !important;
            }

            .swal2-icon.swal2-warning {
                color: #F59E0B !important;
            }

            .swal2-icon.swal2-question {
                color: #3B82F6 !important;
            }

            /* Custom SweetAlert2 buttons */
            .swal-custom-html {
                padding: 0 20px !important;
            }

            .swal-custom-btn {
                width: 100%;
                padding: 8px 10px;
                border: none;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .swal-btn-success {
                background-color: #10B981;
                color: white;
            }

            .swal-btn-success:hover {
                background-color: #059669;
                transform: translateY(-1px);
            }

            .swal-btn-warning {
                background-color: #F59E0B;
                color: white;
            }

            .swal-btn-warning:hover {
                background-color: #D97706;
                transform: translateY(-1px);
            }

            .swal-btn-info {
                background-color: #3B82F6;
                color: white;
            }

            .swal-btn-info:hover {
                background-color: #2563EB;
                transform: translateY(-1px);
            }

            .swal-btn-danger {
                background-color: #EF4444;
                color: white;
            }

            .swal-btn-danger:hover {
                background-color: #DC2626;
                transform: translateY(-1px);
            }

            /* Loading Spinner Animations */
            .spinner {
                width: 16px;
                height: 16px;
                border: 2px solid #e5e7eb;
                border-top: 2px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            .spinner-large {
                width: 32px;
                height: 32px;
                border: 3px solid #e5e7eb;
                border-top: 3px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        </style>
    @endpush

    @push('scripts')
        <!-- SweetAlert2 CDN -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

        <!-- Drag and Drop JavaScript -->
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Wait for Livewire to be available
            const initializeWhenReady = () => {
                if (typeof window.Livewire !== 'undefined') {
                    initializeTimetable();
                    createLoadingOverlay();

                    // Configure SweetAlert2 defaults
                    Swal.mixin({
                        customClass: {
                            confirmButton: 'swal2-confirm',
                            cancelButton: 'swal2-cancel'
                        },
                        buttonsStyling: false,
                        showClass: {
                            popup: 'animate__animated animate__fadeInDown animate__faster'
                        },
                        hideClass: {
                            popup: 'animate__animated animate__fadeOutUp animate__faster'
                        }
                    });
                } else {
                    // Retry after 100ms if Livewire is not ready
                    setTimeout(initializeWhenReady, 100);
                }
            };

            initializeWhenReady();
        });

        // Loading overlay functions
        function createLoadingOverlay() {
            if (document.querySelector('.loading-overlay')) return;

            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
            `;

            overlay.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; text-align: center;">
                    <div class="spinner-large" style="margin: 0 auto 10px;"></div>
                    <div style="color: #374151; font-size: 14px;">Processing...</div>
                </div>
            `;

            document.body.appendChild(overlay);
        }

        function showLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.style.opacity = '1';
                overlay.style.visibility = 'visible';
            }
        }

        function hideLoadingOverlay() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';
            }
        }

        function initializeTimetable() {
            // Remove existing event listeners to prevent duplicates
            const draggableItems = document.querySelectorAll('.draggable-item');
            const dropZones = document.querySelectorAll('.drop-zone');

            // Remove old listeners first
            draggableItems.forEach(item => {
                item.removeEventListener('dragstart', handleDragStart);
                item.removeEventListener('dragend', handleDragEnd);
            });

            dropZones.forEach(zone => {
                zone.removeEventListener('dragover', handleDragOver);
                zone.removeEventListener('drop', handleDrop);
                zone.removeEventListener('dragenter', handleDragEnter);
                zone.removeEventListener('dragleave', handleDragLeave);
            });

            // Add fresh event listeners
            draggableItems.forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
            });

            dropZones.forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('drop', handleDrop);
                zone.addEventListener('dragenter', handleDragEnter);
                zone.addEventListener('dragleave', handleDragLeave);
            });
        }

        function handleDragStart(e) {
            const itemId = e.target.dataset.id;
            const itemType = e.target.dataset.type;

            // Debug log
            console.log('Drag start:', { itemId, itemType, target: e.target });

            if (!itemId || !itemType) {
                console.error('Missing drag data:', { itemId, itemType });
                e.preventDefault();
                return;
            }

            // Clear any previous data and set fresh data
            e.dataTransfer.clearData();
            e.dataTransfer.setData('text/plain', itemId);
            e.dataTransfer.setData('application/x-item-type', itemType);
            e.dataTransfer.setData('application/x-item-id', itemId);

            // Store in global variable as backup
            window.dragData = { itemId, itemType };

            e.target.style.opacity = '0.5';
        }

        function handleDragEnd(e) {
            e.target.style.opacity = '1';
        }

        function handleDragOver(e) {
            e.preventDefault();
        }

        function handleDragEnter(e) {
            e.preventDefault();
            e.target.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.target.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.target.classList.remove('drag-over');

            // Try multiple methods to get drag data
            let itemId = e.dataTransfer.getData('text/plain') ||
                        e.dataTransfer.getData('application/x-item-id') ||
                        (window.dragData && window.dragData.itemId);

            let itemType = e.dataTransfer.getData('application/x-item-type') ||
                          e.dataTransfer.getData('type') ||
                          (window.dragData && window.dragData.itemType);

            // Find the drop zone (might be a child element)
            let dropZone = e.target;
            while (dropZone && !dropZone.classList.contains('drop-zone')) {
                dropZone = dropZone.parentElement;
            }

            const newDateTime = dropZone ? dropZone.dataset.datetime : e.target.dataset.datetime;

            // Debug log the drop data
            console.log('Drop operation data:', {
                itemId, itemType, newDateTime,
                dropTarget: e.target,
                dropZone: dropZone,
                datasetDatetime: e.target.dataset.datetime,
                dropZoneDatetime: dropZone?.dataset.datetime,
                dragData: window.dragData
            });

            // Validate that we have all required data
            if (!itemId || !itemType) {
                console.error('Missing required data for drop operation', {
                    itemId, itemType, newDateTime,
                    dataTransferData: {
                        textPlain: e.dataTransfer.getData('text/plain'),
                        type: e.dataTransfer.getData('type'),
                        itemType: e.dataTransfer.getData('application/x-item-type'),
                        itemId: e.dataTransfer.getData('application/x-item-id')
                    },
                    windowDragData: window.dragData
                });
                return;
            }

            // If no datetime found, try to construct one from the current date and hour
            if (!newDateTime) {
                console.warn('No datetime found in drop zone, attempting to construct one');
                const currentDate = new Date();
                const dateStr = currentDate.toISOString().split('T')[0];
                const hour = Math.floor(currentDate.getHours());
                newDateTime = `${dateStr} ${hour.toString().padStart(2, '0')}:00:00`;
                console.log('Constructed datetime:', newDateTime);
            }

            // Show loading state with spinner
            const draggedElement = document.querySelector(`[data-id="${itemId}"]`);
            if (draggedElement) {
                draggedElement.style.opacity = '0.5';
                draggedElement.style.pointerEvents = 'none';

                // Add loading spinner
                const loadingSpinner = document.createElement('div');
                loadingSpinner.className = 'loading-spinner-overlay';
                loadingSpinner.innerHTML = `
                    <div class="spinner"></div>
                `;
                loadingSpinner.style.cssText = `
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 4px;
                    padding: 8px;
                    z-index: 1000;
                `;
                draggedElement.style.position = 'relative';
                draggedElement.appendChild(loadingSpinner);
            }

            // Show global loading overlay
            showLoadingOverlay();

            if (itemType === 'task') {
                window.Livewire.find('{{ $this->getId() }}').call('updateTaskPosition', itemId, newDateTime)
                    .then(() => {
                        console.log('Task moved successfully');
                        hideLoadingOverlay();
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                            const spinner = draggedElement.querySelector('.loading-spinner-overlay');
                            if (spinner) spinner.remove();
                        }
                        // Show success toast
                        Swal.fire({
                            title: 'Success!',
                            text: 'Task moved successfully',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false,
                            toast: true,
                            position: 'top-end'
                        });
                    })
                    .catch((error) => {
                        console.error('Failed to move task:', error);
                        hideLoadingOverlay();
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                            const spinner = draggedElement.querySelector('.loading-spinner-overlay');
                            if (spinner) spinner.remove();
                        }
                        // Show error toast
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to move task',
                            icon: 'error',
                            timer: 3000,
                            showConfirmButton: false,
                            toast: true,
                            position: 'top-end'
                        });
                    });
            } else if (itemType === 'schedule') {
                window.Livewire.find('{{ $this->getId() }}').call('updateSchedulePosition', itemId, newDateTime)
                    .then(() => {
                        console.log('Schedule moved successfully');
                        hideLoadingOverlay();
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                            const spinner = draggedElement.querySelector('.loading-spinner-overlay');
                            if (spinner) spinner.remove();
                        }
                        // Show success toast
                        Swal.fire({
                            title: 'Success!',
                            text: 'Schedule moved successfully',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false,
                            toast: true,
                            position: 'top-end'
                        });
                    })
                    .catch((error) => {
                        console.error('Failed to move schedule:', error);
                        hideLoadingOverlay();
                        // Reset element state
                        if (draggedElement) {
                            draggedElement.style.opacity = '1';
                            draggedElement.style.pointerEvents = 'auto';
                            const spinner = draggedElement.querySelector('.loading-spinner-overlay');
                            if (spinner) spinner.remove();
                        }
                        // Show error toast
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to move schedule',
                            icon: 'error',
                            timer: 3000,
                            showConfirmButton: false,
                            toast: true,
                            position: 'top-end'
                        });
                    });
            }
        }

        // Reinitialize after Livewire updates
        document.addEventListener('livewire:navigated', function() {
            setTimeout(() => initializeTimetable(), 100);
        });

        document.addEventListener('livewire:init', () => {
            setTimeout(() => initializeTimetable(), 100);
        });

        // Also reinitialize after any Livewire component update
        document.addEventListener('livewire:load', () => {
            if (typeof Livewire !== 'undefined') {
                Livewire.hook('morph.updated', () => {
                    setTimeout(() => {
                        initializeTimetable();
                        hideLoadingOverlay();
                    }, 100);
                });
            }
        });

        // Listen for Livewire events
        window.addEventListener('task-moved', event => {
            console.log('Task moved:', event.detail);
            // You can add a toast notification here
        });

        window.addEventListener('schedule-moved', event => {
            console.log('Schedule moved:', event.detail);
            // You can add a toast notification here
        });

        // Quick task creation function
        function createTaskAtTime(datetime) {
            // Use Livewire directly
            window.Livewire.find('{{ $this->getId() }}').mountAction('new_task', {
                start_datetime: datetime
            });
        }

        // Quick schedule creation function
        function createScheduleAtTime(datetime) {
            // Use Livewire directly
            window.Livewire.find('{{ $this->getId() }}').mountAction('new_schedule', {
                start_time: datetime
            });
        }

        // SweetAlert2 Create Options functionality
        function showCreateOptions(datetime) {
            const date = new Date(datetime);
            const formattedDate = date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            const formattedTime = date.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });

            Swal.fire({
                title: 'Choose what to create for',
                html: ` 
                    <div class="text-center py-3 border">
                        <p class="font-medium text-gray-800">${formattedDate}</p>
                        <p class="text-sm text-gray-600">${formattedTime}</p>
                    </div>
                    <div class="space-y-3 mt-3">
                        <button id="btn-add-task" class="swal-custom-btn swal-btn-info w-full">
                            <i class="fas fa-tasks mr-3"></i>&nbsp; Add Task
                        </button>
                        <button id="btn-add-schedule" class="swal-custom-btn swal-btn-success w-full">
                            <i class="fas fa-calendar-plus mr-3"></i>&nbsp; Add Teaching Schedule
                        </button>
                    </div>
                `,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'Cancel',
                width: '400px',
                customClass: {
                    htmlContainer: 'swal-custom-html'
                },
                didOpen: () => {
                    // Add event listeners to custom buttons
                    document.getElementById('btn-add-task').addEventListener('click', () => {
                        Swal.close();
                        createTaskAtTime(datetime);
                    });

                    document.getElementById('btn-add-schedule').addEventListener('click', () => {
                        Swal.close();
                        createScheduleAtTime(datetime);
                    });
                }
            });
        }

        // Make function globally available
        window.showCreateOptions = showCreateOptions;

        // Help modal functionality
        function toggleHelp() {
            const modal = document.getElementById('help-modal');
            modal.classList.toggle('hidden');
        }

        // Close help modal when clicking outside
        document.getElementById('help-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                toggleHelp();
            }
        });

        // SweetAlert2 Task Details functionality
        function showTaskDetails(itemId, itemType) {
            // Check if Livewire is available
            if (typeof window.Livewire === 'undefined') {
                console.error('Livewire is not loaded yet');
                return;
            }

            // Show global loading overlay
            showLoadingOverlay();

            // Fetch task/schedule data from the server
            if (itemType === 'task') {
                window.Livewire.find('{{ $this->getId() }}').call('getTaskDetails', itemId).then(taskData => {
                    showTaskDetailModal(taskData, itemType);
                    hideLoadingOverlay();
                });
            } else {
                window.Livewire.find('{{ $this->getId() }}').call('getScheduleDetails', itemId).then(scheduleData => {
                    showTaskDetailModal(scheduleData, itemType);
                    hideLoadingOverlay();
                });
            }
        }

        // Make function globally available
        window.showTaskDetails = showTaskDetails;

        function showTaskDetailModal(data, itemType) { 

            const isTask = itemType === 'task';
            const title = isTask ? data.title : data.subject_name;
            const timeInfo = isTask ?
                `${data.start_time} - ${data.end_time || 'No end time'}` :
                `${data.start_time} - ${data.end_time}`;

            let htmlContent = `
                <div class="text-left space-y-4">
                    <div class="border-b pb-3">
                        <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                        <p class="text-sm text-gray-600 mt-1">
                            <i class="fas fa-clock mr-2"></i>&nbsp;${timeInfo}
                        </p>
                    </div>
            `;

            if (isTask) {
                htmlContent += `
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Priority:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${getPriorityClass(data.priority)}">${data.priority}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Status:</span>
                            <span class="ml-2 px-2 py-1 rounded text-xs ${getStatusClass(data.status)}">${data.status}</span>
                        </div>
                        ${data.assigned_user ? `
                        <div class="col-span-2">
                            <span class="font-medium text-gray-700">Assigned to:</span>
                            <span class="ml-2">${data.assigned_user}</span>
                        </div>
                        ` : ''}
                        ${data.location ? `
                        <div class="col-span-2">
                            <span class="font-medium text-gray-700">Location:</span>
                            <span class="ml-2">${data.location}</span>
                        </div>
                        ` : ''}
                    </div>
                `;
            } else {
                htmlContent += `
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-700">Teacher:</span>
                            <span class="ml-2">${data.teacher_name}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Classroom:</span>
                            <span class="ml-2">${data.classroom_name}</span>
                        </div>
                        ${data.lesson_name ? `
                        <div class="col-span-2">
                            <span class="font-medium text-gray-700">Lesson:</span>
                            <span class="ml-2">${data.lesson_name}</span>
                        </div>
                        ` : ''}
                    </div>
                `;
            }

            if (data.description || data.notes) {
                htmlContent += `
                    <div class="border-t pt-3">
                        <span class="font-medium text-gray-700">Description:</span>
                        <p class="mt-1 text-gray-600">${data.description || data.notes}</p>
                    </div>
                `;
            }

            // Add Start Live Lesson button for teaching schedules with live lesson enabled
            const hasLiveLesson = !isTask && data.has_live_lesson;
            const liveVideoId = !isTask && data.live_video_id;

            htmlContent += `
                    <div class="border-t pt-4">
                        ${hasLiveLesson ? `
                        <div class="mb-3">
                            <button id="btn-start-live" class="swal-custom-btn swal-btn-success w-full">
                                <i class="fas fa-video mr-2"></i>&nbsp; ${liveVideoId ? 'Join Live Lesson' : 'Start Live Lesson'}
                            </button>
                        </div>
                        ` : ''}
                        <div class="flex gap-2">
                            <button id="btn-edit" class="swal-custom-btn swal-btn-info flex-1">
                                <i class="fas fa-edit mr-2"></i>&nbsp; Edit
                            </button>
                            <button id="btn-status" class="swal-custom-btn swal-btn-warning flex-1">
                                <i class="fas fa-tasks mr-2"></i>&nbsp; Update Status
                            </button>
                            <button id="btn-delete" class="swal-custom-btn swal-btn-danger flex-1">
                                <i class="fas fa-trash mr-2"></i>&nbsp; Delete
                            </button>
                        </div>
                    </div>
                </div>
            `;
                
            Swal.fire({
                title: isTask ? 'Task Details' : 'Schedule Details',
                html: htmlContent,
                showConfirmButton: false,
                showCancelButton: true,
                cancelButtonText: 'Close',
                width: '500px',
                customClass: {
                    htmlContainer: 'swal-custom-html'
                },
                didOpen: () => {
                    // Add event listeners to custom buttons
                    document.getElementById('btn-edit').addEventListener('click', () => {
                        Swal.close();
                        quickEdit(data.id, itemType);
                    });

                    document.getElementById('btn-status').addEventListener('click', () => {
                        Swal.close();
                        quickComplete(data.id, itemType);
                    });

                    document.getElementById('btn-delete').addEventListener('click', () => {
                        Swal.close();
                        quickDelete(data.id, itemType);
                    });

                    // Add event listener for Start Live Lesson button (only for teaching schedules)
                    const startLiveBtn = document.getElementById('btn-start-live');
                    if (startLiveBtn) {
                        startLiveBtn.addEventListener('click', () => {
                            Swal.close();
                            startLiveLesson(data.id, data.live_video_id);
                        });
                    }
                }
            });
        }

        function getPriorityClass(priority) {
            switch(priority) {
                case 'urgent': return 'bg-red-100 text-red-800';
                case 'high': return 'bg-orange-100 text-orange-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                case 'low': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function getStatusClass(status) {
            switch(status) {
                case 'completed': return 'bg-green-100 text-green-800';
                case 'in_progress': return 'bg-blue-100 text-blue-800';
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'cancelled': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        // Quick action functions
        window.quickComplete = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            Swal.fire({
                title: `Update ${itemType} status`,
                html: `
                    <div class="text-gray-600 mb-6">Choose the new status for this ${itemType}:</div>
                    <div class="grid grid-cols-2 gap-3">
                        <br />
                        <button id="btn-pending" class="swal-custom-btn swal-btn-info">
                            <i class="fas fa-hourglass-start mr-2"></i>&nbsp;
                            Pending
                        </button>
                        <button id="btn-in-progress" class="swal-custom-btn swal-btn-warning">
                            <i class="fas fa-clock mr-2"></i>&nbsp;
                            In Progress
                        </button>
                        <button id="btn-complete" class="swal-custom-btn swal-btn-success">
                            <i class="fas fa-check mr-2"></i>&nbsp;
                            Completed
                        </button>
                        <button id="btn-cancelled" class="swal-custom-btn swal-btn-danger">
                            <i class="fas fa-times mr-2"></i>&nbsp;
                            Cancelled
                        </button>
                    </div>
                `,
                showCancelButton: true,
                showConfirmButton: false,
                cancelButtonText: '<i class="fas fa-times"></i> Cancel',
                cancelButtonColor: '#6B7280',
                customClass: {
                    htmlContainer: 'swal-custom-html'
                },
                didOpen: () => {
                    // Add event listeners to custom buttons
                    document.getElementById('btn-pending').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'pending', 'Pending');
                    });

                    document.getElementById('btn-in-progress').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'in_progress', 'In Progress');
                    });

                    document.getElementById('btn-complete').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'completed', 'Completed');
                    });

                    document.getElementById('btn-cancelled').addEventListener('click', () => {
                        Swal.close();
                        updateTaskStatus(itemType, itemId, 'cancelled', 'Cancelled');
                    });
                }
            });

            // closeTaskPopover(); // No longer needed with SweetAlert2
        };

        function updateTaskStatus(itemType, itemId, status, statusLabel) {
            if (itemType === 'task') {
                window.Livewire.find('{{ $this->getId() }}').call('quickUpdateTaskStatus', itemId, status);
            } else {
                window.Livewire.find('{{ $this->getId() }}').call('quickUpdateScheduleStatus', itemId, status);
            }

            Swal.fire({
                title: `${statusLabel}!`,
                text: `Your ${itemType} has been marked as ${statusLabel.toLowerCase()}.`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                toast: true,
                position: 'top-end'
            });
        }

        window.quickEdit = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            // closeTaskPopover(); // No longer needed with SweetAlert2

            // Call the edit action with the task/schedule data
            if (itemType === 'task') {
                window.Livewire.find('{{ $this->getId() }}').call('quickEditTask', itemId);
            } else {
                window.Livewire.find('{{ $this->getId() }}').call('quickEditSchedule', itemId);
            }
        };

        window.quickDelete = function(itemId,itemType) {
            // if (!popoverData.currentTaskId || !popoverData.currentTaskType) return;

            // const itemType = popoverData.currentTaskType;
            // const itemId = popoverData.currentTaskId;

            Swal.fire({
                title: `Delete ${itemType}?`,
                text: `This action cannot be undone!`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#EF4444',
                cancelButtonColor: '#6B7280',
                confirmButtonText: '<i class="fas fa-trash"></i> Yes, delete it!',
                cancelButtonText: '<i class="fas fa-times"></i> Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    if (itemType === 'task') {
                        window.Livewire.find('{{ $this->getId() }}').call('quickDeleteTask', itemId);
                    } else {
                        window.Livewire.find('{{ $this->getId() }}').call('quickDeleteSchedule', itemId);
                    }

                    Swal.fire({
                        title: 'Deleted!',
                        text: `Your ${itemType} has been deleted.`,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        toast: true,
                        position: 'top-end'
                    });
                }
            });

            // closeTaskPopover(); // No longer needed with SweetAlert2
        };

        // Close help modal when clicking outside
        // document.getElementById('help-modal').addEventListener('click', function(e) {
        // document.addEventListener('click', function(e) {
        //     if (event.target.matches(".quickComplete")) {
        //         // Your code here
        //         console.log("quickComplete clicked");
        //         // window.quickComplete(event);
        //     }else if (event.target.matches(".quickEdit")) {
        //         // Your code here
        //         console.log("quickEdit clicked");
        //         // window.quickEdit(event);
        //     }else if (event.target.matches(".quickDelete")) {
        //         // Your code here
        //         console.log("quickDelete clicked");
        //         // window.quickDelete(event);
        //     }
        // });
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + N for new task
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                window.Livewire.find('{{ $this->getId() }}').mountAction('new_task');
            }

            // Ctrl/Cmd + Shift + N for new schedule
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'N') {
                e.preventDefault();
                window.Livewire.find('{{ $this->getId() }}').mountAction('new_schedule');
            }
        });

        // Start Live Lesson function
        function startLiveLesson(scheduleId, liveVideoId) {
            if (liveVideoId) {
                // If live video already exists, redirect to it
                window.open(`/backend/{{ Filament::getTenant()?->slug }}/live-videos/${liveVideoId}/edit`, '_blank');
            } else {
                // Create new live video for this schedule
                Swal.fire({
                    title: 'Start Live Lesson',
                    html: `
                        <div class="text-left space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Live Lesson Title</label>
                                <input type="text" id="live-title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter lesson title">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <textarea id="live-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Enter description (optional)"></textarea>
                            </div>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="live-recording" checked class="mr-2">
                                    <span class="text-sm text-gray-700">Enable Recording</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" id="live-public" class="mr-2">
                                    <span class="text-sm text-gray-700">Public Access</span>
                                </label>
                            </div>
                        </div>
                    `,
                    showCancelButton: true,
                    confirmButtonText: 'Create & Start',
                    cancelButtonText: 'Cancel',
                    width: '500px',
                    preConfirm: () => {
                        const title = document.getElementById('live-title').value;
                        if (!title) {
                            Swal.showValidationMessage('Please enter a title');
                            return false;
                        }
                        return {
                            title: title,
                            description: document.getElementById('live-description').value,
                            is_recording_enabled: document.getElementById('live-recording').checked,
                            is_public: document.getElementById('live-public').checked
                        };
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Call Livewire method to create live video
                        window.Livewire.find('{{ $this->getId() }}').call('createLiveVideoForSchedule', scheduleId, result.value)
                            .then((response) => {
                                if (response && response.live_video_id) {
                                    Swal.fire({
                                        title: 'Live lesson created!',
                                        text: 'Redirecting to live video interface...',
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                    // Redirect to live video edit page
                                    setTimeout(() => {
                                        window.open(`/backend/{{ Filament::getTenant()?->slug }}/live-videos/${response.live_video_id}/edit`, '_blank');
                                    }, 2000);
                                }
                            });
                    }
                });
            }
        }


        </script>
    @endpush

    @push('styles')
        <style>
        .task-item {
            width: 100%; 
        }

        .draggable-item {
            cursor: move;
            transition: opacity 0.2s ease, transform 0.2s ease;
        }

        .draggable-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .drop-zone {
            min-height: 60px;
            transition: background-color 0.2s ease;
        }

        .drop-zone.drag-over {
            background-color: rgba(59, 130, 246, 0.1);
            border: 2px dashed #3B82F6;
        }

        .time-slot {
            border-bottom: 1px solid #e5e7eb;
        }

        .time-slot:last-child {
            border-bottom: none;
        }

        .timetable-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
        }

        .timetable-cell {
            background-color: white;
            min-height: 60px;
            padding: 4px;
            position: relative;
            overflow-x: hidden;
        }

        .dark .timetable-cell {
            background-color: #374151;
        }

        .dark .timetable-grid {
            background-color: #4b5563;
        }

        .task-item > div {
            border-radius: 4px;
            padding: 4px 8px;
            margin: 1px 0;
            font-size: 12px;
            line-height: 1.3;
            border-left: 3px solid;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            position: relative;
            overflow: hidden;
        }

        .task-item > div::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.05));
        }

        .task-item > div.priority-high {
            border-left-color: #EF4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .task-item > div.priority-urgent {
            border-left-color: #DC2626;
            background-color: rgba(220, 38, 38, 0.15);
        }

        .task-item > div.priority-medium {
            border-left-color: #F59E0B;
            background-color: rgba(245, 158, 11, 0.1);
        }

        .task-item > div.priority-low {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .schedule-item {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .calendar-day {
            min-height: 120px;
            border: 1px solid #e5e7eb;
            padding: 8px;
        }

        .calendar-day.today {
            background-color: rgba(59, 130, 246, 0.05);
            border-color: #3B82F6;
        }

        .calendar-day.other-month {
            opacity: 0.5;
        }

        .day-view-hour {
            border-bottom: 1px solid #e5e7eb;
            min-height: 80px;
        }

        .current-time-indicator {
            position: absolute;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #EF4444;
            z-index: 10;
        }

        #task-popover {
            animation: popoverFadeIn 0.15s ease-out;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        @keyframes popoverFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-5px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .draggable-item:hover {
            z-index: 5;
            filter: brightness(1.05);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .draggable-item {
            transition: all 0.15s ease;
        }

        /* Hide edit action buttons */
        .hidden-edit-action {
            display: none !important;
        }


        </style>
    @endpush
</x-filament-panels::page>
