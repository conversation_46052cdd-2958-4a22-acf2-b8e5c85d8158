<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Live - {{ $liveVideo->title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $liveVideo->title }}</h1>
                        <p class="text-sm text-gray-600">{{ $liveVideo->description }}</p>
                        <p class="text-sm text-gray-500">Teacher: {{ $liveVideo->teacher->name }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="viewer-count" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-eye mr-1"></i> 0 viewers
                        </span>
                        @if($liveVideo->status === 'live')
                        <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-circle mr-1 animate-pulse"></i> LIVE
                        </span>
                        @elseif($liveVideo->status === 'scheduled')
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-clock mr-1"></i> SCHEDULED
                        </span>
                        @elseif($liveVideo->status === 'ended')
                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-stop mr-1"></i> ENDED
                        </span>
                        @endif
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Main Video Area -->
                <div class="lg:col-span-3">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <!-- Video Container -->
                        <div class="video-container bg-black">
                            <video id="remoteVideo" autoplay playsinline class="hidden"></video>
                            
                            <!-- Placeholder based on stream status -->
                            <div id="video-placeholder" class="absolute inset-0 flex items-center justify-center bg-gray-900">
                                <div class="text-center text-white">
                                    @if($liveVideo->status === 'scheduled')
                                        <i class="fas fa-clock text-6xl mb-4 opacity-50"></i>
                                        <h3 class="text-xl font-semibold mb-2">Stream Scheduled</h3>
                                        <p class="text-gray-300">Live lesson starts at {{ $liveVideo->scheduled_start_time->format('M j, Y g:i A') }}</p>
                                        <div class="mt-4">
                                            <div id="countdown" class="text-2xl font-mono"></div>
                                        </div>
                                    @elseif($liveVideo->status === 'live')
                                        <i class="fas fa-video text-6xl mb-4 opacity-50"></i>
                                        <h3 class="text-xl font-semibold mb-2">Connecting to Live Stream</h3>
                                        <p class="text-gray-300">Please wait while we connect you to the live lesson</p>
                                    @elseif($liveVideo->status === 'ended')
                                        <i class="fas fa-stop text-6xl mb-4 opacity-50"></i>
                                        <h3 class="text-xl font-semibold mb-2">Stream Ended</h3>
                                        <p class="text-gray-300">This live lesson has ended</p>
                                        @if($liveVideo->recorded_media_id)
                                        <div class="mt-4">
                                            <a href="{{ $liveVideo->recordedMedia->getUrl() }}" 
                                               class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg inline-block">
                                                <i class="fas fa-play mr-2"></i> Watch Recording
                                            </a>
                                        </div>
                                        @endif
                                    @else
                                        <i class="fas fa-exclamation-triangle text-6xl mb-4 opacity-50"></i>
                                        <h3 class="text-xl font-semibold mb-2">Stream Unavailable</h3>
                                        <p class="text-gray-300">This live lesson is currently unavailable</p>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Stream Controls for Viewers -->
                        @if($liveVideo->status === 'live')
                        <div class="p-4 bg-gray-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button id="toggleMuteBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                    <button id="fullscreenBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">Quality:</span>
                                    <select id="qualitySelect" class="px-3 py-1 border border-gray-300 rounded text-sm">
                                        <option value="auto">Auto</option>
                                        <option value="720p">720p</option>
                                        <option value="480p">480p</option>
                                        <option value="360p">360p</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Stream Information -->
                    <div class="mt-6 bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">About This Lesson</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Teacher:</span>
                                <span class="ml-2">{{ $liveVideo->teacher->name }}</span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Scheduled Time:</span>
                                <span class="ml-2">{{ $liveVideo->scheduled_start_time->format('M j, Y g:i A') }}</span>
                            </div>
                            @if($liveVideo->duration)
                            <div>
                                <span class="font-medium text-gray-700">Duration:</span>
                                <span class="ml-2">{{ $liveVideo->duration }} minutes</span>
                            </div>
                            @endif
                            <div>
                                <span class="font-medium text-gray-700">Status:</span>
                                <span class="ml-2 capitalize">{{ str_replace('_', ' ', $liveVideo->status) }}</span>
                            </div>
                        </div>
                        @if($liveVideo->description)
                        <div class="mt-4">
                            <span class="font-medium text-gray-700">Description:</span>
                            <p class="mt-1 text-gray-600">{{ $liveVideo->description }}</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Chat Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b">
                            <h3 class="font-semibold text-gray-900">Live Chat</h3>
                        </div>
                        
                        <!-- Chat Messages -->
                        <div id="chatMessages" class="chat-container p-4 space-y-3">
                            <div class="text-center text-gray-500 text-sm">
                                @if($liveVideo->status === 'live')
                                    Welcome to the live chat! Ask questions and interact with other viewers.
                                @else
                                    Chat will be available when the stream is live.
                                @endif
                            </div>
                        </div>

                        <!-- Chat Input -->
                        @if($liveVideo->status === 'live')
                        <div class="p-4 border-t bg-gray-50">
                            <div class="flex space-x-2">
                                <input type="text" id="chatInput" placeholder="Type a message..." 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                <button id="sendChatBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Participants -->
                    <div class="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b">
                            <h3 class="font-semibold text-gray-900">Participants</h3>
                        </div>
                        <div id="participantsList" class="p-4">
                            <div class="text-center text-gray-500 text-sm">
                                Loading participants...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <script>
        class LiveStreamViewer {
            constructor(liveVideoId, status) {
                this.liveVideoId = liveVideoId;
                this.status = status;
                this.remoteVideo = document.getElementById('remoteVideo');
                this.videoPlaceholder = document.getElementById('video-placeholder');
                this.viewerCount = document.getElementById('viewer-count');
                
                this.initializeViewer();
                this.setupEventListeners();
                this.startPolling();
            }

            initializeViewer() {
                if (this.status === 'scheduled') {
                    this.startCountdown();
                } else if (this.status === 'live') {
                    this.connectToStream();
                }
            }

            setupEventListeners() {
                const toggleMuteBtn = document.getElementById('toggleMuteBtn');
                const fullscreenBtn = document.getElementById('fullscreenBtn');
                const qualitySelect = document.getElementById('qualitySelect');

                toggleMuteBtn?.addEventListener('click', () => this.toggleMute());
                fullscreenBtn?.addEventListener('click', () => this.toggleFullscreen());
                qualitySelect?.addEventListener('change', (e) => this.changeQuality(e.target.value));
            }

            startCountdown() {
                const countdownElement = document.getElementById('countdown');
                if (!countdownElement) return;

                const targetTime = new Date('{{ $liveVideo->scheduled_start_time->toISOString() }}').getTime();

                const updateCountdown = () => {
                    const now = new Date().getTime();
                    const distance = targetTime - now;

                    if (distance < 0) {
                        countdownElement.innerHTML = "Stream should be starting soon...";
                        return;
                    }

                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    let countdownText = '';
                    if (days > 0) countdownText += `${days}d `;
                    if (hours > 0) countdownText += `${hours}h `;
                    countdownText += `${minutes}m ${seconds}s`;

                    countdownElement.innerHTML = countdownText;
                };

                updateCountdown();
                setInterval(updateCountdown, 1000);
            }

            connectToStream() {
                // This would connect to the actual stream
                // For now, just show a connecting message
                console.log('Connecting to live stream...');
                
                // Simulate connection after 2 seconds
                setTimeout(() => {
                    // In a real implementation, this would connect to WebRTC or streaming server
                    console.log('Connected to stream');
                }, 2000);
            }

            toggleMute() {
                if (this.remoteVideo) {
                    this.remoteVideo.muted = !this.remoteVideo.muted;
                    const btn = document.getElementById('toggleMuteBtn');
                    const icon = btn.querySelector('i');
                    
                    if (this.remoteVideo.muted) {
                        icon.className = 'fas fa-volume-mute';
                        btn.classList.add('bg-red-600');
                        btn.classList.remove('bg-gray-600');
                    } else {
                        icon.className = 'fas fa-volume-up';
                        btn.classList.add('bg-gray-600');
                        btn.classList.remove('bg-red-600');
                    }
                }
            }

            toggleFullscreen() {
                const videoContainer = this.remoteVideo.parentElement;
                
                if (!document.fullscreenElement) {
                    videoContainer.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }

            changeQuality(quality) {
                console.log('Changing quality to:', quality);
                // In a real implementation, this would change the stream quality
            }

            startPolling() {
                // Poll for viewer count and status updates
                setInterval(async () => {
                    try {
                        const response = await fetch(`/api/live-videos/${this.liveVideoId}/viewer-count`);
                        const data = await response.json();
                        
                        if (this.viewerCount) {
                            this.viewerCount.innerHTML = `<i class="fas fa-eye mr-1"></i> ${data.viewer_count} viewers`;
                        }

                        // Check if status changed
                        if (data.status !== this.status) {
                            this.status = data.status;
                            location.reload(); // Reload to update UI based on new status
                        }
                    } catch (error) {
                        console.error('Error polling viewer count:', error);
                    }
                }, 5000); // Poll every 5 seconds
            }
        }

        // Initialize the viewer
        document.addEventListener('DOMContentLoaded', function() {
            const liveVideoId = {{ $liveVideo->id }};
            const status = '{{ $liveVideo->status }}';
            const viewer = new LiveStreamViewer(liveVideoId, status);
        });
    </script>
</body>
</html>
