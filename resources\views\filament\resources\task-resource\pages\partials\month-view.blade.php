@php
    $selectedMonth = now()->parse($selectedDate);
    $startOfMonth = $selectedMonth->copy()->startOfMonth()->startOfWeek();
    $endOfMonth = $selectedMonth->copy()->endOfMonth()->endOfWeek();
    $weeks = [];
    $current = $startOfMonth->copy();
    
    while ($current->lte($endOfMonth)) {
        $week = [];
        for ($i = 0; $i < 7; $i++) {
            $week[] = $current->copy();
            $current->addDay();
        }
        $weeks[] = $week;
    }
@endphp

<div class="max-w-6xl mx-auto">
    <!-- Month Header -->
    <div class="bg-gray-50 dark:bg-gray-700 p-4 text-center border-b">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {{ $selectedMonth->format('F Y') }}
        </h2>
    </div>

    <!-- Calendar Grid -->
    <div class="grid grid-cols-7 gap-px bg-gray-200 dark:bg-gray-600">
        <!-- Day Headers -->
        @foreach(['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'] as $day)
            <div class="bg-gray-50 dark:bg-gray-700 p-2 text-center">
                <span class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ $day }}</span>
            </div>
        @endforeach

        <!-- Calendar Days -->
        @foreach($weeks as $week)
            @foreach($week as $day)
                @php
                    $isCurrentMonth = $day->month === $selectedMonth->month;
                    $isToday = $day->isToday();
                    $dayDateString = $day->format('Y-m-d');
                    
                    // Get tasks for this day and sort by start time
                    $dayTasks = $tasks->filter(function($task) use ($day) {
                        return $task->start_datetime->format('Y-m-d') === $day->format('Y-m-d');
                    })->sortBy('start_datetime');

                    // Get teaching schedules for this day and sort by start time
                    $daySchedules = $teachingSchedules->filter(function($schedule) use ($day) {
                        return $schedule->start_time->format('Y-m-d') === $day->format('Y-m-d');
                    })->sortBy('start_time');
                @endphp
                
                <div class="bg-white dark:bg-gray-800 min-h-[120px] p-2 drop-zone {{ $isCurrentMonth ? '' : 'opacity-50' }} {{ $isToday ? 'ring-2 ring-blue-500' : '' }}" 
                     data-datetime="{{ $day->format('Y-m-d 09:00:00') }}">
                    
                    <!-- Day Number -->
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium {{ $isToday ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-gray-100' }}">
                            {{ $day->format('j') }}
                        </span>
                        @if($dayTasks->count() + $daySchedules->count() > 3)
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                +{{ $dayTasks->count() + $daySchedules->count() - 3 }} more
                            </span>
                        @endif
                    </div>

                    <!-- Tasks and Schedules -->
                    <div class="space-y-1">
                        @foreach($dayTasks->take(2) as $task)
                            <div
                                class="draggable-item p-1 rounded text-xs cursor-pointer truncate"
                                style="background-color: {{ $task->color }}20; border-left: 2px solid {{ $task->color }}"
                                draggable="true"
                                data-id="{{ $task->id }}"
                                data-type="task"
                                title="{{ $task->title }} - {{ $task->time_range }}"
                                onclick="showTaskDetails({{ $task->id }}, 'task')"
                            >
                                <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                    {{ $task->title }}
                                </div>
                                <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1">
                                    <span>{{ $task->start_datetime->format('H:i') }}</span>
                                    @if($task->priority === 'high' || $task->priority === 'urgent')
                                        <span class="w-1.5 h-1.5 bg-red-500 rounded-full"></span>
                                    @endif
                                    @if($task->has_alert)
                                        <x-heroicon-o-bell class="w-2 h-2" />
                                    @endif
                                </div>
                            </div>
                        @endforeach

                        @foreach($daySchedules->take(2) as $schedule)
                            <div
                                class="draggable-item p-1 rounded text-xs cursor-pointer truncate"
                                style="background-color: #10B98120; border-left: 2px solid #10B981"
                                draggable="true"
                                data-id="{{ $schedule->id }}"
                                data-type="schedule"
                                title="{{ $schedule->subject->name }} - {{ $schedule->classroom->room_name }}"
                                onclick="showTaskDetails({{ $schedule->id }}, 'schedule')"
                            >
                                <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                    {{ $schedule->subject->name }}
                                </div>
                                <div class="text-gray-600 dark:text-gray-400">
                                    {{ $schedule->start_time->format('H:i') }}
                                </div>
                            </div>
                        @endforeach

                        <!-- Show remaining count if there are more items -->
                        @if($dayTasks->count() + $daySchedules->count() > 4)
                            <div class="text-xs text-gray-500 dark:text-gray-400 text-center py-1">
                                {{ $dayTasks->count() + $daySchedules->count() - 4 }} more items
                            </div>
                        @endif

                        <!-- Empty state for drop zone -->
                        @if($dayTasks->isEmpty() && $daySchedules->isEmpty())
                            <div class="h-16 flex items-center justify-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded"
                                 {{-- onclick="createTaskAtTime('{{ $day->format('Y-m-d') }} 09:00:00')" --}}
                                 onclick="showCreateOptions('{{ $day->format('Y-m-d') }} 09:00:00')"
                                 title="Click to create task on {{ $day->format('M j') }}">
                                <span class="text-xs text-gray-400 dark:text-gray-600 opacity-0 hover:opacity-100 transition-opacity">
                                    + Add Task
                                </span>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        @endforeach
    </div>
</div>
