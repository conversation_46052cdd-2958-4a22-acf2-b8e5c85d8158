<?php $__env->startSection('title', 'Complete Your Profile - EduNest'); ?>

<?php $__env->startPush('styles'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/css/profile.css'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <?php echo app('Illuminate\Foundation\Vite')('resources/js/profile.js'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>

<!-- Debug info removed -->

<div class="profile-container">
    <!-- Skip Button -->
    <form method="POST" action="<?php echo e(route('profile.skip')); ?>" class="inline">
        <?php echo csrf_field(); ?>
        <button type="submit" class="skip-button">
            Skip for now
        </button>
    </form>

    <div class="max-w-4xl mx-auto px-4">
        <div class="profile-card fade-in role-<?php echo e($role); ?>" id="profileCard">
            <!-- Profile Header -->
            <div class="profile-header">
                <?php if($profile->profile_image): ?>
                    <img src="<?php echo e(Storage::url($profile->profile_image)); ?>" alt="Profile" class="profile-avatar">
                <?php else: ?>
                    <div class="profile-avatar bg-white bg-opacity-20 flex items-center justify-center">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                <?php endif; ?>
                
                <h1 class="profile-name">
                    <?php echo e($profile->full_name ?: $user->name ?: 'Complete Your Profile'); ?>

                </h1>
                <p class="profile-role"><?php echo e(ucfirst(str_replace('_', ' ', $role))); ?></p>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-label">
                    <span>Profile Completion</span>
                    <span class="progress-percentage"><?php echo e($profile->getCompletionPercentage()); ?>%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo e($profile->getCompletionPercentage()); ?>%"></div>
                </div>
            </div>

            <!-- Profile Form -->
            <div class="profile-content">
                <form id="profileForm" method="POST" action="<?php echo e(route('profile.update')); ?>" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <!-- Role Selection (for social/phone users) -->
                    <?php if(!$user->roles->contains('name', $role) || $user->provider || $user->phone): ?>
                    <div class="form-section">
                        <h2 class="section-title">Account Type</h2>
                        <div class="form-grid">
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="user_role" class="form-label required">I am a</label>
                                <select id="user_role" name="user_role" class="form-input form-select" required>
                                    <option value="">Select your role</option>
                                    <option value="student" <?php echo e($role === 'student' ? 'selected' : ''); ?>>Student</option>
                                    <option value="parent" <?php echo e($role === 'parent' ? 'selected' : ''); ?>>Parent</option>
                                    <option value="teacher" <?php echo e($role === 'teacher' ? 'selected' : ''); ?>>Teacher</option>
                                    <option value="school" <?php echo e($role === 'school' ? 'selected' : ''); ?>>School Administrator</option>
                                </select>
                                <p class="text-sm text-gray-600 mt-1">This helps us customize your experience</p>
                                <?php $__errorArgs = ['user_role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Basic Information -->
                    <div class="form-section">
                        <h2 class="section-title">Basic Information</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="first_name" class="form-label required">First Name</label>
                                <input type="text" id="first_name" name="first_name" class="form-input" 
                                       value="<?php echo e(old('first_name', $profile->first_name)); ?>" required>
                                <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="last_name" class="form-label required">Last Name</label>
                                <input type="text" id="last_name" name="last_name" class="form-input" 
                                       value="<?php echo e(old('last_name', $profile->last_name)); ?>" required>
                                <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="phone_number" class="form-label required">Phone Number</label>
                                <input type="tel" id="phone_number" name="phone_number" class="form-input" 
                                       value="<?php echo e(old('phone_number', $profile->phone_number)); ?>" required>
                                <?php $__errorArgs = ['phone_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <?php if(in_array($role, ['student', 'parent', 'teacher'])): ?>
                            <div class="form-group">
                                <label for="date_of_birth" class="form-label <?php echo e($role === 'student' ? 'required' : ''); ?>">Date of Birth</label>
                                <input type="date" id="date_of_birth" name="date_of_birth" class="form-input" 
                                       value="<?php echo e(old('date_of_birth', $profile->date_of_birth?->format('Y-m-d'))); ?>" 
                                       <?php echo e($role === 'student' ? 'required' : ''); ?>>
                                <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="gender" class="form-label <?php echo e($role === 'student' ? 'required' : ''); ?>">Gender</label>
                                <select id="gender" name="gender" class="form-input form-select" <?php echo e($role === 'student' ? 'required' : ''); ?>>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?php echo e(old('gender', $profile->gender) === 'male' ? 'selected' : ''); ?>>Male</option>
                                    <option value="female" <?php echo e(old('gender', $profile->gender) === 'female' ? 'selected' : ''); ?>>Female</option>
                                    <option value="other" <?php echo e(old('gender', $profile->gender) === 'other' ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="profile_image" class="form-label">Profile Image</label>
                                <div class="file-upload">
                                    <input type="file" id="profile_image" name="profile_image" accept="image/*">
                                    <div class="file-upload-label">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        Choose Profile Image
                                    </div>
                                </div>
                                <?php $__errorArgs = ['profile_image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea id="bio" name="bio" class="form-input form-textarea" 
                                          placeholder="Tell us about yourself..."><?php echo e(old('bio', $profile->bio)); ?></textarea>
                                <?php $__errorArgs = ['bio'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="form-section">
                        <h2 class="section-title">Address Information</h2>
                        <div class="form-grid">
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="address" class="form-label <?php echo e(in_array($role, ['student', 'parent']) ? 'required' : ''); ?>">Address</label>
                                <textarea id="address" name="address" class="form-input form-textarea" 
                                          placeholder="Enter your full address..." 
                                          <?php echo e(in_array($role, ['student', 'parent']) ? 'required' : ''); ?>><?php echo e(old('address', $profile->address)); ?></textarea>
                                <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="city" class="form-label">City</label>
                                <input type="text" id="city" name="city" class="form-input" 
                                       value="<?php echo e(old('city', $profile->city)); ?>">
                                <?php $__errorArgs = ['city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="state" class="form-label">State/Province</label>
                                <input type="text" id="state" name="state" class="form-input" 
                                       value="<?php echo e(old('state', $profile->state)); ?>">
                                <?php $__errorArgs = ['state'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="postal_code" class="form-label">Postal Code</label>
                                <input type="text" id="postal_code" name="postal_code" class="form-input" 
                                       value="<?php echo e(old('postal_code', $profile->postal_code)); ?>">
                                <?php $__errorArgs = ['postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="country" class="form-label">Country</label>
                                <input type="text" id="country" name="country" class="form-input" 
                                       value="<?php echo e(old('country', $profile->country)); ?>">
                                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <?php if(view()->exists('profile.partials.' . $role . '-fields')): ?>
                        <?php echo $__env->make('profile.partials.' . $role . '-fields', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php else: ?>
                        <!-- No role-specific fields for <?php echo e($role); ?> -->
                        <div class="form-section">
                            <h2 class="section-title">Additional Information</h2>
                            <p class="text-gray-600">No additional fields required for your role.</p>
                        </div>
                    <?php endif; ?>

                    <!-- Connected Accounts Section -->
                    <div class="form-section">
                        <h2 class="section-title">Connected Accounts</h2>
                        <p class="text-sm text-gray-600 mb-4">Connect your social accounts to login with multiple methods</p>

                        <?php
                            $availableProviders = App\Models\User::getAvailableSocialProviders();
                            $connectedAccounts = $user->getConnectedSocialAccounts();
                        ?>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <?php $__currentLoopData = $availableProviders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $provider => $config): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                    $isConnected = isset($connectedAccounts[$provider]);
                                    $accountData = $connectedAccounts[$provider] ?? null;
                                ?>

                                <div class="border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full flex items-center justify-center"
                                             style="background-color: <?php echo e($config['color']); ?>20;">
                                            <i class="<?php echo e($config['icon']); ?> text-lg" style="color: <?php echo e($config['color']); ?>;"></i>
                                        </div>
                                        <div>
                                            <h3 class="font-medium text-gray-900"><?php echo e($config['name']); ?></h3>
                                            <?php if($isConnected): ?>
                                                <p class="text-sm text-gray-600">
                                                    Connected as <?php echo e($accountData['name'] ?? $accountData['email'] ?? 'Unknown'); ?>

                                                </p>
                                            <?php else: ?>
                                                <p class="text-sm text-gray-600">Not connected</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div>
                                        <?php if($isConnected): ?>
                                            <button type="button"
                                                    onclick="disconnectSocialAccount('<?php echo e($provider); ?>', '<?php echo e($config['name']); ?>')"
                                                    class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors">
                                                Disconnect
                                            </button>
                                        <?php else: ?>
                                            <a href="<?php echo e(route('social.connect', $provider)); ?>"
                                               class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                                Connect
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            <?php if(empty($availableProviders)): ?>
                                <div class="col-span-2 text-center py-8 text-gray-500">
                                    <p>No social login providers are currently enabled.</p>
                                    <p class="text-sm">Contact your administrator to enable social login options.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Social Links -->
                    <div class="form-section">
                        <h2 class="section-title">Social Links (Optional)</h2>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facebook_url" class="form-label">Facebook URL</label>
                                <input type="url" id="facebook_url" name="facebook_url" class="form-input" 
                                       value="<?php echo e(old('facebook_url', $profile->facebook_url)); ?>" 
                                       placeholder="https://facebook.com/yourprofile">
                                <?php $__errorArgs = ['facebook_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="line_id" class="form-label">LINE ID</label>
                                <input type="text" id="line_id" name="line_id" class="form-input" 
                                       value="<?php echo e(old('line_id', $profile->line_id)); ?>" 
                                       placeholder="Your LINE ID">
                                <?php $__errorArgs = ['line_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="form-group">
                                <label for="instagram_url" class="form-label">Instagram URL</label>
                                <input type="url" id="instagram_url" name="instagram_url" class="form-input" 
                                       value="<?php echo e(old('instagram_url', $profile->instagram_url)); ?>" 
                                       placeholder="https://instagram.com/yourprofile">
                                <?php $__errorArgs = ['instagram_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="error-message"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="btn-group">
                        <button type="submit" id="submitBtn" class="btn btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Save Profile
                        </button>
                        
                        <button type="button" onclick="skipProfile()" class="btn btn-outline">
                            Skip for Now
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.frontend', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Projects\Web\htdocs\edu-v2\resources\views/profile/edit.blade.php ENDPATH**/ ?>