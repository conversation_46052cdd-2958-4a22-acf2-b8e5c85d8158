<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix any corrupted JSON data in media table
        $this->fixJsonColumn('media', 'manipulations');
        $this->fixJsonColumn('media', 'custom_properties');
        $this->fixJsonColumn('media', 'generated_conversions');
        $this->fixJsonColumn('media', 'responsive_images');
    }

    /**
     * Fix JSON column data
     */
    private function fixJsonColumn(string $table, string $column): void
    {
        // Get all records where the column is not valid JSON or is a string
        $records = DB::table($table)
            ->whereNotNull($column)
            ->where($column, '!=', '')
            ->get();

        foreach ($records as $record) {
            $value = $record->{$column};

            // Skip if already valid JSON array
            if (is_string($value)) {
                $decoded = json_decode($value, true);

                // If it's not valid JSON or is not an array, set to empty array
                if (json_last_error() !== JSON_ERROR_NONE || !is_array($decoded)) {
                    DB::table($table)
                        ->where('id', $record->id)
                        ->update([$column => json_encode([])]);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to reverse this data fix
    }
};
