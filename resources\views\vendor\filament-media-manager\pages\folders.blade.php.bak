
@php
    // Determine if we need a back button for folders view
    $showBackButton = false;
    $backUrl = null;
    $backLabel = 'Back to Main Folders';

    if (request()->has('model_type') || request()->has('collection')) {
        $showBackButton = true;
        $backUrl = \App\Filament\Resources\MediaManager\FolderResource::getUrl('index');
    }
@endphp

{{-- Back Button for Folders --}}
@if($showBackButton && $backUrl)
<div class="p-4 border-b dark:border-gray-700">
    <a href="{{ $backUrl }}"
       class="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        {{ $backLabel }}
    </a>
</div>
@endif

{{-- Folder Context Info --}}
@if(request()->has('model_type'))
<div class="p-4 border-b dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
    <div class="flex items-center gap-3">
        <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900/30">
            <x-icon name="heroicon-o-folder-open" class="w-5 h-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
            <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ str(request()->get('model_type'))->afterLast('\\')->title() }} Folders
            </h2>
            @if(request()->has('collection'))
                <p class="text-sm text-gray-600 dark:text-gray-400">Collection: {{ request()->get('collection') }}</p>
            @endif
        </div>
    </div>
</div>
@endif

<div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
    @foreach($records as $item)
        {{ ($this->folderAction($item))(['record' => $item->toArray()]) }}
    @endforeach
</div>
