<!-- School-specific Fields -->

<!-- School Information -->
<div class="form-section">
    <h2 class="section-title">School Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="school_name" class="form-label required">School Name</label>
            <input type="text" id="school_name" name="school_name" class="form-input" 
                   value="{{ old('school_name', $profile->school_name) }}" 
                   placeholder="Enter the full school name" required>
            @error('school_name')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="school_code" class="form-label required">School Code</label>
            <input type="text" id="school_code" name="school_code" class="form-input" 
                   value="{{ old('school_code', $profile->school_code) }}" 
                   placeholder="Unique school identifier" required>
            @error('school_code')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="principal_name" class="form-label required">Principal Name</label>
            <input type="text" id="principal_name" name="principal_name" class="form-input" 
                   value="{{ old('principal_name', $profile->principal_name) }}" 
                   placeholder="Name of the school principal" required>
            @error('principal_name')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group" style="grid-column: 1 / -1;">
            <label for="school_address" class="form-label required">School Address</label>
            <textarea id="school_address" name="school_address" class="form-input form-textarea" 
                      placeholder="Enter the complete school address..." required>{{ old('school_address', $profile->school_address) }}</textarea>
            @error('school_address')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- Contact Information -->
<div class="form-section">
    <h2 class="section-title">Contact Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="school_phone" class="form-label">School Phone</label>
            <input type="tel" id="school_phone" name="school_phone" class="form-input" 
                   value="{{ old('school_phone', $profile->school_phone) }}" 
                   placeholder="School main phone number">
            @error('school_phone')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="school_email" class="form-label">School Email</label>
            <input type="email" id="school_email" name="school_email" class="form-input" 
                   value="{{ old('school_email', $profile->school_email) }}" 
                   placeholder="<EMAIL>">
            @error('school_email')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- School Statistics -->
<div class="form-section">
    <h2 class="section-title">School Statistics</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="total_students" class="form-label">Total Students</label>
            <input type="number" id="total_students" name="total_students" class="form-input" 
                   value="{{ old('total_students', $profile->total_students) }}" 
                   min="0" placeholder="Number of enrolled students">
            @error('total_students')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="total_teachers" class="form-label">Total Teachers</label>
            <input type="number" id="total_teachers" name="total_teachers" class="form-input" 
                   value="{{ old('total_teachers', $profile->total_teachers) }}" 
                   min="0" placeholder="Number of teaching staff">
            @error('total_teachers')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="established_date" class="form-label">Established Date</label>
            <input type="date" id="established_date" name="established_date" class="form-input" 
                   value="{{ old('established_date', $profile->established_date?->format('Y-m-d')) }}">
            @error('established_date')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>
