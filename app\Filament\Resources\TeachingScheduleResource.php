<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TeachingScheduleResource\Pages;
use App\Models\TeachingSchedule;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TeachingScheduleResource extends Resource
{
    protected static ?string $model = TeachingSchedule::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar-days';

    protected static ?string $navigationGroup = null; // Hidden from navigation

    protected static ?int $navigationSort = 6;

    protected static ?string $navigationLabel = null; // Hidden from navigation

    protected static ?string $modelLabel = 'Teaching Schedule';

    protected static ?string $pluralModelLabel = 'Teaching Schedules';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'teachingSchedules';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function shouldRegisterNavigation(): bool
    {
        return false; // Hidden from sidebar - accessible via task page
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Schedule Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Teacher')
                            ->relationship('user', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                // Only show users with teacher role
                                return $query->whereHas('roles', function (Builder $q) {
                                    $q->where('name', 'teacher');
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('classroom_id')
                            ->label('Classroom')
                            ->relationship('classroom', 'room_name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('room_name')
                                    ->label('Room Name')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., Math Lab, Science Room, Class 1A'),
                                Forms\Components\TextInput::make('room_number')
                                    ->label('Room Number')
                                    ->required()
                                    ->maxLength(255)
                                    ->placeholder('e.g., 101, A-205, Lab-1'),
                                Forms\Components\Select::make('grade_level')
                                    ->label('Grade Level')
                                    ->options([
                                        'Kindergarten' => 'Kindergarten',
                                        'Grade 1' => 'Grade 1',
                                        'Grade 2' => 'Grade 2',
                                        'Grade 3' => 'Grade 3',
                                        'Grade 4' => 'Grade 4',
                                        'Grade 5' => 'Grade 5',
                                        'Grade 6' => 'Grade 6',
                                        'Multi-Grade' => 'Multi-Grade',
                                        'Special' => 'Special Purpose',
                                    ])
                                    ->required()
                                    ->default('Grade 1'),
                                Forms\Components\TextInput::make('capacity')
                                    ->label('Student Capacity')
                                    ->numeric()
                                    ->minValue(1)
                                    ->maxValue(100)
                                    ->placeholder('Maximum number of students'),
                                Forms\Components\Textarea::make('remark')
                                    ->label('Remarks')
                                    ->rows(2)
                                    ->placeholder('Additional notes about this classroom'),
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                            ])
                            ->createOptionUsing(function (array $data): int {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $data['team_id'] = Filament::getTenant()->id;
                                }
                                return \App\Models\ClassRoom::create($data)->getKey();
                            })
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('Subject Name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('code')
                                    ->label('Subject Code')
                                    ->maxLength(20)
                                    ->placeholder('e.g., MATH101'),
                                Forms\Components\Textarea::make('description')
                                    ->label('Description')
                                    ->rows(3),
                                Forms\Components\ColorPicker::make('color')
                                    ->label('Color')
                                    ->default('#3B82F6'),
                                Forms\Components\TextInput::make('credits')
                                    ->label('Credits')
                                    ->numeric()
                                    ->minValue(1)
                                    ->default(3),
                                Forms\Components\Select::make('level')
                                    ->label('Level')
                                    ->options([
                                        'elementary' => 'Elementary',
                                        'middle' => 'Middle School',
                                        'high' => 'High School',
                                        'college' => 'College',
                                    ])
                                    ->default('elementary'),
                                Forms\Components\Toggle::make('is_active')
                                    ->label('Active')
                                    ->default(true),
                            ])
                            ->createOptionUsing(function (array $data): int {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $data['team_id'] = Filament::getTenant()->id;
                                }
                                return \App\Models\Subject::create($data)->getKey();
                            })
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('lesson_id')
                            ->label('Lesson (Optional)')
                            ->relationship('lesson', 'title', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where(function (Builder $q) {
                                        $q->whereHas('book', function (Builder $bookQuery) {
                                            $bookQuery->where('team_id', Filament::getTenant()->id);
                                        })->orWhereHas('course', function (Builder $courseQuery) {
                                            $courseQuery->where('team_id', Filament::getTenant()->id);
                                        });
                                    });
                                }
                                return $query->where('is_published', true);
                            })
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\Select::make('book_id')
                                    ->label('Book (Optional)')
                                    ->relationship('book', 'title', function (Builder $query) {
                                        if (Filament::hasTenancy() && Filament::getTenant()) {
                                            $query->where('team_id', Filament::getTenant()->id);
                                        }
                                        return $query->where('is_active', true);
                                    })
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\Select::make('course_id')
                                    ->label('Course (Optional)')
                                    ->relationship('course', 'title', function (Builder $query) {
                                        if (Filament::hasTenancy() && Filament::getTenant()) {
                                            $query->where('team_id', Filament::getTenant()->id);
                                        }
                                        return $query->where('is_active', true);
                                    })
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\TextInput::make('title')
                                    ->label('Lesson Title')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('chapter')
                                    ->label('Chapter')
                                    ->maxLength(255)
                                    ->placeholder('e.g., Chapter 1, Module 2'),
                                Forms\Components\RichEditor::make('content')
                                    ->label('Content')
                                    ->required()
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'bulletList',
                                        'orderedList',
                                        'h2',
                                        'h3',
                                        'link',
                                    ]),
                                Forms\Components\Textarea::make('objectives')
                                    ->label('Learning Objectives')
                                    ->rows(3),
                                Forms\Components\TextInput::make('duration_minutes')
                                    ->label('Duration (minutes)')
                                    ->numeric()
                                    ->minValue(1)
                                    ->default(45),
                                Forms\Components\Toggle::make('is_published')
                                    ->label('Published')
                                    ->default(true),
                            ])
                            ->createOptionUsing(function (array $data): int {
                                return \App\Models\Lesson::create($data)->getKey();
                            })
                            ->columnSpan(1),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Time & Date')
                    ->schema([
                        Forms\Components\DateTimePicker::make('start_time')
                            ->required()
                            ->seconds(false)
                            ->visible(fn (Forms\Get $get): bool => !$get('is_recurring'))
                            ->columnSpan(1),

                        Forms\Components\DateTimePicker::make('end_time')
                            ->required()
                            ->seconds(false)
                            ->after('start_time')
                            ->visible(fn (Forms\Get $get): bool => !$get('is_recurring'))
                            ->columnSpan(1),

                        Forms\Components\TimePicker::make('start_time')
                            ->label('Start Time')
                            ->required()
                            ->seconds(false)
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->columnSpan(1),

                        Forms\Components\TimePicker::make('end_time')
                            ->label('End Time')
                            ->required()
                            ->seconds(false)
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->columnSpan(1),

                        Forms\Components\Select::make('status')
                            ->options([
                                'scheduled' => 'Scheduled',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('scheduled')
                            ->required()
                            ->disabled(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->columnSpan(1),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->rows(3)
                            ->placeholder('Additional notes for this teaching schedule')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Recurring Schedule')
                    ->schema([
                        Forms\Components\Toggle::make('is_recurring')
                            ->label('Recurring Schedule')
                            ->default(false)
                            ->live()
                            ->columnSpan(1),

                        Forms\Components\DatePicker::make('recurring_start_date')
                            ->label('Start Date')
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->required(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->default(now())
                            ->columnSpan(1),

                        Forms\Components\DatePicker::make('recurring_end_date')
                            ->label('End Date')
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->after('recurring_start_date')
                            ->columnSpan(1),

                        Forms\Components\CheckboxList::make('recurring_days')
                            ->label('Days of Week')
                            ->options([
                                'monday' => 'Monday',
                                'tuesday' => 'Tuesday',
                                'wednesday' => 'Wednesday',
                                'thursday' => 'Thursday',
                                'friday' => 'Friday',
                                'saturday' => 'Saturday',
                                'sunday' => 'Sunday',
                            ])
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->required(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->default(['monday', 'wednesday', 'friday'])
                            ->columns(3)
                            ->columnSpanFull(),

                        Forms\Components\Hidden::make('recurring_pattern')
                            ->default([])
                            ->dehydrateStateUsing(function (Forms\Get $get) {
                                if (!$get('is_recurring')) {
                                    return null;
                                }
                                return [
                                    'frequency' => 'weekly',
                                    'interval' => 1,
                                    'days' => $get('recurring_days') ?? [],
                                    'start_date' => $get('recurring_start_date'),
                                    'end_date' => $get('recurring_end_date'),
                                ];
                            }),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Live Lesson')
                    ->schema([
                        Forms\Components\Toggle::make('has_live_lesson')
                            ->label('Enable Live Lesson')
                            ->default(false)
                            ->live()
                            ->columnSpan(1),

                        Forms\Components\Select::make('live_video_id')
                            ->label('Existing Live Video')
                            ->relationship('liveVideo', 'title')
                            ->searchable()
                            ->preload()
                            ->visible(fn (Forms\Get $get): bool => $get('has_live_lesson'))
                            ->columnSpan(2),

                        Forms\Components\Actions::make([
                            Forms\Components\Actions\Action::make('create_live_video')
                                ->label('Create New Live Video')
                                ->icon('heroicon-o-video-camera')
                                ->color('success')
                                ->visible(fn (Forms\Get $get): bool => $get('has_live_lesson'))
                                ->form([
                                    Forms\Components\TextInput::make('live_title')
                                        ->label('Live Lesson Title')
                                        ->required(),

                                    Forms\Components\Textarea::make('live_description')
                                        ->label('Description')
                                        ->rows(3),

                                    Forms\Components\Toggle::make('live_is_recording_enabled')
                                        ->label('Enable Recording')
                                        ->default(true),

                                    Forms\Components\Toggle::make('live_is_public')
                                        ->label('Public Access')
                                        ->default(false),
                                ])
                                ->action(function (array $data, Forms\Set $set) {
                                    // This will be handled in the create/edit page
                                    $set('create_live_video_data', $data);
                                }),
                        ])
                        ->visible(fn (Forms\Get $get): bool => $get('has_live_lesson'))
                        ->columnSpanFull(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('formatted_date')
                    ->label('Date')
                    ->sortable(['start_time'])
                    ->searchable(false),
                
                Tables\Columns\TextColumn::make('day_of_week')
                    ->label('Day')
                    ->badge()
                    ->color('gray'),
                
                Tables\Columns\TextColumn::make('time_range')
                    ->label('Time')
                    ->badge()
                    ->color('info'),
                
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Teacher')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('classroom.room_name')
                    ->label('Classroom')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->badge()
                    ->color('primary')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('lesson.title')
                    ->label('Lesson')
                    ->searchable()
                    ->limit(30)
                    ->placeholder('No specific lesson'),
                
                Tables\Columns\TextColumn::make('formatted_duration')
                    ->label('Duration')
                    ->badge()
                    ->color('success'),
                
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'scheduled' => 'info',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\IconColumn::make('is_recurring')
                    ->label('Recurring')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('has_live_lesson')
                    ->label('Live Lesson')
                    ->boolean()
                    ->color(fn (bool $state): string => $state ? 'success' : 'gray'),

                Tables\Columns\TextColumn::make('liveVideo.title')
                    ->label('Live Video')
                    ->limit(30)
                    ->placeholder('No live video')
                    ->visible(fn ($record) => $record->has_live_lesson),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('Teacher')
                    ->relationship('user', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->whereHas('roles', function (Builder $q) {
                            $q->where('name', 'teacher');
                        });
                    }),
                
                Tables\Filters\SelectFilter::make('classroom_id')
                    ->label('Classroom')
                    ->relationship('classroom', 'room_name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    }),
                
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    }),
                
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
                
                Tables\Filters\Filter::make('today')
                    ->label('Today')
                    ->query(fn (Builder $query): Builder => $query->today()),
                
                Tables\Filters\Filter::make('upcoming')
                    ->label('Upcoming')
                    ->query(fn (Builder $query): Builder => $query->upcoming()),
                
                Tables\Filters\TernaryFilter::make('is_recurring')
                    ->label('Recurring'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('start_time', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTeachingSchedules::route('/'),
            'create' => Pages\CreateTeachingSchedule::route('/create'),
            'view' => Pages\ViewTeachingSchedule::route('/{record}'),
            'edit' => Pages\EditTeachingSchedule::route('/{record}/edit'),
        ];
    }
}
