/* Global Layout Styles - Header and Footer */

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #000000 0%, #64748b 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Homepage specific gradient text override */
.gradient-text {
    background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
    display: inline-block;
    position: relative;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Alternative gradient text without animation for fallback */
.gradient-text-static {
    background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    display: inline-block;
}

/* Gradient text with subtle glow effect */
.gradient-text-glow {
    background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
    filter: drop-shadow(0 0 10px rgba(6, 182, 212, 0.3));
    display: inline-block;
}

/* Browser compatibility fallback */
@supports not (background-clip: text) {
    .gradient-text,
    .gradient-text-static,
    .gradient-text-glow {
        background: none;
        color: #06b6d4;
        animation: none;
        filter: none;
    }
}

/* Additional gradient text utilities */
.gradient-text-fast {
    background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    animation: gradientShift 1.5s ease-in-out infinite;
    display: inline-block;
}

.gradient-text-slow {
    background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 50%, #8b5cf6 100%);
    background-size: 200% 100%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    animation: gradientShift 6s ease-in-out infinite;
    display: inline-block;
}

/* Prevent text selection issues with gradient text */
.gradient-text,
.gradient-text-static,
.gradient-text-glow,
.gradient-text-fast,
.gradient-text-slow {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* ===== ANIMATED SVG EFFECTS ===== */

/* Floating Particles Animation */
.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(6, 182, 212, 0.3);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 6s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 8s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 7s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 9s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 6s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 8s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; animation-duration: 7s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; animation-duration: 9s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; animation-duration: 6s; }

@keyframes float {
    0%, 100% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* SVG Path Drawing Animation */
.draw-svg {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: drawPath 3s ease-in-out forwards;
}

@keyframes drawPath {
    to {
        stroke-dashoffset: 0;
    }
}

/* Morphing Shape Animation */
.morph-shape {
    animation: morphShape 4s ease-in-out infinite;
}

@keyframes morphShape {
    0%, 100% {
        d: path("M20,20 C20,20 50,20 50,20 C80,20 80,50 80,50 C80,80 50,80 50,80 C20,80 20,50 20,50 Z");
    }
    50% {
        d: path("M20,30 C30,10 70,10 80,30 C90,50 70,90 50,80 C30,90 10,50 20,30 Z");
    }
}

/* Pulsing Glow Effect */
.pulse-glow-svg {
    filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.5));
    animation: pulseGlowSvg 2s ease-in-out infinite;
}

@keyframes pulseGlowSvg {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.5));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 20px rgba(6, 182, 212, 0.8));
        transform: scale(1.05);
    }
}

/* Orbital Animation */
.orbital-animation {
    animation: orbit 8s linear infinite;
    transform-origin: center;
}

@keyframes orbit {
    from {
        transform: rotate(0deg) translateX(30px) rotate(0deg);
    }
    to {
        transform: rotate(360deg) translateX(30px) rotate(-360deg);
    }
}

/* Wave Animation */
.wave-svg {
    animation: waveSvg 3s ease-in-out infinite;
}

@keyframes waveSvg {
    0%, 100% {
        d: path("M0,50 Q25,25 50,50 T100,50");
    }
    50% {
        d: path("M0,50 Q25,75 50,50 T100,50");
    }
}

/* Liquid Blob Animation */
.liquid-blob {
    animation: liquidBlob 6s ease-in-out infinite;
}

@keyframes liquidBlob {
    0%, 100% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    50% {
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
}

/* Glitch Effect */
.glitch-svg {
    animation: glitch 2s linear infinite;
}

@keyframes glitch {
    2%, 64% {
        transform: translate(2px, 0) skew(0deg);
    }
    4%, 60% {
        transform: translate(-2px, 0) skew(0deg);
    }
    62% {
        transform: translate(0, 0) skew(5deg);
    }
}

/* Breathing Animation */
.breathing-svg {
    animation: breathing 3s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

/* Spiral Animation */
.spiral-svg {
    animation: spiral 4s linear infinite;
    transform-origin: center;
}

@keyframes spiral {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.2);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

/* Elastic Bounce */
.elastic-bounce {
    animation: elasticBounce 2s ease-out infinite;
}

@keyframes elasticBounce {
    0% {
        transform: scale(1, 1);
    }
    30% {
        transform: scale(1.25, 0.75);
    }
    40% {
        transform: scale(0.75, 1.25);
    }
    50% {
        transform: scale(1.15, 0.85);
    }
    65% {
        transform: scale(0.95, 1.05);
    }
    75% {
        transform: scale(1.05, 0.95);
    }
    100% {
        transform: scale(1, 1);
    }
}

/* Typewriter Effect for SVG Text */
.typewriter-svg {
    stroke-dasharray: 500;
    stroke-dashoffset: 500;
    animation: typewriter 3s steps(20) forwards;
}

@keyframes typewriter {
    to {
        stroke-dashoffset: 0;
    }
}

/* Shimmer Effect */
.shimmer-svg {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Pendulum Swing */
.pendulum-svg {
    animation: pendulum 2s ease-in-out infinite;
    transform-origin: top center;
}

@keyframes pendulum {
    0%, 100% {
        transform: rotate(-15deg);
    }
    50% {
        transform: rotate(15deg);
    }
}

/* Magnetic Field Effect */
.magnetic-field {
    animation: magneticField 3s ease-in-out infinite;
}

@keyframes magneticField {
    0%, 100% {
        transform: translateX(0) translateY(0);
    }
    25% {
        transform: translateX(5px) translateY(-3px);
    }
    50% {
        transform: translateX(-3px) translateY(5px);
    }
    75% {
        transform: translateX(-5px) translateY(-2px);
    }
}

/* DNA Helix Animation */
.dna-helix {
    animation: dnaHelix 4s linear infinite;
    transform-origin: center;
}

@keyframes dnaHelix {
    0% {
        transform: rotateY(0deg) rotateX(0deg);
    }
    25% {
        transform: rotateY(90deg) rotateX(90deg);
    }
    50% {
        transform: rotateY(180deg) rotateX(180deg);
    }
    75% {
        transform: rotateY(270deg) rotateX(270deg);
    }
    100% {
        transform: rotateY(360deg) rotateX(360deg);
    }
}

/* Kaleidoscope Effect */
.kaleidoscope-svg {
    animation: kaleidoscope 6s linear infinite;
    transform-origin: center;
}

@keyframes kaleidoscope {
    0% {
        transform: rotate(0deg) scale(1);
        filter: hue-rotate(0deg);
    }
    33% {
        transform: rotate(120deg) scale(1.1);
        filter: hue-rotate(120deg);
    }
    66% {
        transform: rotate(240deg) scale(0.9);
        filter: hue-rotate(240deg);
    }
    100% {
        transform: rotate(360deg) scale(1);
        filter: hue-rotate(360deg);
    }
}

/* Heartbeat Animation */
.heartbeat-svg {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

/* Navigation Styles */
.nav-link {
    position: relative;
    color: #64748b;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.nav-link:hover {
    color: #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
}

.nav-link.active {
    color: #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

/* Mobile Menu Styles */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    max-width: 400px;
    height: 100vh;
    background: white;
    z-index: 50;
    transition: right 0.3s ease;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 40;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: #64748b;
    font-weight: 500;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: #0ea5e9;
    background-color: rgba(14, 165, 233, 0.1);
    padding-left: 2rem;
}

.mobile-nav-link.text-red-600:hover {
    color: #dc2626;
    background-color: rgba(220, 38, 38, 0.1);
}

/* Menu Toggle Button */
.menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 2rem;
    height: 2rem;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 10;
}

.menu-toggle span {
    width: 2rem;
    height: 0.25rem;
    background: #64748b;
    border-radius: 10px;
    transition: all 0.3s linear;
    position: relative;
    transform-origin: 1px;
}

.menu-toggle.active span:first-child {
    transform: rotate(45deg);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: translateX(20px);
}

.menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg);
}

@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }
    
    .desktop-nav {
        display: none;
    }
}

/* Footer Styles */
.footer {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.footer-content {
    position: relative;
    z-index: 2;
}

.footer-section h3 {
    color: #f8fafc;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
}

.footer-section h3::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 2rem;
    height: 2px;
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    border-radius: 1px;
}

.footer-link {
    color: #cbd5e1;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
}

.footer-link:hover {
    color: #0ea5e9;
    transform: translateX(0.25rem);
}

.footer-link::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    width: 0;
    height: 1px;
    background: #0ea5e9;
    transition: width 0.3s ease;
    transform: translateY(-50%);
}

.footer-link:hover::before {
    width: 0.5rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: #cbd5e1;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-link:hover {
    background: rgba(14, 165, 233, 0.2);
    color: #0ea5e9;
    transform: translateY(-0.25rem);
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

/* Background Shapes */
.shape {
    position: fixed;
    border-radius: 50%;
    filter: blur(100px);
    opacity: 0.1;
    z-index: -1;
    pointer-events: none;
}

.blob {
    animation: blob 20s infinite;
}

@keyframes blob {
    0%, 100% {
        transform: translate(0, 0) scale(1);
    }
    25% {
        transform: translate(20px, -50px) scale(1.1);
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    75% {
        transform: translate(50px, 10px) scale(1.05);
    }
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(100px);
    z-index: 30;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.scroll-to-top:hover {
    transform: translateY(-0.25rem);
    box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4);
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #0ea5e9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}
 

.gradient-bg {
    background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
}

/* Responsive Design */
@media (max-width: 640px) {
    .scroll-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .mobile-menu {
        max-width: 100%;
    }
    
    .footer-section {
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .social-link {
        width: 2rem;
        height: 2rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .footer,
    .scroll-to-top,
    .mobile-menu,
    .mobile-menu-overlay {
        display: none !important;
    }
    
    .shape {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .nav-link,
    .mobile-nav-link {
        border: 1px solid currentColor;
    }
    
    .footer {
        background: #000;
    }
    
    .social-link {
        border: 1px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .blob {
        animation: none;
    }
    
    .loading-spinner {
        animation: none;
        border-top-color: transparent;
    }
}

/* User Dropdown Styles */
.user-dropdown {
    z-index: 1000;
}

.user-dropdown-menu {
    min-width: 14rem;
    max-width: 20rem;
}

.user-dropdown-item {
    transition: all 0.2s ease;
}

.user-dropdown-item:hover {
    transform: translateX(2px);
}

.user-avatar {
    transition: all 0.2s ease;
}

.user-avatar:hover {
    transform: scale(1.05);
}

/* Alpine.js Cloak */
[x-cloak] {
    display: none !important;
}
