<?php

namespace App\Filament\App\Widgets\Parent;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class ChildrenProgressWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.parent.children-progress';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('parent');
    }

    public function getChildrenProgress(): array
    {
        // Placeholder data - replace with actual children data
        return [
            [
                'name' => 'น้องมายด์',
                'grade' => 'ป.6',
                'subjects' => [
                    [
                        'name' => 'คณิตศาสตร์',
                        'progress' => 75,
                        'average_score' => 92,
                        'exercises_completed' => '75%',
                        'color' => '#3b82f6',
                        'bg_gradient' => 'from-blue-500 to-blue-600'
                    ],
                    [
                        'name' => 'วิทยาศาสตร์',
                        'progress' => 60,
                        'average_score' => 85,
                        'exercises_completed' => '60%',
                        'color' => '#22c55e',
                        'bg_gradient' => 'from-green-500 to-green-600'
                    ],
                    [
                        'name' => 'ภาษาไทย',
                        'progress' => 90,
                        'average_score' => 99,
                        'exercises_completed' => '90%',
                        'color' => '#a855f7',
                        'bg_gradient' => 'from-purple-500 to-purple-600'
                    ],
                ],
                'overall_performance' => 'excellent',
                'gpa' => 3.8,
                'attendance' => 95
            ],
            [
                'name' => 'น้องมิกกี้',
                'grade' => 'ป.4',
                'subjects' => [
                    [
                        'name' => 'คณิตศาสตร์',
                        'progress' => 55,
                        'average_score' => 78,
                        'exercises_completed' => '55%',
                        'color' => '#3b82f6',
                        'bg_gradient' => 'from-blue-500 to-blue-600'
                    ],
                    [
                        'name' => 'ภาษาไทย',
                        'progress' => 70,
                        'average_score' => 85,
                        'exercises_completed' => '70%',
                        'color' => '#a855f7',
                        'bg_gradient' => 'from-purple-500 to-purple-600'
                    ],
                    [
                        'name' => 'ภาษาอังกฤษ',
                        'progress' => 40,
                        'average_score' => 72,
                        'exercises_completed' => '40%',
                        'color' => '#eab308',
                        'bg_gradient' => 'from-yellow-500 to-yellow-600'
                    ],
                ],
                'overall_performance' => 'good',
                'gpa' => 3.2,
                'attendance' => 88
            ],
        ];
    }

    public function getCircleProgress($progress): array
    {
        $circumference = 2 * pi() * 45; // radius = 45
        $strokeDasharray = $circumference;
        $strokeDashoffset = $circumference - ($progress / 100) * $circumference;

        return [
            'strokeDasharray' => $strokeDasharray,
            'strokeDashoffset' => $strokeDashoffset
        ];
    }
}
