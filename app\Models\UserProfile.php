<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'phone_number',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'profile_image',
        'bio',
        'emergency_contact_name',
        'emergency_contact_phone',
        'emergency_contact_relationship',
        'student_id',
        'employee_id',
        'grade_level',
        'class_section',
        'subjects',
        'qualification',
        'years_of_experience',
        'occupation',
        'workplace',
        'children_ids',
        'school_name',
        'school_code',
        'school_address',
        'principal_name',
        'school_phone',
        'school_email',
        'total_students',
        'total_teachers',
        'established_date',
        'profile_completed',
        'profile_completed_at',
        'completed_sections',
        'language',
        'timezone',
        'notification_preferences',
        'facebook_url',
        'line_id',
        'instagram_url',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'established_date' => 'date',
        'profile_completed' => 'boolean',
        'profile_completed_at' => 'datetime',
        'subjects' => 'array',
        'children_ids' => 'array',
        'completed_sections' => 'array',
        'notification_preferences' => 'array',
    ];

    /**
     * Get the user that owns the profile
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name) ?: $this->user->name;
    }

    /**
     * Check if profile is complete based on user role
     */
    public function isProfileComplete(): bool
    {
        if ($this->profile_completed) {
            return true;
        }

        $userRole = $this->user->roles->first()?->name;

        return match($userRole) {
            'student' => $this->isStudentProfileComplete(),
            'parent' => $this->isParentProfileComplete(),
            'teacher' => $this->isTeacherProfileComplete(),
            'school' => $this->isSchoolProfileComplete(),
            default => $this->isBasicProfileComplete(),
        };
    }

    /**
     * Check if basic profile fields are complete
     */
    private function isBasicProfileComplete(): bool
    {
        return !empty($this->first_name) &&
               !empty($this->last_name) &&
               !empty($this->phone_number);
    }

    /**
     * Check if student profile is complete
     */
    private function isStudentProfileComplete(): bool
    {
        return $this->isBasicProfileComplete() &&
               !empty($this->date_of_birth) &&
               !empty($this->gender) &&
               !empty($this->address) &&
               !empty($this->emergency_contact_name) &&
               !empty($this->emergency_contact_phone);
    }

    /**
     * Check if parent profile is complete
     */
    private function isParentProfileComplete(): bool
    {
        return $this->isBasicProfileComplete() &&
               !empty($this->occupation) &&
               !empty($this->address);
    }

    /**
     * Check if teacher profile is complete
     */
    private function isTeacherProfileComplete(): bool
    {
        return $this->isBasicProfileComplete() &&
               !empty($this->qualification) &&
               !empty($this->subjects) &&
               isset($this->years_of_experience);
    }

    /**
     * Check if school profile is complete
     */
    private function isSchoolProfileComplete(): bool
    {
        return $this->isBasicProfileComplete() &&
               !empty($this->school_name) &&
               !empty($this->school_code) &&
               !empty($this->school_address) &&
               !empty($this->principal_name);
    }

    /**
     * Mark profile as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'profile_completed' => true,
            'profile_completed_at' => now(),
        ]);
    }

    /**
     * Get profile completion percentage
     */
    public function getCompletionPercentage(): int
    {
        $userRole = $this->user->roles->first()?->name;

        return match($userRole) {
            'student' => $this->getStudentCompletionPercentage(),
            'parent' => $this->getParentCompletionPercentage(),
            'teacher' => $this->getTeacherCompletionPercentage(),
            'school' => $this->getSchoolCompletionPercentage(),
            default => $this->getBasicCompletionPercentage(),
        };
    }

    private function getBasicCompletionPercentage(): int
    {
        $fields = ['first_name', 'last_name', 'phone_number'];
        $completed = 0;

        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $completed++;
            }
        }

        return round(($completed / count($fields)) * 100);
    }

    private function getStudentCompletionPercentage(): int
    {
        $fields = [
            'first_name', 'last_name', 'phone_number', 'date_of_birth',
            'gender', 'address', 'emergency_contact_name', 'emergency_contact_phone'
        ];
        $completed = 0;

        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $completed++;
            }
        }

        return round(($completed / count($fields)) * 100);
    }

    private function getParentCompletionPercentage(): int
    {
        $fields = ['first_name', 'last_name', 'phone_number', 'occupation', 'address'];
        $completed = 0;

        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $completed++;
            }
        }

        return round(($completed / count($fields)) * 100);
    }

    private function getTeacherCompletionPercentage(): int
    {
        $fields = ['first_name', 'last_name', 'phone_number', 'qualification', 'subjects', 'years_of_experience'];
        $completed = 0;

        foreach ($fields as $field) {
            if ($field === 'subjects') {
                if (!empty($this->subjects) && is_array($this->subjects) && count($this->subjects) > 0) {
                    $completed++;
                }
            } elseif ($field === 'years_of_experience') {
                if (isset($this->years_of_experience)) {
                    $completed++;
                }
            } else {
                if (!empty($this->$field)) {
                    $completed++;
                }
            }
        }

        return round(($completed / count($fields)) * 100);
    }

    private function getSchoolCompletionPercentage(): int
    {
        $fields = [
            'first_name', 'last_name', 'phone_number', 'school_name',
            'school_code', 'school_address', 'principal_name'
        ];
        $completed = 0;

        foreach ($fields as $field) {
            if (!empty($this->$field)) {
                $completed++;
            }
        }

        return round(($completed / count($fields)) * 100);
    }
}
