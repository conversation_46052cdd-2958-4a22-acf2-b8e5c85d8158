<x-filament-panels::page>
    @php
        $user = auth()->user();
        $role = null;
        
        if ($user) {
            if ($user->hasRole('team_admin') || $user->hasRole('school')) {
                $role = 'admin';
            } elseif ($user->hasRole('teacher')) {
                $role = 'teacher';
            } elseif ($user->hasRole('parent')) {
                $role = 'parent';
            } elseif ($user->hasRole('student')) {
                $role = 'student';
            }
        }
    @endphp

    {{-- Role-specific welcome message --}}
    @if($role)
        <div class="mb-6 rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
            @switch($role)
                @case('admin')
                    <div class="flex items-center space-x-3">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900">
                            <x-heroicon-o-home class="h-6 w-6 text-blue-600 dark:text-blue-400" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">School Administration</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Manage your school operations, staff, and students</p>
                        </div>
                    </div>
                    @break

                @case('teacher')
                    <div class="flex items-center space-x-3">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900">
                            <x-heroicon-o-user class="h-6 w-6 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Teacher Portal</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Manage your classes, students, and academic activities</p>
                        </div>
                    </div>
                    @break

                @case('parent')
                    <div class="flex items-center space-x-3">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900">
                            <x-heroicon-o-heart class="h-6 w-6 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Parent Portal</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Track your children's progress and stay connected with school</p>
                        </div>
                    </div>
                    @break

                @case('student')
                    <div class="flex items-center space-x-3">
                        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-orange-100 dark:bg-orange-900">
                            <x-heroicon-o-book-open class="h-6 w-6 text-orange-600 dark:text-orange-400" />
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Student Portal</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Access your courses, assignments, and academic resources</p>
                        </div>
                    </div>
                    @break
            @endswitch
        </div>
    @endif

    {{-- Quick Actions based on role --}}
    @if($role)
        <div class="mb-6">
            <h4 class="mb-4 text-sm font-medium text-gray-900 dark:text-white">Quick Actions</h4>
            <div class="grid grid-cols-2 gap-4 sm:grid-cols-4">
                @switch($role)
                    @case('admin')
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-plus class="mb-2 h-6 w-6 text-blue-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Add Teacher</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-user class="mb-2 h-6 w-6 text-green-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Enroll Student</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-document class="mb-2 h-6 w-6 text-purple-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">View Reports</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-cog class="mb-2 h-6 w-6 text-gray-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Settings</span>
                        </a>
                        @break
                    
                    @case('teacher')
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-clipboard class="mb-2 h-6 w-6 text-green-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Take Attendance</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-document class="mb-2 h-6 w-6 text-blue-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Create Assignment</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-star class="mb-2 h-6 w-6 text-yellow-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Grade Work</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-envelope class="mb-2 h-6 w-6 text-purple-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Message Parents</span>
                        </a>
                        @break
                    
                    @case('parent')
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-document class="mb-2 h-6 w-6 text-purple-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">View Progress</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-envelope class="mb-2 h-6 w-6 text-blue-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Messages</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-banknotes class="mb-2 h-6 w-6 text-green-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Payments</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-calendar class="mb-2 h-6 w-6 text-orange-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Events</span>
                        </a>
                        @break
                    
                    @case('student')
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-document class="mb-2 h-6 w-6 text-blue-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Assignments</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-star class="mb-2 h-6 w-6 text-yellow-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">My Grades</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-clock class="mb-2 h-6 w-6 text-green-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Schedule</span>
                        </a>
                        <a href="#" class="flex flex-col items-center rounded-lg border border-gray-200 bg-white p-4 text-center hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-gray-700">
                            <x-heroicon-o-book-open class="mb-2 h-6 w-6 text-purple-600" />
                            <span class="text-xs text-gray-600 dark:text-gray-400">Resources</span>
                        </a>
                        @break
                @endswitch
            </div>
        </div>
    @endif

    {{-- Widgets --}}
    <x-filament-widgets::widgets
        :widgets="$this->getWidgets()"
        :columns="$this->getColumns()"
    />
</x-filament-panels::page>
