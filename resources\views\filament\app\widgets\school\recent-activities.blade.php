<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-clock class="w-5 h-5 text-primary-600 mr-2" />
                Recent Activities
            </div>
        </x-slot>

        <div class="space-y-4">
            @foreach($this->getActivities() as $activity)
                <div class="flex items-start space-x-3 p-3 rounded-lg
                    @if($activity['color'] === 'success') bg-success-50 dark:bg-success-900/20
                    @elseif($activity['color'] === 'info') bg-info-50 dark:bg-info-900/20
                    @elseif($activity['color'] === 'warning') bg-warning-50 dark:bg-warning-900/20
                    @endif">
                    
                    <div class="flex-shrink-0">
                        @svg($activity['icon'], 'w-5 h-5 ' . 
                            ($activity['color'] === 'success' ? 'text-success-600 dark:text-success-400' : 
                            ($activity['color'] === 'info' ? 'text-info-600 dark:text-info-400' : 
                            'text-warning-600 dark:text-warning-400')))
                    </div>
                    
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $activity['message'] }}
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {{ $activity['time'] }}
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
