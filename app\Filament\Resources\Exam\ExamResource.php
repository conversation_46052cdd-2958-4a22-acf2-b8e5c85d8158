<?php

namespace App\Filament\Resources\Exam;

use App\Filament\Resources\Exam\ExamResource\Pages;
use App\Models\Exam\Exam;
use App\Models\Subject;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ExamResource extends Resource
{
    protected static ?string $model = Exam::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationLabel = 'Exercises & Exams';

    protected static ?string $modelLabel = 'Exercise';

    protected static ?string $pluralModelLabel = 'Exercises & Exams';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'exercise' => 'Exercise',
                                'exam' => 'Exam',
                            ])
                            ->required()
                            ->default('exercise'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Question Settings')
                    ->schema([
                        Forms\Components\TextInput::make('total_questions')
                            ->label('Number of Questions')
                            ->numeric()
                            ->required()
                            ->default(10)
                            ->minValue(1)
                            ->maxValue(100),

                        Forms\Components\TextInput::make('total_score')
                            ->label('Total Score')
                            ->numeric()
                            ->required()
                            ->default(100)
                            ->minValue(1),

                        Forms\Components\TextInput::make('time_limit')
                            ->label('Time Limit (minutes)')
                            ->numeric()
                            ->minValue(1)
                            ->helperText('Leave empty for no time limit'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Randomization & Display')
                    ->schema([
                        Forms\Components\Toggle::make('randomize_questions')
                            ->label('Randomize Questions')
                            ->default(true),

                        Forms\Components\Toggle::make('randomize_choices')
                            ->label('Randomize Answer Choices')
                            ->default(true),

                        Forms\Components\Toggle::make('show_results_immediately')
                            ->label('Show Results Immediately')
                            ->default(false),

                        Forms\Components\Toggle::make('allow_multiple_attempts')
                            ->label('Allow Multiple Attempts')
                            ->default(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Availability')
                    ->schema([
                        Forms\Components\DateTimePicker::make('available_from')
                            ->label('Available From')
                            ->helperText('Leave empty to make available immediately'),

                        Forms\Components\DateTimePicker::make('available_until')
                            ->label('Available Until')
                            ->helperText('Leave empty for no end date'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->columns(3),
            ])
            ->mutateFormDataBeforeCreate(function (array $data): array {
                $data['team_id'] = filament()->getTenant()->id;
                $data['user_id'] = auth()->id();
                return $data;
            });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'exercise' => 'info',
                        'exam' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_questions')
                    ->label('Questions')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('total_score')
                    ->label('Total Score')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('time_limit')
                    ->label('Time Limit')
                    ->formatStateUsing(fn (?int $state): string => $state ? "{$state} min" : 'No limit')
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('attempts_count')
                    ->label('Attempts')
                    ->counts('attempts')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('available_from')
                    ->label('Available From')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('available_until')
                    ->label('Available Until')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('teacher.name')
                    ->label('Created By')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name'),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'exercise' => 'Exercise',
                        'exam' => 'Exam',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\Filter::make('available_now')
                    ->label('Available Now')
                    ->query(fn (Builder $query): Builder => $query->available()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('manage_questions')
                    ->label('Questions')
                    ->icon('heroicon-o-question-mark-circle')
                    ->url(fn (Exam $record): string => QuestionResource::getUrl('index', ['tenant' => filament()->getTenant()]))
                    ->color('info'),
                Tables\Actions\Action::make('view_attempts')
                    ->label('Attempts')
                    ->icon('heroicon-o-users')
                    ->url(fn (Exam $record): string => '#') // TODO: Create attempts view
                    ->color('success'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('team_id', filament()->getTenant()->id)
            ->with(['subject', 'teacher', 'attempts']);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExams::route('/'),
            'create' => Pages\CreateExam::route('/create'),
            'edit' => Pages\EditExam::route('/{record}/edit'),
        ];
    }
}
