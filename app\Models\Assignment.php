<?php

namespace App\Models;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Assignment extends Model
{
    use HasFactory, BelongsToTeam;

    protected $fillable = [
        'team_id',
        'subject_id',
        'lesson_id',
        'user_id', // teacher
        'title',
        'type',
        'total_score',
        'due_date',
        'description',
        'instructions',
        'is_active',
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'total_score' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the team that owns the assignment
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the subject that the assignment belongs to
     */
    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    /**
     * Get the lesson that the assignment belongs to
     */
    public function lesson(): BelongsTo
    {
        return $this->belongsTo(Lesson::class);
    }

    /**
     * Get the teacher (user) who created the assignment
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the submissions for this assignment
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    /**
     * Scope a query to only include active assignments
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by subject
     */
    public function scopeForSubject(Builder $query, int $subjectId): Builder
    {
        return $query->where('subject_id', $subjectId);
    }

    /**
     * Scope a query to filter by lesson
     */
    public function scopeForLesson(Builder $query, int $lessonId): Builder
    {
        return $query->where('lesson_id', $lessonId);
    }

    /**
     * Scope a query to filter by teacher
     */
    public function scopeForTeacher(Builder $query, int $teacherId): Builder
    {
        return $query->where('user_id', $teacherId);
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get assignments that are due soon (within next 7 days)
     */
    public function scopeDueSoon(Builder $query): Builder
    {
        return $query->where('due_date', '>=', now())
                    ->where('due_date', '<=', now()->addDays(7));
    }

    /**
     * Get overdue assignments
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('due_date', '<', now());
    }

    /**
     * Get the display title with subject
     */
    public function getDisplayTitleAttribute(): string
    {
        return "{$this->title} ({$this->subject->name})";
    }

    /**
     * Get the formatted due date
     */
    public function getFormattedDueDateAttribute(): string
    {
        return $this->due_date ? $this->due_date->format('M j, Y g:i A') : 'No due date';
    }

    /**
     * Check if assignment is overdue
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date && $this->due_date->isPast();
    }

    /**
     * Get submission count
     */
    public function getSubmissionCountAttribute(): int
    {
        return $this->submissions()->count();
    }

    /**
     * Get graded submission count
     */
    public function getGradedSubmissionCountAttribute(): int
    {
        return $this->submissions()->whereNotNull('score')->count();
    }
}
