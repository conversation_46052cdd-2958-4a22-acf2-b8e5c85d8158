<?php

namespace Database\Factories;

use App\Models\Book;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookFactory extends Factory
{
    protected $model = Book::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),
            'author' => $this->faker->name(),
            'description' => $this->faker->paragraph(),
            'isbn' => '978-' . str_pad($this->faker->numberBetween(1000000000, 9999999999), 10, '0', STR_PAD_LEFT),
            'publisher' => $this->faker->company(),
            'published_date' => $this->faker->dateTimeBetween('-10 years', 'now'),
            'edition' => $this->faker->numberBetween(1, 5),
            'pages' => $this->faker->numberBetween(100, 800),
            'language' => 'English',
            'is_active' => true,
        ];
    }
}
