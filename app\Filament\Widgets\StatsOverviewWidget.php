<?php

namespace App\Filament\Widgets;

use App\Models\Shop\Customer;
use App\Models\Shop\Order;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 0;

    protected function getStats(): array
    {
        $startDate = ! is_null($this->filters['startDate'] ?? null) ?
            Carbon::parse($this->filters['startDate']) :
            null;

        $endDate = ! is_null($this->filters['endDate'] ?? null) ?
            Carbon::parse($this->filters['endDate']) :
            now();

        // Get current tenant for filtering
        $currentTenant = Filament::getTenant();

        // Base queries filtered by current tenant
        $ordersQuery = Order::query();
        $customersQuery = Customer::query();
        $usersQuery = User::query();

        if ($currentTenant) {
            $ordersQuery->where('team_id', $currentTenant->id);
            $customersQuery->where('team_id', $currentTenant->id);
            $usersQuery->where('team_id', $currentTenant->id);
        }

        // Apply date filters
        if ($startDate) {
            $ordersQuery->where('created_at', '>=', $startDate);
            $customersQuery->where('created_at', '>=', $startDate);
            $usersQuery->where('created_at', '>=', $startDate);
        }

        if ($endDate) {
            $ordersQuery->where('created_at', '<=', $endDate);
            $customersQuery->where('created_at', '<=', $endDate);
            $usersQuery->where('created_at', '<=', $endDate);
        }

        // Apply business customers filter
        $isBusinessCustomersOnly = $this->filters['businessCustomersOnly'] ?? null;
        if ($isBusinessCustomersOnly !== null) {
            // Assuming business customers have a specific field or criteria
            // Adjust this based on your Customer model structure
            if ($isBusinessCustomersOnly) {
                $customersQuery->where('is_business', true);
            } else {
                $customersQuery->where('is_business', false);
            }
        }

        // Calculate actual stats
        $totalOrders = $ordersQuery->count();
        $totalRevenue = $ordersQuery->sum('total_price') ?? 0;
        $totalCustomers = $customersQuery->count();
        $totalUsers = $usersQuery->count();

        $formatNumber = function (int $number): string {
            if ($number < 1000) {
                return (string) Number::format($number, 0);
            }

            if ($number < 1000000) {
                return Number::format($number / 1000, 2) . 'k';
            }

            return Number::format($number / 1000000, 2) . 'm';
        };

        // Get team name for context
        $teamName = $currentTenant ? $currentTenant->name : 'All Teams';

        return [
            Stat::make('Revenue', '$' . $formatNumber((int) $totalRevenue))
                ->description("Total revenue for {$teamName}")
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
            Stat::make('Customers', $formatNumber($totalCustomers))
                ->description("Total customers in {$teamName}")
                ->descriptionIcon('heroicon-m-user-group')
                ->color('primary'),
            Stat::make('Orders', $formatNumber($totalOrders))
                ->description("Total orders for {$teamName}")
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('warning'),
            Stat::make('School Users', $formatNumber($totalUsers))
                ->description("Users in {$teamName}")
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),
        ];
    }
}
