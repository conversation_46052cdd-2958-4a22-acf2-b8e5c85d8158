<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class ClearTeamSettingsCache
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get current tenant from URL
        $currentTenantSlug = $request->route('tenant');
        
        // Get previous tenant from session
        $previousTenantSlug = Session::get('current_tenant_slug');
        
        // If tenant has changed, clear cache
        if ($currentTenantSlug && $currentTenantSlug !== $previousTenantSlug) {
            $this->clearSettingsCacheOnTeamSwitch($currentTenantSlug, $previousTenantSlug);
            
            // Update session with new tenant
            Session::put('current_tenant_slug', $currentTenantSlug);
        }
        
        return $next($request);
    }

    /**
     * Clear settings cache when switching teams (only team-specific cache)
     */
    protected function clearSettingsCacheOnTeamSwitch($currentTenantSlug, $previousTenantSlug): void
    {
        // Get team IDs from slugs
        $currentTeam = \App\Models\Team::where('slug', $currentTenantSlug)->first();
        $previousTeam = $previousTenantSlug ? \App\Models\Team::where('slug', $previousTenantSlug)->first() : null;

        // Clear cache for both teams (only settings cache, not Laravel cache)
        if ($currentTeam) {
            clear_team_settings_cache($currentTeam->id);
        }

        if ($previousTeam) {
            clear_team_settings_cache($previousTeam->id);
        }

        // Clear specific cache keys that might be related to settings
        Cache::forget('settings_data.all');
        Cache::forget('app_settings_cache');

        // Log the cache clearing for debugging
        Log::info('Team settings cache cleared on team switch', [
            'previous_team' => $previousTenantSlug,
            'current_team' => $currentTenantSlug,
            'previous_team_id' => $previousTeam?->id,
            'current_team_id' => $currentTeam?->id,
            'cache_type' => 'team_settings_only'
        ]);
    }
}
