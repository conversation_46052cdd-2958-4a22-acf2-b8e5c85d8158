<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherQuickAccessWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-quick-access';
    
    protected int | string | array $columnSpan = [
        'md' => 1,
        'xl' => 1,
    ];

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getQuickActions(): array
    {
        return [
            [
                'label' => 'เพิ่มตารางสอน',
                'icon' => 'heroicon-o-calendar-plus',
                'url' => '#',
                'color' => 'primary'
            ],
            [
                'label' => 'สร้างแบบทดสอบ',
                'icon' => 'heroicon-o-document-plus',
                'url' => '#',
                'color' => 'success'
            ],
            [
                'label' => 'เพิ่มนักเรียน',
                'icon' => 'heroicon-o-user-plus',
                'url' => '#',
                'color' => 'info'
            ],
            [
                'label' => 'ค้นหาสื่อการสอน',
                'icon' => 'heroicon-o-book-open',
                'url' => '#',
                'color' => 'warning'
            ],
        ];
    }
}
