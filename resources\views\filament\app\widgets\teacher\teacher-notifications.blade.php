<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-bell class="w-5 h-5 text-primary-600 mr-2" />
                การแจ้งเตือน
            </div>
        </x-slot>

        <div class="space-y-3">
            @foreach($this->getNotifications() as $notification)
                <div class="rounded-r-md p-3 border-l-4
                    @if($notification['color'] === 'red') border-red-500 bg-red-50 dark:bg-red-900/20
                    @elseif($notification['color'] === 'yellow') border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20
                    @elseif($notification['color'] === 'green') border-green-500 bg-green-50 dark:bg-green-900/20
                    @endif">
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-3">
                            @svg($notification['icon'], 'w-5 h-5 ' . 
                                ($notification['color'] === 'red' ? 'text-red-600 dark:text-red-400' : 
                                ($notification['color'] === 'yellow' ? 'text-yellow-600 dark:text-yellow-400' : 
                                'text-green-600 dark:text-green-400')))
                        </div>
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 dark:text-white">{{ $notification['title'] }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $notification['description'] }}</p>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
