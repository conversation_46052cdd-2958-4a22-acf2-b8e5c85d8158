# SVG Integration Guide for EduNest

## Overview
This guide shows you how to download SVG files and integrate them into your `components.animated-svg` system.

## Directory Structure
```
resources/
  views/
    components/
      animated-svg.blade.php     ← Main component file
      svg/
        icons/                   ← Individual icon components
          default.blade.php
          education.blade.php
          download.blade.php
        illustrations/           ← Larger SVG illustrations
public/
  images/
    svg/                        ← External SVG files
```

## Method 1: Using Custom Icon Components

### Step 1: Create Icon Component
Create a new file in `resources/views/components/svg/icons/your-icon.blade.php`:

```blade
{{-- Your Custom Icon --}}
<svg viewBox="0 0 24 24" fill="none" stroke="{{ $color ?? 'currentColor' }}" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <!-- Paste your downloaded SVG path here -->
    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
</svg>
```

### Step 2: Use in Your Pages
```blade
@include('components.animated-svg', [
    'type' => 'custom-icon',
    'icon' => 'your-icon',           // File name without .blade.php
    'size' => '16',                  // Tailwind size class
    'animation' => 'pulse-glow-svg', // Animation class
    'color' => '#06b6d4'            // Custom color
])
```

## Method 2: External SVG Files

### Step 1: Save SVG File
1. Download your SVG file
2. Save it to `public/images/svg/your-file.svg`

### Step 2: Use External SVG
```blade
@include('components.animated-svg', [
    'type' => 'external-svg',
    'file' => 'your-file.svg',      // Filename in public/images/svg/
    'size' => '24',                 // Size
    'animation' => 'breathing-svg', // Animation
    'filter' => 'hue-rotate(180deg)' // CSS filter
])
```

## Method 3: Inline Custom SVG

### Direct SVG Content
```blade
@include('components.animated-svg', [
    'type' => 'inline-custom',
    'size' => '20',
    'viewBox' => '0 0 24 24',
    'color' => '#3b82f6',
    'animation' => 'elastic-bounce',
    'svgContent' => '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
])
```

## Available Animation Classes

| Class | Effect | Best For |
|-------|--------|----------|
| `pulse-glow-svg` | Pulsing glow | Buttons, notifications |
| `breathing-svg` | Breathing scale | Decorative elements |
| `elastic-bounce` | Bouncing | Interactive elements |
| `orbital-animation` | Circular rotation | Loading states |
| `spiral-svg` | Spiral rotation | Complex animations |
| `heartbeat-svg` | Heartbeat pulse | Progress indicators |
| `kaleidoscope-svg` | Color shifting | Artistic elements |

## Popular SVG Resources

### Free Resources
- **Heroicons**: https://heroicons.com/ (Tailwind team)
- **Feather Icons**: https://feathericons.com/ (Minimal)
- **Lucide**: https://lucide.dev/ (Beautiful icons)
- **Tabler Icons**: https://tabler-icons.io/ (4000+ icons)
- **Phosphor Icons**: https://phosphoricons.com/ (Flexible)

### Illustrations
- **Undraw**: https://undraw.co/ (Free illustrations)
- **Storyset**: https://storyset.com/ (Animated illustrations)
- **Manypixels**: https://www.manypixels.co/gallery (Free illustrations)

## Step-by-Step Example

### 1. Download from Heroicons
1. Go to https://heroicons.com/
2. Find "academic-cap" icon
3. Copy the SVG code

### 2. Create Component
Create `resources/views/components/svg/icons/academic-cap.blade.php`:
```blade
<svg viewBox="0 0 24 24" fill="none" stroke="{{ $color ?? 'currentColor' }}" stroke-width="2">
    <path d="M12 14l9-5-9-5-9 5 9 5z"/>
    <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
    <path stroke-linecap="round" stroke-linejoin="round" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"/>
</svg>
```

### 3. Use in Your Page
```blade
<div class="hero-section">
    <h1>Education Platform</h1>
    
    <!-- Add animated academic cap icon -->
    <div class="absolute top-10 right-10">
        @include('components.animated-svg', [
            'type' => 'custom-icon',
            'icon' => 'academic-cap',
            'size' => '16',
            'animation' => 'pulse-glow-svg',
            'color' => '#06b6d4'
        ])
    </div>
</div>
```

## Best Practices

1. **Optimize SVGs**: Remove unnecessary attributes and comments
2. **Use viewBox**: Always include viewBox for scalability
3. **Color Variables**: Use `{{ $color ?? 'currentColor' }}` for flexibility
4. **Performance**: Don't use too many complex animations on one page
5. **Accessibility**: Add appropriate alt text for external SVGs
6. **Consistency**: Use your theme colors for better design harmony

## Troubleshooting

### SVG Not Showing
- Check file path is correct
- Ensure SVG has proper viewBox
- Verify component name matches file name

### Animation Not Working
- Check animation class exists in layout.css
- Ensure proper CSS is loaded
- Verify browser compatibility

### Color Not Changing
- Use `stroke="{{ $color ?? 'currentColor' }}"` for outline icons
- Use `fill="{{ $color ?? 'currentColor' }}"` for filled icons
- Check if SVG has hardcoded colors

## Quick Reference

```blade
{{-- Basic usage --}}
@include('components.animated-svg', ['type' => 'custom-icon', 'icon' => 'education'])

{{-- With animation --}}
@include('components.animated-svg', ['type' => 'custom-icon', 'icon' => 'education', 'animation' => 'pulse-glow-svg'])

{{-- External file --}}
@include('components.animated-svg', ['type' => 'external-svg', 'file' => 'my-icon.svg'])

{{-- Inline custom --}}
@include('components.animated-svg', ['type' => 'inline-custom', 'svgContent' => '<circle cx="12" cy="12" r="10"/>'])
```

That's it! You now have a complete system for integrating any downloaded SVG into your animated component system.
