<?php

namespace App\Filament\Resources\MediaManager\JsonResources;

use Illuminate\Http\Resources\Json\JsonResource;

class FoldersResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'color' => $this->color,
            'icon' => $this->icon,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
