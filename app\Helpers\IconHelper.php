<?php

namespace App\Helpers;

class IconHelper
{
    /**
     * List of commonly available Heroicons (outline versions)
     */
    public static array $safeIcons = [
        // Navigation & Interface
        'home', 'bars-3', 'x-mark', 'chevron-left', 'chevron-right', 
        'chevron-up', 'chevron-down', 'arrow-left', 'arrow-right',
        
        // Actions & Controls
        'plus', 'minus', 'pencil', 'trash', 'eye', 'eye-slash', 
        'cog-6-tooth', 'adjustments-horizontal',
        
        // Users & People
        'user', 'users', 'user-plus', 'user-minus', 'user-circle', 
        'user-group', 'academic-cap',
        
        // Communication
        'envelope', 'envelope-open', 'chat-bubble-left', 'phone', 
        'bell', 'megaphone',
        
        // Documents & Files
        'document', 'document-text', 'document-plus', 'folder', 
        'folder-open', 'clipboard', 'book-open',
        
        // Time & Status
        'clock', 'calendar', 'calendar-days', 'play', 'pause', 
        'stop', 'check', 'check-circle', 'x-circle',
        
        // Business & Finance
        'banknotes', 'credit-card', 'currency-dollar', 'shopping-cart', 
        'shopping-bag', 'gift',
        
        // Data & Analytics
        'chart-bar', 'chart-pie', 'presentation-chart-line', 
        'table-cells', 'list-bullet', 'squares-2x2',
        
        // Technology
        'computer-desktop', 'device-phone-mobile', 'wifi', 'globe-alt', 
        'magnifying-glass', 'link',
        
        // Miscellaneous
        'star', 'heart', 'tag', 'share', 'printer', 'key', 
        'lock-closed', 'shield-check', 'exclamation-triangle',
        'information-circle', 'question-mark-circle'
    ];

    /**
     * Icon fallbacks for commonly problematic icons
     */
    public static array $fallbacks = [
        'building-office-2' => 'home',
        'chat-bubble-left-right' => 'chat-bubble-left',
        'clipboard-document-check' => 'clipboard',
        'clipboard-document-list' => 'clipboard',
        'cog' => 'cog-6-tooth',
        'document-text' => 'document',
        'academic-cap' => 'user',
        'building-office' => 'home',
    ];

    /**
     * Get a safe icon name, with fallback if needed
     */
    public static function getSafeIcon(string $iconName): string
    {
        // Remove heroicon prefix if present
        $iconName = str_replace(['heroicon-o-', 'heroicon-s-'], '', $iconName);
        
        // Check if icon is in safe list
        if (in_array($iconName, self::$safeIcons)) {
            return $iconName;
        }
        
        // Check if we have a fallback
        if (isset(self::$fallbacks[$iconName])) {
            return self::$fallbacks[$iconName];
        }
        
        // Default fallback
        return 'home';
    }

    /**
     * Get the full component name for an icon
     */
    public static function getComponent(string $iconName, bool $solid = false): string
    {
        $safeIcon = self::getSafeIcon($iconName);
        $prefix = $solid ? 'heroicon-s-' : 'heroicon-o-';
        
        return $prefix . $safeIcon;
    }

    /**
     * Check if an icon is considered safe to use
     */
    public static function isSafe(string $iconName): bool
    {
        $iconName = str_replace(['heroicon-o-', 'heroicon-s-'], '', $iconName);
        return in_array($iconName, self::$safeIcons);
    }

    /**
     * Get all safe icons grouped by category
     */
    public static function getIconsByCategory(): array
    {
        return [
            'Navigation' => [
                'home', 'bars-3', 'x-mark', 'chevron-left', 'chevron-right', 
                'chevron-up', 'chevron-down', 'arrow-left', 'arrow-right'
            ],
            'Actions' => [
                'plus', 'minus', 'pencil', 'trash', 'eye', 'eye-slash', 
                'cog-6-tooth', 'adjustments-horizontal'
            ],
            'Users' => [
                'user', 'users', 'user-plus', 'user-minus', 'user-circle', 
                'user-group', 'academic-cap'
            ],
            'Communication' => [
                'envelope', 'envelope-open', 'chat-bubble-left', 'phone', 
                'bell', 'megaphone'
            ],
            'Documents' => [
                'document', 'document-text', 'document-plus', 'folder', 
                'folder-open', 'clipboard', 'book-open'
            ],
            'Time & Status' => [
                'clock', 'calendar', 'calendar-days', 'play', 'pause', 
                'stop', 'check', 'check-circle', 'x-circle'
            ],
            'Business' => [
                'banknotes', 'credit-card', 'currency-dollar', 'shopping-cart', 
                'shopping-bag', 'gift'
            ],
            'Analytics' => [
                'chart-bar', 'chart-pie', 'presentation-chart-line', 
                'table-cells', 'list-bullet', 'squares-2x2'
            ],
            'Technology' => [
                'computer-desktop', 'device-phone-mobile', 'wifi', 'globe-alt', 
                'magnifying-glass', 'link'
            ],
            'Miscellaneous' => [
                'star', 'heart', 'tag', 'share', 'printer', 'key', 
                'lock-closed', 'shield-check', 'exclamation-triangle',
                'information-circle', 'question-mark-circle'
            ]
        ];
    }

    /**
     * Generate a random safe icon (useful for testing)
     */
    public static function random(): string
    {
        return self::$safeIcons[array_rand(self::$safeIcons)];
    }
}
