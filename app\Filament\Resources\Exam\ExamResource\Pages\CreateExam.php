<?php

namespace App\Filament\Resources\Exam\ExamResource\Pages;

use App\Filament\Resources\Exam\ExamResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Facades\Filament;

class CreateExam extends CreateRecord
{
    protected static string $resource = ExamResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Automatically set team_id and user_id
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $data['team_id'] = Filament::getTenant()->id;
        }

        $data['user_id'] = auth()->id();

        return $data;
    }
}
