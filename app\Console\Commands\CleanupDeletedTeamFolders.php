<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupDeletedTeamFolders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:cleanup-deleted-teams {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up folders for deleted teams (folders ending with -deleted)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('DRY RUN MODE - No files will be deleted');
        }

        $teamsPath = 'teams';
        
        if (!Storage::disk('public')->exists($teamsPath)) {
            $this->info('No teams directory found.');
            return;
        }

        $directories = Storage::disk('public')->directories($teamsPath);
        $deletedFolders = array_filter($directories, function ($dir) {
            return str_ends_with(basename($dir), '-deleted');
        });

        if (empty($deletedFolders)) {
            $this->info('No deleted team folders found.');
            return;
        }

        $this->info("Found " . count($deletedFolders) . " deleted team folders:");

        foreach ($deletedFolders as $folder) {
            $folderName = basename($folder);
            $this->line("  - {$folderName}");
            
            if (!$dryRun) {
                if ($this->confirm("Delete folder '{$folderName}' and all its contents?")) {
                    Storage::disk('public')->deleteDirectory($folder);
                    $this->info("    ✓ Deleted: {$folderName}");
                } else {
                    $this->comment("    - Skipped: {$folderName}");
                }
            }
        }

        if ($dryRun) {
            $this->info("\nTo actually delete these folders, run the command without --dry-run");
        } else {
            $this->info("\nCleanup completed!");
        }
    }
}
