<?php

namespace App\Filament\Resources\TaskResource\Pages;

use App\Filament\Resources\TaskResource;
use App\Models\Task;
use App\Models\TeachingSchedule;
use App\Models\User;
use App\Models\Subject;
use App\Models\Classroom;
use Filament\Facades\Filament;
use Filament\Resources\Pages\Page;
use Filament\Actions;
use Filament\Forms;

class TaskTimetable extends Page
{
    protected static string $resource = TaskResource::class;

    protected static string $view = 'filament.resources.task-resource.pages.task-timetable';

    protected static ?string $title = 'Task Calendar';

    protected static ?string $navigationLabel = 'Calendar';

    public $selectedDate;
    public $viewMode = 'week'; // week, day, month
    public $editingTask = null;
    public $editingSchedule = null;

    // Filter properties
    public $showTasks = true;
    public $showSchedules = true;
    public $showAssignedToMe = true;
    public $showMyTasks = true;

    // Form data for editing
    public $editTaskData = [];
    public $editScheduleData = [];

    public function mount(): void
    {
        $this->selectedDate = now()->format('Y-m-d');
    }

    protected function getTaskForm(): array
    {
        return [
            Forms\Components\Section::make('Task Details')
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Enter task title'),

                    Forms\Components\Textarea::make('description')
                        ->rows(3)
                        ->placeholder('Enter task description (optional)'),

                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_datetime')
                                ->label('Start Date & Time')
                                ->required()
                                ->default(now()->addHour()->startOfHour())
                                ->seconds(false),

                            Forms\Components\DateTimePicker::make('end_datetime')
                                ->label('End Date & Time')
                                ->seconds(false)
                                ->after('start_datetime'),
                        ]),

                    Forms\Components\Grid::make(3)
                        ->schema([
                            Forms\Components\Select::make('priority')
                                ->options([
                                    'low' => 'Low',
                                    'medium' => 'Medium',
                                    'high' => 'High',
                                    'urgent' => 'Urgent',
                                ])
                                ->default('medium')
                                ->required(),

                            Forms\Components\Select::make('status')
                                ->options([
                                    'pending' => 'Pending',
                                    'in_progress' => 'In Progress',
                                    'completed' => 'Completed',
                                    'cancelled' => 'Cancelled',
                                ])
                                ->default('pending')
                                ->required(),

                            Forms\Components\ColorPicker::make('color')
                                ->default('#3B82F6'),
                        ]),

                    Forms\Components\TextInput::make('location')
                        ->placeholder('Meeting room, address, etc.'),
                ]),

            Forms\Components\Section::make('Alerts & Assignment')
                ->schema([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\Toggle::make('has_alert')
                                ->label('Enable Alert'),

                            Forms\Components\Select::make('assigned_to')
                                ->label('Assign To')
                                ->options(function () {
                                    return User::where('team_id', Filament::getTenant()->id)
                                        ->pluck('name', 'id');
                                })
                                ->searchable()
                                ->placeholder('Select user to assign'),
                        ]),
                ]),
        ];
    }

    protected function getScheduleForm(): array
    {
        return [
            Forms\Components\Section::make('Schedule Details')
                ->schema([
                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\Select::make('subject_id')
                                ->label('Subject')
                                ->options(function () {
                                    return Subject::where('team_id', Filament::getTenant()->id)
                                        ->pluck('name', 'id');
                                })
                                ->required()
                                ->searchable()
                                ->placeholder('Select subject'),

                            Forms\Components\Select::make('classroom_id')
                                ->label('Classroom')
                                ->options(function () {
                                    return Classroom::where('team_id', Filament::getTenant()->id)
                                        ->pluck('room_name', 'id');
                                })
                                ->required()
                                ->searchable()
                                ->placeholder('Select classroom'),
                        ]),

                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\DateTimePicker::make('start_time')
                                ->label('Start Date & Time')
                                ->required()
                                ->default(now()->addHour()->startOfHour())
                                ->seconds(false),

                            Forms\Components\DateTimePicker::make('end_time')
                                ->label('End Date & Time')
                                ->required()
                                ->seconds(false)
                                ->after('start_time'),
                        ]),

                    Forms\Components\Grid::make(2)
                        ->schema([
                            Forms\Components\Select::make('user_id')
                                ->label('Teacher')
                                ->options(function () {
                                    return User::where('team_id', Filament::getTenant()->id)
                                        ->whereHas('roles', function ($query) {
                                            $query->where('name', 'teacher');
                                        })
                                        ->pluck('name', 'id');
                                })
                                ->required()
                                ->searchable()
                                ->placeholder('Select teacher'),

                            Forms\Components\Select::make('status')
                                ->options([
                                    'scheduled' => 'Scheduled',
                                    'in_progress' => 'In Progress',
                                    'completed' => 'Completed',
                                    'cancelled' => 'Cancelled',
                                ])
                                ->default('scheduled')
                                ->required(),
                        ]),

                    Forms\Components\Textarea::make('notes')
                        ->rows(2)
                        ->placeholder('Additional notes (optional)'),
                ]),
        ];
    }

    protected function createTask(array $data): void
    {
        $data['team_id'] = Filament::getTenant()->id;
        $data['user_id'] = auth()->id();

        // Set default end time if not provided
        if (!isset($data['end_datetime']) && isset($data['start_datetime'])) {
            $data['end_datetime'] = \Carbon\Carbon::parse($data['start_datetime'])->addHour();
        }

        // Calculate alert datetime if alert is enabled
        if ($data['has_alert'] && isset($data['alert_minutes_before'])) {
            $data['alert_datetime'] = \Carbon\Carbon::parse($data['start_datetime'])
                ->subMinutes($data['alert_minutes_before']);
        }

        Task::create($data);

        // Show success notification
        \Filament\Notifications\Notification::make()
            ->title('Task Created')
            ->body('The task has been created successfully.')
            ->success()
            ->send();
    }

    protected function createSchedule(array $data): void
    {
        $data['team_id'] = Filament::getTenant()->id;
        $data['user_id'] = auth()->id();

        TeachingSchedule::create($data);

        // Show success notification
        \Filament\Notifications\Notification::make()
            ->title('Schedule Created')
            ->body('The teaching schedule has been created successfully.')
            ->success()
            ->send();
    }

    // Quick action methods
    public function quickCompleteTask($taskId): void
    {
        $task = Task::find($taskId);

        if ($task && $task->team_id === Filament::getTenant()->id) {
            $task->update(['status' => 'completed']);

            \Filament\Notifications\Notification::make()
                ->title('Task Completed')
                ->body('The task has been marked as completed.')
                ->success()
                ->send();
        }
    }

    public function quickCompleteSchedule($scheduleId): void
    {
        $schedule = TeachingSchedule::find($scheduleId);

        if ($schedule && $schedule->team_id === Filament::getTenant()->id) {
            $schedule->update(['status' => 'completed']);

            \Filament\Notifications\Notification::make()
                ->title('Schedule Completed')
                ->body('The teaching schedule has been marked as completed.')
                ->success()
                ->send();
        }
    }

    public function quickUpdateTaskStatus($taskId, $status): void
    {
        $task = Task::find($taskId);

        if ($task && $task->team_id === Filament::getTenant()->id) {
            $task->update(['status' => $status]);

            $statusLabel = ucfirst(str_replace('_', ' ', $status));
            \Filament\Notifications\Notification::make()
                ->title('Task Updated')
                ->body("The task has been marked as {$statusLabel}.")
                ->success()
                ->send();
        }
    }

    public function quickUpdateScheduleStatus($scheduleId, $status): void
    {
        $schedule = TeachingSchedule::find($scheduleId);

        if ($schedule && $schedule->team_id === Filament::getTenant()->id) {
            $schedule->update(['status' => $status]);

            $statusLabel = ucfirst(str_replace('_', ' ', $status));
            \Filament\Notifications\Notification::make()
                ->title('Schedule Updated')
                ->body("The teaching schedule has been marked as {$statusLabel}.")
                ->success()
                ->send();
        }
    }

    public function quickEditTask($taskId): void
    {
        $task = Task::find($taskId);

        if ($task && $task->team_id === Filament::getTenant()->id) {
            $this->editingTask = $task;

            // Populate form data
            $this->editTaskData = [
                'title' => $task->title,
                'description' => $task->description,
                'start_datetime' => $task->start_datetime,
                'end_datetime' => $task->end_datetime,
                'priority' => $task->priority,
                'status' => $task->status,
                'color' => $task->color ?? '#3B82F6',
                'location' => $task->location,
                'has_alert' => $task->has_alert,
                'assigned_to' => $task->assigned_to,
            ];

            // Debug: Add notification to see if method is called
            // \Filament\Notifications\Notification::make()
            //     ->title('Debug: quickEditTask called')
            //     ->body("Task ID: {$taskId}, Title: {$task->title}")
            //     ->info()
            //     ->send();

            try {
                $this->mountAction('edit_task');
            } catch (\Exception $e) {
                \Filament\Notifications\Notification::make()
                    ->title('Error mounting action')
                    ->body($e->getMessage())
                    ->danger()
                    ->send();
            }
        }
    }

    public function quickEditSchedule($scheduleId): void
    {
        $schedule = TeachingSchedule::find($scheduleId);

        if ($schedule && $schedule->team_id === Filament::getTenant()->id) {
            $this->editingSchedule = $schedule;

            // Populate form data
            $this->editScheduleData = [
                'subject_id' => $schedule->subject_id,
                'classroom_id' => $schedule->classroom_id,
                'start_time' => $schedule->start_time,
                'end_time' => $schedule->end_time,
                'user_id' => $schedule->user_id,
                'status' => $schedule->status ?? 'scheduled',
                'notes' => $schedule->notes,
            ];

            $this->mountAction('edit_schedule');
        }
    }





    public function quickDeleteTask($taskId): void
    {
        $task = Task::find($taskId);

        if ($task && $task->team_id === Filament::getTenant()->id) {
            $task->delete();

            \Filament\Notifications\Notification::make()
                ->title('Task Deleted')
                ->body('The task has been deleted successfully.')
                ->success()
                ->send();
        }
    }

    public function quickDeleteSchedule($scheduleId): void
    {
        $schedule = TeachingSchedule::find($scheduleId);

        if ($schedule && $schedule->team_id === Filament::getTenant()->id) {
            $schedule->delete();

            \Filament\Notifications\Notification::make()
                ->title('Schedule Deleted')
                ->body('The teaching schedule has been deleted successfully.')
                ->success()
                ->send();
        }
    }



    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('list_view')
                ->label('List View')
                ->icon('heroicon-o-list-bullet')
                ->url(fn (): string => TaskResource::getUrl('list'))
                ->color('gray'),
            Actions\Action::make('new_schedule')
                ->label('Quick Add Schedule')
                ->icon('heroicon-o-academic-cap')
                ->color('success')
                ->tooltip('Ctrl+Shift+N')
                ->modal()
                ->modalHeading('Quick Add Teaching Schedule')
                ->modalDescription('Add a new teaching schedule to your calendar.')
                ->modalSubmitActionLabel('Create Schedule')
                ->form($this->getScheduleForm())
                ->fillForm(function (array $arguments): array {
                    // Pre-fill form with datetime if provided
                    if (isset($arguments['start_time'])) {
                        $startDateTime = \Carbon\Carbon::parse($arguments['start_time']);
                        return [
                            'start_time' => $startDateTime,
                            'end_time' => $startDateTime->copy()->addHour(),
                        ];
                    }
                    return [];
                })
                ->action(function (array $data): void {
                    $this->createSchedule($data);
                }),
            Actions\Action::make('new_task')
                ->label('New Task')
                ->icon('heroicon-o-plus')
                ->color('primary')
                ->tooltip('Ctrl+N')
                ->modal()
                ->modalHeading('Create New Task')
                ->modalDescription('Create a new task and add it to your calendar.')
                ->modalSubmitActionLabel('Create Task')
                ->form($this->getTaskForm())
                ->fillForm(function (array $arguments): array {
                    // Pre-fill form with datetime if provided
                    if (isset($arguments['start_datetime'])) {
                        $startDateTime = \Carbon\Carbon::parse($arguments['start_datetime']);
                        return [
                            'start_datetime' => $startDateTime,
                            'end_datetime' => $startDateTime->copy()->addHour(),
                        ];
                    }
                    return [];
                })
                ->action(function (array $data): void {
                    $this->createTask($data);
                }),

            // Edit actions (hidden from header)
            Actions\Action::make('edit_task')
                ->label('Edit Task')
                ->modal()
                ->modalHeading('Edit Task')
                ->modalDescription('Update task details and settings.')
                ->modalSubmitActionLabel('Update Task')
                ->form($this->getTaskForm())
                ->fillForm(fn(): array => $this->editTaskData)
                ->action(function (array $data): void {
                    if ($this->editingTask) {
                        $this->editingTask->update($data);
                        \Filament\Notifications\Notification::make()
                            ->title('Task Updated')
                            ->body('The task has been updated successfully.')
                            ->success()
                            ->send();
                        $this->editingTask = null;
                        $this->editTaskData = [];
                    }
                })
                ->color('warning')
                ->icon('heroicon-o-pencil')
                ->extraAttributes(['class' => 'hidden-edit-action']),

            Actions\Action::make('edit_schedule')
                ->label('Edit Schedule')
                ->modal()
                ->modalHeading('Edit Teaching Schedule')
                ->modalDescription('Update schedule details and settings.')
                ->modalSubmitActionLabel('Update Schedule')
                ->form($this->getScheduleForm())
                ->fillForm(fn(): array => $this->editScheduleData)
                ->action(function (array $data): void {
                    if ($this->editingSchedule) {
                        $this->editingSchedule->update($data);
                        \Filament\Notifications\Notification::make()
                            ->title('Schedule Updated')
                            ->body('The teaching schedule has been updated successfully.')
                            ->success()
                            ->send();
                        $this->editingSchedule = null;
                        $this->editScheduleData = [];
                    }
                })
                ->color('warning')
                ->icon('heroicon-o-academic-cap')
                ->extraAttributes(['class' => 'hidden-edit-action']),
        ];
    }

    public function getTasks()
    {
        // Return empty collection if tasks are filtered out
        if (!$this->showTasks && !$this->showAssignedToMe && !$this->showMyTasks) {
            return collect();
        }

        $query = Task::query();

        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        // Apply user-based filters
        $currentUserId = auth()->id();
        $userFilters = [];

        if ($this->showMyTasks) {
            $userFilters[] = ['user_id', '=', $currentUserId];
        }

        if ($this->showAssignedToMe) {
            $userFilters[] = ['assigned_to', '=', $currentUserId];
        }

        if (!empty($userFilters)) {
            $query->where(function ($q) use ($userFilters) {
                foreach ($userFilters as $filter) {
                    $q->orWhere($filter[0], $filter[1], $filter[2]);
                }
            });
        }

        // Filter by selected date range based on view mode
        switch ($this->viewMode) {
            case 'day':
                $query->whereDate('start_datetime', $this->selectedDate);
                break;
            case 'week':
                $startOfWeek = now()->parse($this->selectedDate)->startOfWeek();
                $endOfWeek = now()->parse($this->selectedDate)->endOfWeek();
                $query->whereBetween('start_datetime', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
                $startOfMonth = now()->parse($this->selectedDate)->startOfMonth();
                $endOfMonth = now()->parse($this->selectedDate)->endOfMonth();
                $query->whereBetween('start_datetime', [$startOfMonth, $endOfMonth]);
                break;
        }

        return $query->with(['assignedUser', 'user'])->orderBy('start_datetime', 'asc')->get();
    }

    public function getTeachingSchedules()
    {
        // Return empty collection if schedules are filtered out
        if (!$this->showSchedules) {
            return collect();
        }

        $query = TeachingSchedule::query();

        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        // Filter by selected date range based on view mode
        switch ($this->viewMode) {
            case 'day':
                $query->whereDate('start_time', $this->selectedDate);
                break;
            case 'week':
                $startOfWeek = now()->parse($this->selectedDate)->startOfWeek();
                $endOfWeek = now()->parse($this->selectedDate)->endOfWeek();
                $query->whereBetween('start_time', [$startOfWeek, $endOfWeek]);
                break;
            case 'month':
                $startOfMonth = now()->parse($this->selectedDate)->startOfMonth();
                $endOfMonth = now()->parse($this->selectedDate)->endOfMonth();
                $query->whereBetween('start_time', [$startOfMonth, $endOfMonth]);
                break;
        }

        return $query->with(['teacher', 'classroom', 'subject', 'lesson'])->orderBy('start_time', 'asc')->get();
    }

    public function changeDate($date)
    {
        $this->selectedDate = $date;
    }

    public function changeViewMode($mode)
    {
        $this->viewMode = $mode;
    }

    // Filter toggle methods
    public function toggleTasks()
    {
        $this->showTasks = !$this->showTasks;
    }

    public function toggleSchedules()
    {
        $this->showSchedules = !$this->showSchedules;
    }

    public function toggleAssignedToMe()
    {
        $this->showAssignedToMe = !$this->showAssignedToMe;
    }

    public function toggleMyTasks()
    {
        $this->showMyTasks = !$this->showMyTasks;
    }

    public function showAll()
    {
        $this->showTasks = true;
        $this->showSchedules = true;
        $this->showAssignedToMe = true;
        $this->showMyTasks = true;
    }

    public function updateTaskPosition($taskId, $newDateTime, $newEndDateTime = null)
    {
        try {
            // Debug log the received data
            // \Log::info('updateTaskPosition called', [
            //     'taskId' => $taskId,
            //     'newDateTime' => $newDateTime,
            //     'newEndDateTime' => $newEndDateTime
            // ]);

            $task = Task::find($taskId);

            if (!$task || $task->team_id !== Filament::getTenant()->id) {
                $this->addError('task', 'Task not found or access denied.');
                return;
            }

            // Validate the datetime string
            if (empty($newDateTime)) {
                $this->addError('task', 'New datetime is required.');
                return;
            }

            // Parse the new datetime
            $newStartDateTime = \Carbon\Carbon::parse($newDateTime);

            // Calculate new end datetime based on original duration
            $newEndDateTime = null;
            if ($task->end_datetime) {
                $originalDuration = $task->start_datetime->diffInMinutes($task->end_datetime);
                $newEndDateTime = $newStartDateTime->copy()->addMinutes($originalDuration);
            }

            \Log::info('Updating task', [
                'task_id' => $task->id,
                'old_start' => $task->start_datetime,
                'new_start' => $newStartDateTime,
                'old_end' => $task->end_datetime,
                'new_end' => $newEndDateTime
            ]);

            $task->update([
                'start_datetime' => $newStartDateTime,
                'end_datetime' => $newEndDateTime,
            ]);

            $this->dispatch('task-moved', [
                'taskId' => $taskId,
                'newDateTime' => $newStartDateTime->format('Y-m-d H:i:s'),
                'message' => 'Task moved successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to move task', [
                'taskId' => $taskId,
                'newDateTime' => $newDateTime,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->addError('task', 'Failed to move task: ' . $e->getMessage());
        }
    }

    public function updateSchedulePosition($scheduleId, $newDateTime, $newEndDateTime = null)
    {
        try {
            $schedule = TeachingSchedule::find($scheduleId);

            if (!$schedule || $schedule->team_id !== Filament::getTenant()->id) {
                $this->addError('schedule', 'Schedule not found or access denied.');
                return;
            }

            // Parse the new datetime
            $newStartDateTime = \Carbon\Carbon::parse($newDateTime);

            // Calculate new end datetime based on original duration
            $originalDuration = $schedule->start_time->diffInMinutes($schedule->end_time);
            $newEndDateTime = $newStartDateTime->copy()->addMinutes($originalDuration);

            $schedule->update([
                'start_time' => $newStartDateTime,
                'end_time' => $newEndDateTime,
            ]);

            $this->dispatch('schedule-moved', [
                'scheduleId' => $scheduleId,
                'newDateTime' => $newStartDateTime->format('Y-m-d H:i:s'),
                'message' => 'Schedule moved successfully'
            ]);

        } catch (\Exception $e) {
            $this->addError('schedule', 'Failed to move schedule: ' . $e->getMessage());
        }
    }

    public function getTaskDetails($taskId)
    {
        $task = Task::find($taskId);

        if (!$task || $task->team_id !== Filament::getTenant()->id) {
            return null;
        }

        return [
            'id' => $task->id,
            'title' => $task->title,
            'description' => $task->description,
            'start_time' => $task->start_datetime->format('H:i'),
            'end_time' => $task->end_datetime ? $task->end_datetime->format('H:i') : null,
            'priority' => $task->priority,
            'status' => $task->status,
            'location' => $task->location,
            'assigned_user' => $task->assignedUser ? $task->assignedUser->name : null,
        ];
    }

    public function getScheduleDetails($scheduleId)
    {
        $schedule = TeachingSchedule::find($scheduleId);

        if (!$schedule || $schedule->team_id !== Filament::getTenant()->id) {
            return null;
        }

        return [
            'id' => $schedule->id,
            'subject_name' => $schedule->subject->name,
            'notes' => $schedule->notes,
            'start_time' => $schedule->start_time->format('H:i'),
            'end_time' => $schedule->end_time->format('H:i'),
            'teacher_name' => $schedule->teacher->name,
            'classroom_name' => $schedule->classroom->room_name,
            'lesson_name' => $schedule->lesson ? $schedule->lesson->title : null,
        ];
    }

    public function getViewData(): array
    {
        return [
            'tasks' => $this->getTasks(),
            'teachingSchedules' => $this->getTeachingSchedules(),
            'selectedDate' => $this->selectedDate,
            'viewMode' => $this->viewMode,
            'showTasks' => $this->showTasks,
            'showSchedules' => $this->showSchedules,
            'showAssignedToMe' => $this->showAssignedToMe,
            'showMyTasks' => $this->showMyTasks,
        ];
    }
}
