@extends('layouts.frontend')

@section('title', 'My Profile - EduNest')

@push('styles')
    @vite('resources/css/profile.css')
@endpush

@section('content')

<div class="profile-container">
    <div class="max-w-4xl mx-auto px-4">
        <div class="profile-card fade-in role-{{ $role }}">
            <!-- Profile Header -->
            <div class="profile-header">
                @if($profile->profile_image)
                    <img src="{{ Storage::url($profile->profile_image) }}" alt="Profile" class="profile-avatar">
                @else
                    <div class="profile-avatar bg-white bg-opacity-20 flex items-center justify-center">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                @endif
                
                <h1 class="profile-name">{{ $profile->full_name ?: $user->name }}</h1>
                <p class="profile-role">{{ ucfirst(str_replace('_', ' ', $role)) }}</p>
            </div>

            <!-- Progress Bar -->
            @if(!$profile->profile_completed)
            <div class="progress-container">
                <div class="progress-label">
                    <span>Profile Completion</span>
                    <span class="progress-percentage">{{ $profile->getCompletionPercentage() }}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ $profile->getCompletionPercentage() }}%"></div>
                </div>
                @if($profile->getCompletionPercentage() < 100)
                <div class="mt-2 text-center">
                    <a href="{{ route('profile.edit') }}" class="btn btn-primary">Complete Profile</a>
                </div>
                @endif
            </div>
            @endif

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Basic Information -->
                <div class="form-section">
                    <h2 class="section-title">Basic Information</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Full Name</label>
                            <div class="profile-value">{{ $profile->full_name ?: 'Not provided' }}</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Email</label>
                            <div class="profile-value">{{ $user->email ?: 'Not provided' }}</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Phone Number</label>
                            <div class="profile-value">{{ $profile->phone_number ?: 'Not provided' }}</div>
                        </div>

                        @if($profile->date_of_birth)
                        <div class="form-group">
                            <label class="form-label">Date of Birth</label>
                            <div class="profile-value">{{ $profile->date_of_birth->format('F j, Y') }}</div>
                        </div>
                        @endif

                        @if($profile->gender)
                        <div class="form-group">
                            <label class="form-label">Gender</label>
                            <div class="profile-value">{{ ucfirst($profile->gender) }}</div>
                        </div>
                        @endif

                        @if($profile->bio)
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">Bio</label>
                            <div class="profile-value">{{ $profile->bio }}</div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Address Information -->
                @if($profile->address || $profile->city || $profile->state)
                <div class="form-section">
                    <h2 class="section-title">Address Information</h2>
                    <div class="form-grid">
                        @if($profile->address)
                        <div class="form-group" style="grid-column: 1 / -1;">
                            <label class="form-label">Address</label>
                            <div class="profile-value">{{ $profile->address }}</div>
                        </div>
                        @endif

                        @if($profile->city)
                        <div class="form-group">
                            <label class="form-label">City</label>
                            <div class="profile-value">{{ $profile->city }}</div>
                        </div>
                        @endif

                        @if($profile->state)
                        <div class="form-group">
                            <label class="form-label">State/Province</label>
                            <div class="profile-value">{{ $profile->state }}</div>
                        </div>
                        @endif

                        @if($profile->postal_code)
                        <div class="form-group">
                            <label class="form-label">Postal Code</label>
                            <div class="profile-value">{{ $profile->postal_code }}</div>
                        </div>
                        @endif

                        @if($profile->country)
                        <div class="form-group">
                            <label class="form-label">Country</label>
                            <div class="profile-value">{{ $profile->country }}</div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Role-specific Information -->
                @include('profile.partials.' . $role . '-show')

                <!-- Connected Accounts Section -->
                <div class="form-section">
                    <h2 class="section-title">Connected Accounts</h2>
                    <p class="text-sm text-gray-600 mb-4">Connect your social accounts to login with multiple methods</p>

                    @php
                        $availableProviders = App\Models\User::getAvailableSocialProviders();
                        $connectedAccounts = $user->getConnectedSocialAccounts();
                    @endphp

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @foreach($availableProviders as $provider => $config)
                            @php
                                $isConnected = isset($connectedAccounts[$provider]);
                                $accountData = $connectedAccounts[$provider] ?? null;
                            @endphp

                            <div class="border border-gray-200 rounded-lg p-4 flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 rounded-full flex items-center justify-center"
                                         style="background-color: {{ $config['color'] }}20;">
                                        <i class="{{ $config['icon'] }} text-lg" style="color: {{ $config['color'] }};"></i>
                                    </div>
                                    <div>
                                        <h3 class="font-medium text-gray-900">{{ $config['name'] }}</h3>
                                        @if($isConnected)
                                            <p class="text-sm text-gray-600">
                                                Connected as {{ $accountData['name'] ?? $accountData['email'] ?? 'Unknown' }}
                                            </p>
                                            <p class="text-xs text-gray-500">
                                                Connected {{ \Carbon\Carbon::parse($accountData['connected_at'])->diffForHumans() }}
                                            </p>
                                        @else
                                            <p class="text-sm text-gray-600">Not connected</p>
                                        @endif
                                    </div>
                                </div>

                                <div>
                                    @if($isConnected)
                                        <form method="POST" action="{{ route('social.disconnect', $provider) }}" class="inline">
                                            @csrf
                                            <button type="submit"
                                                    class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                                                    onclick="return confirm('Are you sure you want to disconnect your {{ $config['name'] }} account?')">
                                                Disconnect
                                            </button>
                                        </form>
                                    @else
                                        <a href="{{ route('social.connect', $provider) }}"
                                           class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                            Connect
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endforeach

                        @if(empty($availableProviders))
                            <div class="col-span-2 text-center py-8 text-gray-500">
                                <p>No social login providers are currently enabled.</p>
                                <p class="text-sm">Contact your administrator to enable social login options.</p>
                            </div>
                        @endif
                    </div>

                    @if(!empty($connectedAccounts))
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                <div class="text-sm text-blue-700">
                                    <p class="font-medium">You can now login using any of your connected accounts!</p>
                                    <p>Use the social login buttons on the login page to sign in with your connected accounts.</p>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Social Links -->
                @if($profile->facebook_url || $profile->line_id || $profile->instagram_url)
                <div class="form-section">
                    <h2 class="section-title">Social Links</h2>
                    <div class="form-grid">
                        @if($profile->facebook_url)
                        <div class="form-group">
                            <label class="form-label">Facebook</label>
                            <div class="profile-value">
                                <a href="{{ $profile->facebook_url }}" target="_blank" class="text-blue-600 hover:underline">
                                    {{ $profile->facebook_url }}
                                </a>
                            </div>
                        </div>
                        @endif

                        @if($profile->line_id)
                        <div class="form-group">
                            <label class="form-label">LINE ID</label>
                            <div class="profile-value">{{ $profile->line_id }}</div>
                        </div>
                        @endif

                        @if($profile->instagram_url)
                        <div class="form-group">
                            <label class="form-label">Instagram</label>
                            <div class="profile-value">
                                <a href="{{ $profile->instagram_url }}" target="_blank" class="text-blue-600 hover:underline">
                                    {{ $profile->instagram_url }}
                                </a>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="btn-group">
                    <a href="{{ route('profile.edit') }}" class="btn btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Profile
                    </a>
                    
                    <a href="{{ $user->getDashboardRoute() }}" class="btn btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-value {
    padding: 0.75rem 1rem;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    font-size: 1rem;
}

.profile-value:empty::before {
    content: 'Not provided';
    color: #a0aec0;
    font-style: italic;
}
</style>

@endsection
