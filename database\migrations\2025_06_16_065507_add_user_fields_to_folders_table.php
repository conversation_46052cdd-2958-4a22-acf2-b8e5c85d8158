<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('folders', function (Blueprint $table) {
            $table->foreignId('created_by')->nullable()->after('team_id')->constrained('users')->nullOnDelete();
            $table->foreignId('owner_id')->nullable()->after('created_by')->constrained('users')->nullOnDelete();
            $table->boolean('is_personal')->default(false)->after('owner_id');
            $table->boolean('is_deleted_user')->default(false)->after('is_personal');
            $table->index(['created_by', 'owner_id', 'is_personal', 'is_deleted_user']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('folders', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['owner_id']);
            $table->dropIndex(['created_by', 'owner_id', 'is_personal', 'is_deleted_user']);
            $table->dropColumn(['created_by', 'owner_id', 'is_personal', 'is_deleted_user']);
        });
    }
};
