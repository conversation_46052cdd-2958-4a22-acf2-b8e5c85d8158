// Teacher Event Show JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from layout)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Copy link functionality
    const copyLinkBtn = document.getElementById('copyLinkBtn');
    if (copyLinkBtn) {
        copyLinkBtn.addEventListener('click', function() {
            const currentUrl = window.location.href;
            
            // Use modern clipboard API if available
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(currentUrl).then(function() {
                    showCopySuccess();
                }).catch(function(err) {
                    console.error('Failed to copy: ', err);
                    fallbackCopyTextToClipboard(currentUrl);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(currentUrl);
            }
        });
    }

    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        
        // Avoid scrolling to bottom
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess();
            } else {
                showNotification('ไม่สามารถคัดลอกลิงก์ได้', 'error');
            }
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            showNotification('ไม่สามารถคัดลอกลิงก์ได้', 'error');
        }
        
        document.body.removeChild(textArea);
    }

    function showCopySuccess() {
        const originalContent = copyLinkBtn.innerHTML;
        
        copyLinkBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            คัดลอกลิงก์แล้ว
        `;
        
        copyLinkBtn.classList.add('success-bounce');
        
        setTimeout(function() {
            copyLinkBtn.innerHTML = originalContent;
            copyLinkBtn.classList.remove('success-bounce');
        }, 2000);
        
        showNotification('คัดลอกลิงก์เรียบร้อยแล้ว', 'success');
    }

    // Activity button handlers
    const activityButtons = document.querySelectorAll('.btn-test, .btn-exercise');
    activityButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('btn-disabled')) {
                const activityType = this.textContent.trim();
                const studentRow = this.closest('tr');
                const studentName = studentRow.querySelector('td:nth-child(2)').textContent.trim();
                
                showActivityModal(activityType, studentName);
            }
        });
    });

    function showActivityModal(activityType, studentName) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 modal-content">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">เปิดกิจกรรม</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">รายละเอียด</h4>
                        <p><strong>นักเรียน:</strong> ${studentName}</p>
                        <p><strong>กิจกรรม:</strong> ${activityType}</p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">คำแนะนำ</h4>
                        <p class="text-sm text-gray-600">
                            ${activityType === 'ทำแบบทดสอบ' 
                                ? 'นักเรียนจะได้ทำแบบทดสอบเพื่อประเมินความเข้าใจในบทเรียน' 
                                : 'นักเรียนจะได้ฝึกทำแบบฝึกหัดเพื่อเสริมทักษะการเรียนรู้'}
                        </p>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 flex-1 open-activity">
                            เปิดกิจกรรม
                        </button>
                        <button class="close-modal bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400">
                            ยกเลิก
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        const closeButtons = modal.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // Open activity functionality
        const openActivityBtn = modal.querySelector('.open-activity');
        openActivityBtn.addEventListener('click', function() {
            showNotification(`เปิด${activityType}สำหรับ ${studentName}`, 'info');
            document.body.removeChild(modal);
        });
    }

    // Score color coding
    const scoreElements = document.querySelectorAll('td:nth-child(3), td:nth-child(4)');
    scoreElements.forEach(element => {
        const scoreText = element.textContent.trim();
        
        if (scoreText === '-') {
            element.classList.add('score-missing');
        } else if (scoreText.includes('/')) {
            const [score, total] = scoreText.split('/').map(Number);
            const percentage = (score / total) * 100;
            
            if (percentage >= 90) {
                element.classList.add('score-excellent');
            } else if (percentage >= 80) {
                element.classList.add('score-good');
            } else if (percentage >= 70) {
                element.classList.add('score-average');
            } else {
                element.classList.add('score-poor');
            }
        }
    });

    // Table row animations
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach((row, index) => {
        row.style.animationDelay = `${index * 0.1}s`;
        row.classList.add('table-row-enter');
    });

    // Progress bar animation
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        const targetWidth = progressBar.style.width;
        progressBar.style.width = '0%';
        
        setTimeout(() => {
            progressBar.style.width = targetWidth;
        }, 500);
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }

    // Search functionality (if search input exists)
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const studentName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                if (studentName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }

    // Export functionality
    function exportToCSV() {
        const table = document.querySelector('.student-table');
        const rows = table.querySelectorAll('tr');
        let csvContent = '';
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => {
                // Clean up cell content (remove buttons, etc.)
                const textContent = cell.textContent.trim();
                return `"${textContent}"`;
            });
            csvContent += rowData.join(',') + '\n';
        });
        
        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'student_results.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('ส่งออกข้อมูลเรียบร้อยแล้ว', 'success');
    }

    // Add export button if needed
    const headerCard = document.querySelector('.header-card');
    if (headerCard) {
        const exportBtn = document.createElement('button');
        exportBtn.className = 'bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center ml-2';
        exportBtn.innerHTML = `
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            ส่งออกข้อมูล
        `;
        exportBtn.addEventListener('click', exportToCSV);
        
        const buttonContainer = headerCard.querySelector('.flex.flex-col.md\\:flex-row');
        if (buttonContainer) {
            buttonContainer.appendChild(exportBtn);
        }
    }

    // Add fade-in animation to main elements
    const headerCardElement = document.querySelector('.header-card');
    const tableContainer = document.querySelector('.table-container');
    
    if (headerCardElement) {
        headerCardElement.classList.add('fade-in');
    }
    
    if (tableContainer) {
        tableContainer.classList.add('fade-in');
        tableContainer.style.animationDelay = '0.2s';
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + C to copy link
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && !e.target.matches('input, textarea')) {
            e.preventDefault();
            if (copyLinkBtn) {
                copyLinkBtn.click();
            }
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const modals = document.querySelectorAll('.fixed.inset-0');
            modals.forEach(modal => {
                if (modal.parentElement) {
                    modal.remove();
                }
            });
        }
    });
});
