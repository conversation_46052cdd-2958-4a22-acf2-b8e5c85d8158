<?php

namespace App\Filament\Widgets;

use App\Models\Shop\Customer;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Widgets\ChartWidget;

class CustomersChart extends ChartWidget
{
    protected static ?string $heading = 'Customers by Month';

    protected static ?int $sort = 2;

    protected function getType(): string
    {
        return 'line';
    }

    protected function getData(): array
    {
        $currentTenant = Filament::getTenant();

        // Get customer data for the last 12 months, filtered by current tenant
        $data = [];
        $labels = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $labels[] = $date->format('M');

            $query = Customer::whereYear('created_at', $date->year)
                           ->whereMonth('created_at', $date->month);

            if ($currentTenant) {
                $query->where('team_id', $currentTenant->id);
            }

            $data[] = $query->count();
        }

        $teamName = $currentTenant ? $currentTenant->name : 'All Teams';

        return [
            'datasets' => [
                [
                    'label' => "Customers - {$teamName}",
                    'data' => $data,
                    'fill' => 'start',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                ],
            ],
            'labels' => $labels,
        ];
    }

    public function getHeading(): ?string
    {
        $currentTenant = Filament::getTenant();
        $teamName = $currentTenant ? $currentTenant->name : 'All Teams';

        return "Customers by Month - {$teamName}";
    }
}
