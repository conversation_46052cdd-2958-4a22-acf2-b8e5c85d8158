<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class EnsureProfileCompleted
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip for guests
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Skip for profile routes to avoid infinite redirect
        if ($request->routeIs('profile.*')) {
            return $next($request);
        }

        // Skip for logout and API routes
        if ($request->routeIs('logout') || $request->is('api/*')) {
            return $next($request);
        }

        // Check if user needs profile completion
        if ($user->needsProfileCompletion()) {
            return redirect()->route('profile.edit')
                ->with('info', 'Please complete your profile to continue.');
        }

        return $next($request);
    }
}
