<?php

namespace App\Filament\Resources\LiveVideoResource\Pages;

use App\Filament\Resources\LiveVideoResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Facades\Filament;

class CreateLiveVideo extends CreateRecord
{
    protected static string $resource = LiveVideoResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-assign team_id and user_id
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $data['team_id'] = Filament::getTenant()->id;
        }

        $data['user_id'] = auth()->id();

        return $data;
    }
}
