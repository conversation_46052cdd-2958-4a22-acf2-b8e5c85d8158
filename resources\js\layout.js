// Global Layout JavaScript - Header and Footer functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all layout components
    initializeHeader();
    initializeMobileMenu();
    initializeScrollToTop();
    initializePageTransitions();
    initializeActiveNavigation();
    initializeLoadingStates();
    initializeAnchorLinks();
    
    // Header scroll effect
    function initializeHeader() {
        const header = document.querySelector('.header');
        if (!header) return;
        
        let lastScrollY = window.scrollY;
        let ticking = false;
        
        function updateHeader() {
            const scrollY = window.scrollY;
            
            if (scrollY > 50) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollY > lastScrollY && scrollY > 100) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollY = scrollY;
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateHeader);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick, { passive: true });
    }
    
    // Mobile menu functionality
    function initializeMobileMenu() {
        const menuToggle = document.getElementById('menu-toggle');
        const mobileMenu = document.getElementById('mobile-menu');
        const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
        const closeMenu = document.getElementById('close-menu');
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
        
        if (!menuToggle || !mobileMenu) return;
        
        // Toggle mobile menu
        menuToggle.addEventListener('click', function() {
            toggleMobileMenu();
        });
        
        // Close menu when clicking close button
        if (closeMenu) {
            closeMenu.addEventListener('click', function() {
                closeMobileMenu();
            });
        }
        
        // Close menu when clicking overlay
        if (mobileMenuOverlay) {
            mobileMenuOverlay.addEventListener('click', function() {
                closeMobileMenu();
            });
        }
        
        // Close menu when clicking nav links
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                closeMobileMenu();
            });
        });
        
        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
        
        function toggleMobileMenu() {
            const isActive = mobileMenu.classList.contains('active');
            
            if (isActive) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }
        
        function openMobileMenu() {
            mobileMenu.classList.add('active');
            if (mobileMenuOverlay) {
                mobileMenuOverlay.classList.add('active');
            }
            menuToggle.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Focus trap
            const firstFocusable = mobileMenu.querySelector('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
                firstFocusable.focus();
            }
        }
        
        function closeMobileMenu() {
            mobileMenu.classList.remove('active');
            if (mobileMenuOverlay) {
                mobileMenuOverlay.classList.remove('active');
            }
            menuToggle.classList.remove('active');
            document.body.style.overflow = '';
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768 && mobileMenu.classList.contains('active')) {
                closeMobileMenu();
            }
        });
    }
    
    // Scroll to top functionality
    function initializeScrollToTop() {
        const scrollToTopBtn = document.querySelector('.scroll-to-top');
        if (!scrollToTopBtn) {
            // Create scroll to top button if it doesn't exist
            createScrollToTopButton();
        }
        
        const btn = document.querySelector('.scroll-to-top');
        if (!btn) return;
        
        let ticking = false;
        
        function updateScrollToTopVisibility() {
            const scrollY = window.scrollY;
            
            if (scrollY > 300) {
                btn.classList.add('visible');
            } else {
                btn.classList.remove('visible');
            }
            
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollToTopVisibility);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick, { passive: true });
        
        btn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    function createScrollToTopButton() {
        const btn = document.createElement('button');
        btn.className = 'scroll-to-top';
        btn.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg>';
        btn.setAttribute('aria-label', 'Scroll to top');
        document.body.appendChild(btn);
    }
    
    // Page transitions
    function initializePageTransitions() {
        // Only handle links that are NOT anchor links
        const links = document.querySelectorAll('a[href^="/"], a[href^="' + window.location.origin + '"]');

        links.forEach(link => {
            // Skip if this link has already been processed
            if (link.hasAttribute('data-layout-processed')) {
                return;
            }
            link.setAttribute('data-layout-processed', 'true');

            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');

                // Skip if it's a direct anchor link (starts with #)
                if (href.startsWith('#')) {
                    return; // Let direct anchor links work normally without loading overlay
                }

                // Skip if it's an external link or has special attributes
                if (this.hasAttribute('download') ||
                    this.hasAttribute('target') ||
                    this.getAttribute('rel') === 'external') {
                    return;
                }

                // For links with anchors, check if it's same page navigation
                if (href.includes('#')) {
                    // Extract the URL part before the hash
                    const hashIndex = href.indexOf('#');
                    const urlPart = href.substring(0, hashIndex);

                    // Get current page URL without hash
                    const currentPageUrl = window.location.origin + window.location.pathname;

                    // Check if it's the same page
                    const isSamePage =
                        urlPart === currentPageUrl ||
                        urlPart === window.location.pathname ||
                        (urlPart === window.location.origin && window.location.pathname === '/') ||
                        (urlPart === window.location.origin + '/' && window.location.pathname === '/');

                    if (isSamePage) {
                        // It's same page anchor navigation, don't show loading
                        return;
                    }
                    // If it's a different page with anchor, allow normal navigation with loading
                }

                // Skip if it's the current page (without anchor)
                if (href === window.location.pathname || href === window.location.origin + window.location.pathname) {
                    e.preventDefault();
                    return;
                }

                // Add loading state for actual page navigation
                showLoadingOverlay();
            }, { once: false, passive: false });
        });
    }
    
    // Active navigation highlighting
    function initializeActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // Remove existing active classes
            link.classList.remove('active');
            
            // Add active class if href matches current path
            if (href === currentPath || 
                (href !== '/' && currentPath.startsWith(href))) {
                link.classList.add('active');
            }
        });
    }
    
    // Loading states
    function initializeLoadingStates() {
        // Create loading overlay if it doesn't exist
        if (!document.querySelector('.loading-overlay')) {
            createLoadingOverlay();
        }

        // Hide loading overlay when page is fully loaded
        window.addEventListener('load', function() {
            hideLoadingOverlay();
        });
    }

    // Anchor links handling
    function initializeAnchorLinks() {
        // Handle only direct anchor links (#section) for same-page navigation
        const anchorLinks = document.querySelectorAll('a[href^="#"]');

        anchorLinks.forEach(link => {
            // Skip if this link has already been processed
            if (link.hasAttribute('data-anchor-processed')) {
                return;
            }
            link.setAttribute('data-anchor-processed', 'true');

            link.addEventListener('click', function(e) {
                const href = this.getAttribute('href');

                // Skip empty anchors
                if (href === '#' || href === '#!') {
                    return;
                }

                e.preventDefault();
                e.stopPropagation();

                const targetId = href.substring(1);
                const success = window.LayoutUtils.smoothScrollTo(targetId);

                // If target not found, don't prevent default behavior
                if (!success) {
                    window.location.hash = href;
                }
            }, { once: false, passive: false });
        });
    }
    
    function createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(overlay);
    }
    
    function showLoadingOverlay() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }
    
    function hideLoadingOverlay() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    // Utility functions for other scripts to use
    window.LayoutUtils = {
        showLoading: showLoadingOverlay,
        hideLoading: hideLoadingOverlay,
        closeMobileMenu: function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const menuToggle = document.getElementById('menu-toggle');

            if (mobileMenu) mobileMenu.classList.remove('active');
            if (mobileMenuOverlay) mobileMenuOverlay.classList.remove('active');
            if (menuToggle) menuToggle.classList.remove('active');
            document.body.style.overflow = '';
        },

        smoothScrollTo: function(targetId) {
            // Hide loading overlay
            hideLoadingOverlay();

            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // Close mobile menu
                this.closeMobileMenu();

                return true;
            }
            return false;
        },
        
        showNotification: function(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 60;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
            `;
            
            // Set background color based on type
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };
            
            notification.style.backgroundColor = colors[type] || colors.info;
            notification.style.color = 'white';
            
            notification.innerHTML = `
                <div class="flex items-center">
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, duration);
        },
        
        animateOnScroll: function() {
            const elements = document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animationPlayState = 'running';
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });
            
            elements.forEach(el => {
                el.style.animationPlayState = 'paused';
                observer.observe(el);
            });
        }
    };
    
    // Initialize scroll animations
    if ('IntersectionObserver' in window) {
        window.LayoutUtils.animateOnScroll();
    }
    
    // Handle form submissions with loading states
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                showLoadingOverlay();
                
                // Hide loading after 10 seconds as fallback
                setTimeout(() => {
                    hideLoadingOverlay();
                }, 10000);
            }
        });
    });
    
    // Keyboard navigation improvements
    document.addEventListener('keydown', function(e) {
        // Skip to main content with Alt+M
        if (e.altKey && e.key === 'm') {
            e.preventDefault();
            const main = document.querySelector('main');
            if (main) {
                main.focus();
                main.scrollIntoView({ behavior: 'smooth' });
            }
        }
        
        // Focus search with Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"], input[placeholder*="ค้นหา"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });
    
    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 3000) {
                    console.warn('Page load time is slow:', perfData.loadEventEnd + 'ms');
                }
            }, 0);
        });
    }
});
