<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'EduNest - Your Learning Sanctuary')</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/css/layout.css'])

    @stack('styles')
</head>
<body class="font-prompt bg-gray-50 text-slate-700 overflow-x-hidden">

    <!-- Header -->
    <header class="header sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{{ route('homepage') }}" class="flex items-center">
                        <svg class="w-10 h-10 text-cyan-500 pulse-glow" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="draw-animation" d="M20 5L5 12.5L20 20L35 12.5L20 5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="2"></path>
                            <path class="draw-animation" style="animation-delay: 0.5s;" d="M5 27.5L20 35L35 27.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path class="draw-animation" style="animation-delay: 1s;" d="M5 20L20 27.5L35 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <span class="logo ml-3">Edu<span class="gradient-text">Nest</span></span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="desktop-nav hidden md:flex space-x-2">
                    <a href="{{ route('homepage') }}#home" class="nav-link {{ request()->routeIs('homepage') ? 'active' : '' }}">หน้าแรก</a>
                    <a href="{{ route('homepage') }}#features" class="nav-link">คุณสมบัติ</a>
                    <a href="{{ route('downloads') }}" class="nav-link {{ request()->routeIs('downloads') ? 'active' : '' }}">ดาวน์โหลด</a>
                    <a href="{{ route('homepage') }}#pricing" class="nav-link">แพ็กเกจ</a>
                    <a href="{{ route('homepage') }}#about" class="nav-link">เกี่ยวกับเรา</a>
                    <a href="{{ route('homepage') }}#contact" class="nav-link">ติดต่อ</a>
                </nav>

                <!-- CTA Buttons / User Menu -->
                <div class="hidden md:flex items-center space-x-4">
                    @auth
                        <!-- User Avatar Dropdown -->
                        <div class="relative user-dropdown" x-data="{ open: false }">
                            <button @click="open = !open" @click.away="open = false"
                                    @keydown.escape="open = false"
                                    :aria-expanded="open"
                                    aria-haspopup="true"
                                    class="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100 transition-colors user-avatar"
                                    id="user-menu-button">
                                @if(auth()->user()->profile && auth()->user()->profile->profile_image)
                                    <img src="{{ Storage::url(auth()->user()->profile->profile_image) }}"
                                         alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                @elseif(auth()->user()->avatar)
                                    <img src="{{ auth()->user()->avatar }}"
                                         alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                @else
                                    <div class="w-8 h-8 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white text-sm font-medium">
                                            {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                                        </span>
                                    </div>
                                @endif
                                <span class="text-gray-700 font-medium">{{ auth()->user()->name }}</span>
                                <svg class="w-4 h-4 text-gray-500 transition-transform" :class="{ 'rotate-180': open }"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div x-show="open"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 @keydown.escape="open = false"
                                 role="menu"
                                 aria-orientation="vertical"
                                 aria-labelledby="user-menu-button"
                                 class="absolute right-0 mt-2 user-dropdown-menu bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
                                 style="display: none;"
                                 x-cloak>

                                <!-- User Info -->
                                <div class="px-4 py-3 border-b border-gray-100">
                                    <p class="text-sm font-medium text-gray-900">{{ auth()->user()->name }}</p>
                                    <p class="text-sm text-gray-500">{{ auth()->user()->email }}</p>
                                    <p class="text-xs text-gray-400 mt-1">
                                        {{ ucfirst(str_replace('_', ' ', auth()->user()->getPrimaryRole())) }}
                                    </p>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-1">
                                    <a href="{{ route('profile.show') }}"
                                       role="menuitem"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors user-dropdown-item">
                                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        View Profile
                                    </a>

                                    <a href="{{ route('profile.edit') }}"
                                       role="menuitem"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors user-dropdown-item">
                                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit Profile
                                    </a>

                                    <a href="{{ auth()->user()->getDashboardRoute() }}"
                                       role="menuitem"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors user-dropdown-item">
                                        <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                        Go to Dashboard
                                    </a>

                                    @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('school'))
                                        <a href="/backend"
                                           role="menuitem"
                                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors user-dropdown-item">
                                            <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            Admin Panel
                                        </a>
                                    @endif
                                </div>

                                <!-- Logout -->
                                <div class="border-t border-gray-100 py-1">
                                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                                        @csrf
                                        <button type="submit"
                                                role="menuitem"
                                                class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors user-dropdown-item">
                                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                            </svg>
                                            Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Guest Buttons -->
                        <a href="{{ route('login') }}" class="nav-link">เข้าสู่ระบบ</a>
                        <a href="{{ route('register') }}" class="gradient-bg px-5 py-2 rounded-full text-white font-medium hover-lift">สมัครสมาชิก</a>
                    @endauth
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="menu-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="mobile-menu">
            <div class="flex justify-end mb-8">
                <button id="close-menu" class="text-gray-700 hover:text-cyan-500 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <nav class="flex flex-col">
                <a href="{{ route('homepage') }}#home" class="mobile-nav-link {{ request()->routeIs('homepage') ? 'active' : '' }}">หน้าแรก</a>
                <a href="{{ route('homepage') }}#features" class="mobile-nav-link">คุณสมบัติ</a>
                <a href="{{ route('downloads') }}" class="mobile-nav-link {{ request()->routeIs('downloads') ? 'active' : '' }}">ดาวน์โหลด</a>
                <a href="{{ route('homepage') }}#pricing" class="mobile-nav-link">แพ็กเกจ</a>
                <a href="{{ route('homepage') }}#about" class="mobile-nav-link">เกี่ยวกับเรา</a>
                <a href="{{ route('homepage') }}#contact" class="mobile-nav-link">ติดต่อ</a>
                <div class="pt-4 mt-4 border-t border-gray-200">
                    @auth
                        <!-- User Info -->
                        <div class="px-6 py-3 bg-gray-50 rounded-lg mx-6 mb-4">
                            <div class="flex items-center space-x-3">
                                @if(auth()->user()->profile && auth()->user()->profile->profile_image)
                                    <img src="{{ Storage::url(auth()->user()->profile->profile_image) }}"
                                         alt="Profile" class="w-10 h-10 rounded-full object-cover">
                                @elseif(auth()->user()->avatar)
                                    <img src="{{ auth()->user()->avatar }}"
                                         alt="Profile" class="w-10 h-10 rounded-full object-cover">
                                @else
                                    <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-medium">
                                            {{ strtoupper(substr(auth()->user()->name, 0, 1)) }}
                                        </span>
                                    </div>
                                @endif
                                <div>
                                    <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                                    <p class="text-sm text-gray-500">{{ ucfirst(str_replace('_', ' ', auth()->user()->getPrimaryRole())) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu Items -->
                        <a href="{{ route('profile.show') }}" class="mobile-nav-link">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            View Profile
                        </a>
                        <a href="{{ route('profile.edit') }}" class="mobile-nav-link">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Profile
                        </a>
                        <a href="{{ auth()->user()->getDashboardRoute() }}" class="mobile-nav-link">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Dashboard
                        </a>
                        @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('school'))
                            <a href="/backend" class="mobile-nav-link">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Admin Panel
                            </a>
                        @endif
                        <form method="POST" action="{{ route('logout') }}" class="w-full">
                            @csrf
                            <button type="submit" class="mobile-nav-link text-red-600 w-full text-left">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Logout
                            </button>
                        </form>
                    @else
                        <!-- Guest Menu Items -->
                        <a href="{{ route('login') }}" class="mobile-nav-link">เข้าสู่ระบบ</a>
                        <a href="{{ route('register') }}" class="block w-full py-2 text-center gradient-bg rounded-full text-white font-medium mx-6 my-3">สมัครสมาชิก</a>
                    @endauth
                </div>
            </nav>
        </div>

        <!-- Mobile Menu Overlay -->
        <div id="mobile-menu-overlay" class="mobile-menu-overlay"></div>
    </header>

    @yield('content')

    <!-- Footer -->
    <footer class="footer py-12">
        <div class="footer-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="footer-section">
                    <div class="flex items-center mb-4">
                        <svg class="w-8 h-8 text-cyan-400" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 5L5 12.5L20 20L35 12.5L20 5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="2"></path>
                            <path d="M5 27.5L20 35L35 27.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M5 20L20 27.5L35 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <span class="ml-2 text-xl font-bold">Edu<span class="text-cyan-400">Nest</span></span>
                    </div>
                    <p class="text-gray-500 mb-4">
                        แพลตฟอร์มการเรียนรู้ออนไลน์ที่ครบครันและทันสมัย เพื่อการศึกษาที่ดีกว่าในอนาคต
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                        </a>
                        <a href="#" class="social-link">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-section">
                    <h3>ลิงก์ด่วน</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('homepage') }}#home" class="footer-link">หน้าแรก</a></li>
                        <li><a href="{{ route('homepage') }}#features" class="footer-link">คุณสมบัติ</a></li>
                        <li><a href="{{ route('downloads') }}" class="footer-link">ดาวน์โหลด</a></li>
                        <li><a href="{{ route('homepage') }}#pricing" class="footer-link">แพ็กเกจ</a></li>
                        <li><a href="{{ route('homepage') }}#contact" class="footer-link">ติดต่อเรา</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div class="footer-section">
                    <h3>การสนับสนุน</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="footer-link">ศูนย์ช่วยเหลือ</a></li>
                        <li><a href="#" class="footer-link">คำถามที่พบบ่อย</a></li>
                        <li><a href="#" class="footer-link">ติดต่อฝ่ายสนับสนุน</a></li>
                        <li><a href="#" class="footer-link">รายงานปัญหา</a></li>
                        <li><a href="#" class="footer-link">คู่มือการใช้งาน</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div class="footer-section">
                    <h3>ข้อมูลทางกฎหมาย</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="footer-link">นโยบายความเป็นส่วนตัว</a></li>
                        <li><a href="#" class="footer-link">ข้อกำหนดการใช้งาน</a></li>
                        <li><a href="#" class="footer-link">นโยบายคุกกี้</a></li>
                        <li><a href="#" class="footer-link">ข้อกำหนดการชำระเงิน</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom border-t mt-8 py-2 text-center">
                <p class="text-gray-500">
                    © {{ date('Y') }} EduNest. สงวนลิขสิทธิ์ทั้งหมด. | พัฒนาด้วย ❤️ เพื่อการศึกษาไทย
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    @vite(['resources/js/app.js', 'resources/js/layout.js', 'resources/js/animated-svg.js'])

    @stack('scripts')
</body>
</html>
