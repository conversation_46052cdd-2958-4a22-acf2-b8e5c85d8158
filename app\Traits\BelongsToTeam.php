<?php

namespace App\Traits;

use App\Models\Team;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToTeam
{
    /**
     * Boot the trait.
     */
    protected static function bootBelongsToTeam(): void
    {
        static::addGlobalScope('team', function (Builder $builder) {
            $user = auth()->user();
            $model = $builder->getModel();
            $table = $model->getTable();

            // Always filter by current tenant if available
            if (Filament::hasTenancy() && Filament::getTenant()) {
                $builder->where("{$table}.team_id", Filament::getTenant()->id);
                return;
            }

            // If no tenant context, filter by user's team (for non-super admins)
            if ($user && $user->team_id !== null) {
                $builder->where("{$table}.team_id", $user->team_id);
            }

            // Super admins without tenant context see nothing (force them to select a tenant)
            if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
                $builder->whereRaw('1 = 0'); // Return no results
            }
        });

        static::creating(function (Model $model) {
            // Skip auto-assignment if team_id is already set
            if ($model->team_id) {
                return;
            }

            $user = auth()->user();

            // Don't auto-assign team for super admin created records unless explicitly set
            if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
                return; // Let super admins explicitly set team_id
            }

            // Auto-assign team based on current tenant or user's team
            if (Filament::hasTenancy() && Filament::getTenant()) {
                $model->team_id = Filament::getTenant()->id;
            } elseif ($user && $user->team_id) {
                // Fallback to user's team if no tenant context
                $model->team_id = $user->team_id;
            }
        });
    }

    /**
     * Get the team that owns the model.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Scope a query to only include models for a specific team.
     */
    public function scopeForTeam(Builder $query, Team $team): Builder
    {
        return $query->where('team_id', $team->id);
    }

    /**
     * Scope a query to only include models for the current team.
     */
    public function scopeForCurrentTeam(Builder $query): Builder
    {
        if (Filament::hasTenancy() && Filament::getTenant()) {
            return $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }
}
