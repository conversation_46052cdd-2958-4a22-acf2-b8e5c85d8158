<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->foreignId('subject_id')->nullable()->constrained('subjects')->nullOnDelete();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['online', 'onsite', 'hybrid'])->default('online');
            $table->decimal('price', 10, 2)->nullable(); // Course price
            $table->string('currency', 3)->default('USD'); // Currency code
            $table->integer('duration_hours')->nullable(); // Total course duration
            $table->enum('difficulty_level', ['beginner', 'intermediate', 'advanced'])->default('beginner');
            $table->integer('max_students')->nullable(); // Maximum number of students
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->json('schedule')->nullable(); // JSON for flexible scheduling
            $table->string('instructor')->nullable(); // Instructor name
            $table->text('requirements')->nullable(); // Course requirements/prerequisites
            $table->text('objectives')->nullable(); // Learning objectives
            $table->string('certificate_template')->nullable(); // Certificate template path
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['team_id', 'is_active']);
            $table->index(['team_id', 'type']);
            $table->index(['subject_id', 'is_active']);
            $table->index(['start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
