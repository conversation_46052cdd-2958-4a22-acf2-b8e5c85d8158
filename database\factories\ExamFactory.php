<?php

namespace Database\Factories;

use App\Models\Exam\Exam;
use Illuminate\Database\Eloquent\Factories\Factory;

class ExamFactory extends Factory
{
    protected $model = Exam::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(['exercise', 'exam']),
            'total_questions' => $this->faker->numberBetween(5, 20),
            'total_score' => $this->faker->numberBetween(50, 100),
            'time_limit' => $this->faker->optional(0.7)->numberBetween(15, 120),
            'randomize_questions' => $this->faker->boolean(80),
            'randomize_choices' => $this->faker->boolean(80),
            'show_results_immediately' => $this->faker->boolean(60),
            'allow_multiple_attempts' => $this->faker->boolean(40),
            'available_from' => $this->faker->optional(0.3)->dateTimeBetween('-1 week', '+1 week'),
            'available_until' => $this->faker->optional(0.3)->dateTimeBetween('+1 week', '+1 month'),
            'is_active' => true,
        ];
    }

    public function exercise(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'exercise',
            'show_results_immediately' => true,
            'allow_multiple_attempts' => true,
        ]);
    }

    public function exam(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'exam',
            'show_results_immediately' => false,
            'allow_multiple_attempts' => false,
        ]);
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
