<?php

namespace App\Models\Exam;

use App\Models\Team;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentAnswer extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'attempt_id',
        'question_id',
        'selected_choice_id',
        'answer_text',
        'is_correct',
        'score',
        'max_score',
        'teacher_comment',
        'answered_at',
    ];

    protected $casts = [
        'is_correct' => 'boolean',
        'score' => 'float',
        'max_score' => 'float',
        'answered_at' => 'datetime',
    ];

    // Relationships
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function attempt(): BelongsTo
    {
        return $this->belongsTo(StudentExamAttempt::class, 'attempt_id');
    }

    public function question(): BelongsTo
    {
        return $this->belongsTo(Question::class);
    }

    public function selectedChoice(): BelongsTo
    {
        return $this->belongsTo(Choice::class, 'selected_choice_id');
    }

    // Scopes
    public function scopeCorrect($query)
    {
        return $query->where('is_correct', true);
    }

    public function scopeIncorrect($query)
    {
        return $query->where('is_correct', false);
    }

    public function scopeNeedsGrading($query)
    {
        return $query->whereNull('is_correct')
            ->whereHas('question', function ($q) {
                $q->whereIn('question_type', ['short_answer', 'essay']);
            });
    }

    // Helper methods
    public function isMultipleChoice(): bool
    {
        return $this->question->question_type === 'multiple_choice';
    }

    public function isWrittenAnswer(): bool
    {
        return in_array($this->question->question_type, ['short_answer', 'essay']);
    }

    public function checkCorrectness(): void
    {
        if ($this->isMultipleChoice() && $this->selected_choice_id) {
            $this->is_correct = $this->selectedChoice->is_correct;
            $this->score = $this->is_correct ? $this->max_score : 0;
            $this->save();
        }
    }

    public function getDisplayAnswer(): string
    {
        if ($this->isMultipleChoice() && $this->selectedChoice) {
            return $this->selectedChoice->choice_text;
        }

        return $this->answer_text ?? '';
    }
}
