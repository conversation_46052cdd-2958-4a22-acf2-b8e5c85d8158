<?php

namespace Database\Factories;

use App\Models\Lesson;
use Illuminate\Database\Eloquent\Factories\Factory;

class LessonFactory extends Factory
{
    protected $model = Lesson::class;

    public function definition(): array
    {
        $chapterNumber = $this->faker->numberBetween(1, 20);
        
        return [
            'title' => $this->faker->sentence(4),
            'chapter' => "Chapter {$chapterNumber}",
            'content' => $this->faker->paragraphs(3, true),
            'objectives' => $this->faker->paragraph(),
            'summary' => $this->faker->paragraph(),
            'duration_minutes' => $this->faker->numberBetween(30, 120),
            'sort_order' => $chapterNumber,
            'is_published' => true,
        ];
    }
}
