<!DOCTYPE html>
<!-- saved from url=(0140)https://bd7ysnnrsyqv165k.canva-hosted-embed.com/codelet/AAEAEGJkN3lzbm5yc3lxdjE2NWsAAAAAAZdOepeuFa89-5EGhSU03htufTcyRMzRRXPd6ipRdsqLpOR5g4U/ -->
<html lang="th"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แสดงผลกิจกรรมที่มอบหมายแล้ว</title>
    <link href="./css2" rel="stylesheet">
    <script src="./saved_resource"></script>
    <style>
        body {
            font-family: 'Sarabun', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }
        .header-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .info-label {
            color: #666;
            font-weight: 500;
        }
        .info-value {
            font-weight: 600;
            color: #333;
        }
        .copy-link-btn {
            background-color: #7c3aed;
            transition: all 0.3s ease;
        }
        .copy-link-btn:hover {
            background-color: #6d28d9;
        }
        .table-container {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }
        th {
            background-color: #f3f4f6;
            font-weight: 600;
            text-align: left;
            color: #4b5563;
        }
        tr:nth-child(even) {
            background-color: #f9fafb;
        }
        tr:nth-child(odd) {
            background-color: white;
        }
        tr:hover {
            background-color: #f0f9ff;
        }
        .btn-test {
            background-color: #8b5cf6;
            transition: all 0.2s;
        }
        .btn-test:hover {
            background-color: #7c3aed;
        }
        .btn-exercise {
            background-color: #10b981;
            transition: all 0.2s;
        }
        .btn-exercise:hover {
            background-color: #059669;
        }
        .btn-disabled {
            background-color: #9ca3af;
            cursor: default;
        }
        .btn-disabled:hover {
            background-color: #9ca3af;
        }
        .progress-container {
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #8b5cf6;
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-6{margin-bottom:1.5rem}.ml-1{margin-left:0.25rem}.mr-2{margin-right:0.5rem}.mr-4{margin-right:1rem}.mt-1{margin-top:0.25rem}.mt-4{margin-top:1rem}.flex{display:flex}.grid{display:grid}.h-4{height:1rem}.h-5{height:1.25rem}.w-4{width:1rem}.w-5{width:1.25rem}.w-full{width:100%}.max-w-7xl{max-width:80rem}.list-disc{list-style-type:disc}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-2{gap:0.5rem}.gap-4{gap:1rem}.overflow-x-auto{overflow-x:auto}.rounded-lg{border-radius:0.5rem}.rounded-md{border-radius:0.375rem}.border-t{border-top-width:1px}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.pl-5{padding-left:1.25rem}.pt-4{padding-top:1rem}.text-left{text-align:left}.text-center{text-align:center}.text-right{text-align:right}.text-2xl{font-size:1.5rem;line-height:2rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.font-bold{font-weight:700}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}@media (min-width: 768px){.md\:mb-0{margin-bottom:0px}.md\:w-1\/3{width:33.333333%}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:flex-row{flex-direction:row}.md\:items-center{align-items:center}.md\:p-6{padding:1.5rem}}@media (min-width: 1024px){.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.lg\:p-8{padding:2rem}}</style></head>
<body class="p-4 md:p-6 lg:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- ส่วนที่ 1: แถบหัวหน้ากิจกรรม (Activity Header) -->
        <div class="header-card p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2 md:mb-0">กิจกรรมที่มอบหมายแล้ว</h1>
                <button id="copyLinkBtn" class="copy-link-btn text-white px-4 py-2 rounded-lg flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    คัดลอกลิงก์หน้านี้
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="mb-2">
                    <p class="info-label">ระดับชั้น/ห้อง</p>
                    <p class="info-value">ประถมศึกษาปีที่ 5 ห้อง 1</p>
                </div>
                <div class="mb-2">
                    <p class="info-label">วิชา</p>
                    <p class="info-value">คณิตศาสตร์</p>
                </div>
                <div class="mb-2">
                    <p class="info-label">ชื่อหนังสือและบทเรียน</p>
                    <p class="info-value">หนังสือเรียนคณิตศาสตร์ ป.5 บทที่ 3 เศษส่วน</p>
                </div>
                <div class="mb-2">
                    <p class="info-label">วันที่สอน</p>
                    <p class="info-value">4 มิ.ย. 2566</p>
                </div>
                <div class="mb-2">
                    <p class="info-label">วันกำหนดส่ง</p>
                    <p class="info-value">6 มิ.ย. 2566</p>
                </div>
                <div class="mb-2">
                    <p class="info-label">รายการกิจกรรมที่มอบหมาย</p>
                    <ul class="info-value list-disc pl-5">
                        <li>แบบฝึกหัดจาก EduNest</li>
                        <li>เกมฝึกทักษะ</li>
                    </ul>
                </div>
            </div>

            <div class="mt-4 pt-4 border-t border-gray-200">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <div class="flex items-center mb-3 md:mb-0">
                        <div class="mr-4">
                            <p class="info-label">จำนวนนักเรียนในห้อง</p>
                            <p class="info-value">38 คน</p>
                        </div>
                        <div>
                            <p class="info-label">ส่งแล้ว</p>
                            <p class="info-value">22 คน</p>
                        </div>
                    </div>
                    <div class="w-full md:w-1/3">
                        <p class="info-label mb-1">ความคืบหน้า</p>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 58%;"></div>
                        </div>
                        <p class="text-right text-sm text-gray-600 mt-1">58%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- ส่วนที่ 2: ตารางรายชื่อนักเรียน (Student Table View) -->
        <div class="table-container bg-white">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left">ลำดับ</th>
                            <th class="px-4 py-3 text-left">ชื่อ-นามสกุล</th>
                            <th class="px-4 py-3 text-center">คะแนนแบบทดสอบ</th>
                            <th class="px-4 py-3 text-center">คะแนนแบบฝึกหัด</th>
                            <th class="px-4 py-3 text-center">กิจกรรม</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="px-4 py-3">1</td>
                            <td class="px-4 py-3">ด.ช. ธีรภัทร พรมมา</td>
                            <td class="px-4 py-3 text-center">8/10</td>
                            <td class="px-4 py-3 text-center">9/10</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm flex items-center">
                                        แบบฝึกหัด 
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">2</td>
                            <td class="px-4 py-3">ด.ญ. ขวัญฤดี ทองสุข</td>
                            <td class="px-4 py-3 text-center">6/10</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-exercise text-white px-3 py-1 rounded-md text-sm">แบบฝึกหัด</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">3</td>
                            <td class="px-4 py-3">ด.ช. ภูมิพัฒน์ แสงทอง</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3 text-center">7/10</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-test text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm flex items-center">
                                        แบบฝึกหัด 
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">4</td>
                            <td class="px-4 py-3">ด.ญ. กัญญาภัค วงศ์สุวรรณ</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-test text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-exercise text-white px-3 py-1 rounded-md text-sm">แบบฝึกหัด</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">5</td>
                            <td class="px-4 py-3">ด.ช. ณัฐวุฒิ ศรีสุข</td>
                            <td class="px-4 py-3 text-center">9/10</td>
                            <td class="px-4 py-3 text-center">8/10</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm flex items-center">
                                        แบบฝึกหัด 
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">6</td>
                            <td class="px-4 py-3">ด.ญ. พิมพ์มาดา จันทร์เพ็ญ</td>
                            <td class="px-4 py-3 text-center">7/10</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-exercise text-white px-3 py-1 rounded-md text-sm">แบบฝึกหัด</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">7</td>
                            <td class="px-4 py-3">ด.ช. ธนกฤต บุญมี</td>
                            <td class="px-4 py-3 text-center">-</td>
                            <td class="px-4 py-3 text-center">6/10</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-test text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm flex items-center">
                                        แบบฝึกหัด 
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-4 py-3">8</td>
                            <td class="px-4 py-3">ด.ญ. สุภาวดี ใจดี</td>
                            <td class="px-4 py-3 text-center">10/10</td>
                            <td class="px-4 py-3 text-center">10/10</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-wrap justify-center gap-2">
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm">ทำแบบทดสอบ</button>
                                    <button class="btn-disabled text-white px-3 py-1 rounded-md text-sm flex items-center">
                                        แบบฝึกหัด 
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // คัดลอกลิงก์
        document.getElementById('copyLinkBtn').addEventListener('click', function() {
            const currentUrl = window.location.href;
            navigator.clipboard.writeText(currentUrl).then(function() {
                const originalText = document.getElementById('copyLinkBtn').innerHTML;
                document.getElementById('copyLinkBtn').innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    คัดลอกลิงก์แล้ว
                `;
                
                setTimeout(function() {
                    document.getElementById('copyLinkBtn').innerHTML = originalText;
                }, 2000);
            });
        });

        // ทำให้ปุ่มทำงานได้
        document.querySelectorAll('.btn-test, .btn-exercise').forEach(button => {
            button.addEventListener('click', function() {
                if (!this.classList.contains('btn-disabled')) {
                    alert('เปิดกิจกรรม: ' + this.textContent.trim());
                }
            });
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'94be7e6a6109d01f',t:'MTc0OTI4MTk0Ny4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script><iframe height="1" width="1" style="position: absolute; top: 0px; left: 0px; border: none; visibility: hidden;" src="./saved_resource.html"></iframe>

</body></html>