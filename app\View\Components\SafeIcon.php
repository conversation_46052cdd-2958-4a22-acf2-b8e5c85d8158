<?php

namespace App\View\Components;

use App\Helpers\IconHelper;
use Illuminate\View\Component;

class SafeIcon extends Component
{
    public string $icon;
    public string $class;
    public bool $solid;

    public function __construct(
        string $icon, 
        string $class = 'h-6 w-6', 
        bool $solid = false
    ) {
        $this->icon = IconHelper::getSafeIcon($icon);
        $this->class = $class;
        $this->solid = $solid;
    }

    public function render()
    {
        $component = $this->solid ? "heroicon-s-{$this->icon}" : "heroicon-o-{$this->icon}";
        
        return view('components.safe-icon', [
            'component' => $component,
            'class' => $this->class
        ]);
    }
}
