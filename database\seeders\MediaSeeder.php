<?php

namespace Database\Seeders;

use App\Models\MediaManager\Folder;
use App\Models\Team;
use Illuminate\Database\Seeder;

use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get available teams (assuming we have teams with IDs 1 and 2)
        $teams = Team::limit(2)->pluck('id')->toArray();
        
        if (empty($teams)) {
            $this->command->warn('No teams found. Please run TeamSeeder first.');
            return;
        }

        $this->command->info('Creating sample folders and media for teams...');

        // Create sample folders for each team
        foreach ($teams as $teamId) {
            $this->createSampleFoldersAndMedia($teamId);
        }

        // Update existing media records to have random team_id
        $this->updateExistingMedia($teams);

        $this->command->info('MediaSeeder completed successfully!');
    }

    /**
     * Create sample folders and media for a specific team
     */
    private function createSampleFoldersAndMedia(int $teamId): void
    {
        $teamName = Team::find($teamId)->name ?? "Team {$teamId}";
        
        // Create main folders for the team
        $folders = [
            [
                'name' => "{$teamName} - Product Images",
                'collection' => "team_{$teamId}_product_images",
                'description' => "Product images for {$teamName}",
                'icon' => 'heroicon-o-photo',
                'color' => '#3B82F6',
                'team_id' => $teamId,
            ],
            [
                'name' => "{$teamName} - Documents",
                'collection' => "team_{$teamId}_documents",
                'description' => "Documents and files for {$teamName}",
                'icon' => 'heroicon-o-document',
                'color' => '#10B981',
                'team_id' => $teamId,
            ],
            [
                'name' => "{$teamName} - Marketing Materials",
                'collection' => "team_{$teamId}_marketing",
                'description' => "Marketing materials for {$teamName}",
                'icon' => 'heroicon-o-megaphone',
                'color' => '#F59E0B',
                'team_id' => $teamId,
            ],
        ];

        foreach ($folders as $folderData) {
            $folder = Folder::create($folderData);
            
            // Create some sample media entries for each folder
            $this->createSampleMediaForFolder($folder);
        }
    }

    /**
     * Create sample media entries for a folder
     */
    private function createSampleMediaForFolder(Folder $folder): void
    {
        // Create 3-5 sample media entries per folder
        $mediaCount = rand(3, 5);
        
        for ($i = 1; $i <= $mediaCount; $i++) {
            // Get a random image file from the seeder images
            try {
                $randomImage = LocalImages::getRandomFile(LocalImages::SIZE_200x200);
                $fileName = $randomImage->getFilename();
                $filePath = $randomImage->getPathname();
                
                // Create media record
                Media::create([
                    'model_type' => 'App\\Models\\MediaManager\\Folder',
                    'model_id' => $folder->id,
                    'uuid' => \Illuminate\Support\Str::uuid(),
                    'collection_name' => $folder->collection,
                    'name' => "Sample {$folder->name} {$i}",
                    'file_name' => $fileName,
                    'mime_type' => 'image/jpeg',
                    'disk' => 'public',
                    'conversions_disk' => 'public',
                    'size' => filesize($filePath),
                    'manipulations' => json_encode([]),
                    'custom_properties' => json_encode([
                        'title' => "Sample {$folder->name} {$i}",
                        'description' => "Sample media file for {$folder->name}",
                        'team_name' => Team::find($folder->team_id)->name ?? 'Unknown Team',
                    ]),
                    'generated_conversions' => json_encode([]),
                    'responsive_images' => json_encode([]),
                    'order_column' => $i,
                    'team_id' => $folder->team_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
            } catch (\Exception $e) {
                $this->command->warn("Could not create sample media for folder {$folder->name}: " . $e->getMessage());
            }
        }
    }

    /**
     * Update existing media records to have random team_id
     */
    private function updateExistingMedia(array $teams): void
    {
        $existingMediaCount = Media::whereNull('team_id')->count();
        
        if ($existingMediaCount > 0) {
            $this->command->info("Updating {$existingMediaCount} existing media records with random team_id...");
            
            // Update existing media records in batches
            Media::whereNull('team_id')->chunk(100, function ($mediaRecords) use ($teams) {
                foreach ($mediaRecords as $media) {
                    $randomTeamId = $teams[array_rand($teams)];
                    $media->update(['team_id' => $randomTeamId]);
                }
            });
        }

        // Also update existing folders
        $existingFoldersCount = Folder::whereNull('team_id')->count();
        
        if ($existingFoldersCount > 0) {
            $this->command->info("Updating {$existingFoldersCount} existing folder records with random team_id...");
            
            Folder::whereNull('team_id')->chunk(100, function ($folders) use ($teams) {
                foreach ($folders as $folder) {
                    $randomTeamId = $teams[array_rand($teams)];
                    $folder->update(['team_id' => $randomTeamId]);
                }
            });
        }
    }
}
