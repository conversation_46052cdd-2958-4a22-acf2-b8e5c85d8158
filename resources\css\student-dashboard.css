/* Student Dashboard Specific Styles */

/* Card Styles */
.card {
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Progress Bar Styles */
.progress-bar {
    height: 10px;
    border-radius: 5px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 5px;
    transition: width 1s ease;
}

/* Calendar Styles */
.calendar-day {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    background-color: #f0f4ff;
}

.calendar-day.has-event {
    position: relative;
}

.calendar-day.has-event::after {
    content: '';
    position: absolute;
    bottom: 2px;
    width: 4px;
    height: 4px;
    background-color: #ff6b6b;
    border-radius: 50%;
}

.calendar-day.active {
    background-color: #4f46e5;
    color: white;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

/* Daily Practice Item */
.daily-practice-item {
    border-radius: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.daily-practice-item:hover {
    transform: scale(1.02);
}

.locked {
    position: relative;
    opacity: 0.7;
}

.locked::after {
    content: '🔒';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
}

/* Subject Icon */
.subject-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 100;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 16px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

/* Circular Progress Bar Styles */
.circular-progress {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 15px;
}

.circular-progress svg {
    transform: rotate(-90deg);
}

.circular-progress circle {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
}

.circular-progress .bg {
    stroke: rgba(255, 255, 255, 0.3);
}

.circular-progress .progress {
    stroke-dasharray: 283;
    transition: stroke-dashoffset 1s ease;
}

.circular-progress .percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: bold;
    color: white;
}

/* Subject Card Styles */
.subject-card {
    border-radius: 16px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.subject-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.subject-card h4 {
    margin-top: 10px;
    margin-bottom: 5px;
    font-weight: 600;
    color: white;
}

.subject-card p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.action-button {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    border: none;
    cursor: pointer;
}

.action-button svg {
    width: 16px;
    height: 16px;
    margin-right: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .circular-progress {
        width: 80px;
        height: 80px;
    }
    
    .action-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .action-button {
        width: 100%;
        justify-content: center;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out forwards;
    transform: translateX(-20px);
    opacity: 0;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Hover Effects */
.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Table Styles */
.table-hover tbody tr:hover {
    background-color: #f8fafc;
}

/* Status Indicators */
.status-completed {
    color: #10b981;
}

.status-pending {
    color: #f59e0b;
}

.status-overdue {
    color: #ef4444;
}

/* Progress Ring Animation */
.progress-ring {
    transition: stroke-dashoffset 0.5s ease-in-out;
}

/* Loading States */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
