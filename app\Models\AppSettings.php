<?php

namespace App\Models;

use CWSPS154\AppSettings\Models\AppSettings as BaseAppSettings;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 
 *
 * @property string $id
 * @property int|null $team_id
 * @property string|null $tab
 * @property string|null $key
 * @property string|null $default
 * @property string|null $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Team|null $team
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings forTeam($teamId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings global()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereTab($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AppSettings whereValue($value)
 * @mixin \Eloquent
 */
class AppSettings extends BaseAppSettings
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tab',
        'key',
        'default',
        'value',
        'team_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'team_id' => 'integer',
    ];

    /**
     * Get the team that owns the setting.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Scope a query to only include settings for a specific team.
     */
    public function scopeForTeam($query, $teamId)
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope a query to only include global settings (no team).
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('team_id');
    }

    /**
     * Get settings for a specific team with fallback to global settings.
     */
    public static function getTeamSettingWithFallback($tab, $key, $teamId = null)
    {
        // First try to get team-specific setting
        if ($teamId) {
            $teamSetting = static::where('tab', $tab)
                ->where('key', $key)
                ->where('team_id', $teamId)
                ->first();
                
            if ($teamSetting) {
                return $teamSetting->value;
            }
        }
        
        // Fallback to global setting
        $globalSetting = static::where('tab', $tab)
            ->where('key', $key)
            ->whereNull('team_id')
            ->first();
            
        return $globalSetting ? $globalSetting->value : null;
    }

    /**
     * Set a team-specific setting.
     */
    public static function setTeamSetting($tab, $key, $value, $teamId = null)
    {
        return static::updateOrCreate(
            [
                'tab' => $tab,
                'key' => $key,
                'team_id' => $teamId,
            ],
            [
                'value' => $value,
            ]
        );
    }

    /**
     * Delete team-specific settings when a team is deleted.
     */
    public static function deleteTeamSettings($teamId)
    {
        return static::where('team_id', $teamId)->delete();
    }
}
