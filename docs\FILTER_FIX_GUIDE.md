# Filter Fix Guide

## Issue Fixed: "Undefined array key 'price_from'"

### Problem
The table filters in PhysicalProductResource and DigitalProductResource were causing errors when trying to access array keys that might not exist in the filter data.

### Root Cause
The filter query functions were directly accessing `$data['price_from']` and `$data['price_to']` without checking if these keys existed in the array.

### Solution Applied

#### Before (Problematic Code):
```php
->query(function (Builder $query, array $data): Builder {
    return $query
        ->when(
            $data['price_from'],
            fn (Builder $query, $price): Builder => $query->where('price', '>=', $price),
        )
        ->when(
            $data['price_to'],
            fn (Builder $query, $price): Builder => $query->where('price', '<=', $price),
        );
})
```

#### After (Fixed Code):
```php
->query(function (Builder $query, array $data): Builder {
    return $query
        ->when(
            isset($data['price_from']) && $data['price_from'] !== '',
            fn (Builder $query): Builder => $query->where('price', '>=', $data['price_from']),
        )
        ->when(
            isset($data['price_to']) && $data['price_to'] !== '',
            fn (Builder $query): Builder => $query->where('price', '<=', $data['price_to']),
        );
})
```

### Key Changes Made

#### 1. Added Existence Checks
- `isset($data['price_from'])` - Checks if the key exists
- `$data['price_from'] !== ''` - Checks if the value is not empty

#### 2. Removed Unused Parameters
- Removed unused `$price` and `$qty` parameters from closures
- Simplified closure signatures to avoid IDE warnings

#### 3. Direct Value Access
- Access values directly from `$data` array within the closure
- No need to pass values as parameters to the closure

### Files Fixed

#### PhysicalProductResource.php
- Fixed price filter (price_from, price_to)
- Fixed quantity filter (qty_from, qty_to)

#### DigitalProductResource.php
- Fixed price filter (price_from, price_to)
- Removed unused Model import

### Filter Behavior

#### Empty Values
- Empty strings are treated as "no filter"
- Null values are treated as "no filter"
- Only non-empty values trigger the filter

#### Range Filtering
- `price_from` only: Shows products >= specified price
- `price_to` only: Shows products <= specified price
- Both values: Shows products within the price range

### Testing the Fix

#### Test Cases
1. **No filter values**: Should show all products
2. **Only minimum value**: Should filter >= minimum
3. **Only maximum value**: Should filter <= maximum
4. **Both values**: Should filter within range
5. **Empty strings**: Should be ignored (no filter applied)

#### Expected Behavior
- No more "Undefined array key" errors
- Filters work correctly with partial values
- Empty form submissions don't cause errors

### Prevention for Future Filters

#### Best Practice Pattern
```php
Tables\Filters\Filter::make('custom_filter')
    ->form([
        Forms\Components\TextInput::make('value_from')
            ->numeric()
            ->label('From'),
        Forms\Components\TextInput::make('value_to')
            ->numeric()
            ->label('To'),
    ])
    ->query(function (Builder $query, array $data): Builder {
        return $query
            ->when(
                isset($data['value_from']) && $data['value_from'] !== '',
                fn (Builder $query): Builder => $query->where('column', '>=', $data['value_from']),
            )
            ->when(
                isset($data['value_to']) && $data['value_to'] !== '',
                fn (Builder $query): Builder => $query->where('column', '<=', $data['value_to']),
            );
    })
```

#### Key Points
1. Always check `isset()` before accessing array keys
2. Check for empty strings with `!== ''`
3. Use direct array access within closures
4. Avoid unused parameters in closures

### Alternative Approaches

#### Using Null Coalescing
```php
->when(
    ($data['price_from'] ?? '') !== '',
    fn (Builder $query): Builder => $query->where('price', '>=', $data['price_from']),
)
```

#### Using array_key_exists()
```php
->when(
    array_key_exists('price_from', $data) && $data['price_from'] !== '',
    fn (Builder $query): Builder => $query->where('price', '>=', $data['price_from']),
)
```

### Impact

#### Fixed Resources
- ✅ PhysicalProductResource: Price and quantity filters
- ✅ DigitalProductResource: Price filters
- ✅ ProductResource: Uses QueryBuilder (no issues)

#### User Experience
- ✅ No more filter errors
- ✅ Smooth filtering experience
- ✅ Partial filter values work correctly
- ✅ Empty form submissions handled gracefully

This fix ensures robust filter handling across all product resources while maintaining the expected filtering functionality.
