<?php

namespace App\Filament\Resources\TaskResource\Pages;

use App\Filament\Resources\TaskResource;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\CreateRecord;

class CreateTask extends CreateRecord
{
    protected static string $resource = TaskResource::class;

    public function mount(): void
    {
        parent::mount();

        // Pre-fill form with datetime from URL parameter
        if (request()->has('start_datetime')) {
            $startDateTime = \Carbon\Carbon::parse(request('start_datetime'));
            $this->form->fill([
                'start_datetime' => $startDateTime,
                'end_datetime' => $startDateTime->copy()->addHour(),
            ]);
        }
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Automatically set team_id and user_id based on current tenant and user
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $data['team_id'] = Filament::getTenant()->id;
        }
        
        $data['user_id'] = auth()->id();

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back_to_calendar')
                ->label('Back to Calendar')
                ->icon('heroicon-o-calendar-days')
                ->color('gray')
                ->url($this->getResource()::getUrl('index')),
        ];
    }
}
