<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LessonResource\Pages;
use App\Models\Lesson;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LessonResource extends Resource
{
    protected static ?string $model = Lesson::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationLabel = 'Lessons';

    protected static ?string $modelLabel = 'Lesson';

    protected static ?string $pluralModelLabel = 'Lessons';

    // Hide from main navigation - lessons are managed through Books and Courses
    protected static bool $shouldRegisterNavigation = false;

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering through polymorphic relationship
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->whereHasMorph('lessonable', ['App\Models\Book', 'App\Models\Course'], function (Builder $query) {
                $query->where('team_id', Filament::getTenant()->id);
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\Select::make('lessonable_type')
                            ->label('Parent Type')
                            ->options([
                                'App\Models\Book' => 'Book',
                                'App\Models\Course' => 'Course',
                            ])
                            ->required()
                            ->reactive()
                            ->columnSpan(1),

                        Forms\Components\Select::make('lessonable_id')
                            ->label('Parent')
                            ->options(function (callable $get) {
                                $type = $get('lessonable_type');
                                if (!$type) return [];

                                $model = new $type;
                                $query = $model->newQuery();

                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }

                                if ($type === 'App\Models\Book') {
                                    $query->where('is_active', true);
                                    return $query->pluck('title', 'id');
                                } elseif ($type === 'App\Models\Course') {
                                    $query->where('is_active', true);
                                    return $query->pluck('title', 'id');
                                }

                                return [];
                            })
                            ->required()
                            ->searchable()
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\TextInput::make('chapter')
                            ->maxLength(255)
                            ->placeholder('e.g., Chapter 1, Unit 2, Section A')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('duration_minutes')
                            ->label('Duration (minutes)')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(480) // 8 hours max
                            ->placeholder('Expected lesson duration')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Order')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->columnSpan(1),
                        
                        Forms\Components\Toggle::make('is_published')
                            ->label('Published')
                            ->default(false)
                            ->columnSpan(1),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ])
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('objectives')
                            ->label('Learning Objectives')
                            ->rows(3)
                            ->placeholder('What students will learn from this lesson')
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('summary')
                            ->label('Lesson Summary')
                            ->rows(3)
                            ->placeholder('Brief summary of the lesson content')
                            ->columnSpan(1),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('book.title')
                    ->label('Book')
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('chapter')
                    ->searchable()
                    ->badge()
                    ->color('gray')
                    ->placeholder('No chapter'),
                
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('book.subject.name')
                    ->label('Subject')
                    ->badge()
                    ->color('primary')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('formatted_duration')
                    ->label('Duration')
                    ->placeholder('Not set'),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->numeric()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_published')
                    ->label('Published')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('book_id')
                    ->label('Book')
                    ->relationship('book', 'title', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    }),
                
                Tables\Filters\Filter::make('has_chapter')
                    ->label('Has Chapter')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('chapter')),
                
                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('Published Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLessons::route('/'),
            'create' => Pages\CreateLesson::route('/create'),
            'view' => Pages\ViewLesson::route('/{record}'),
            'edit' => Pages\EditLesson::route('/{record}/edit'),
        ];
    }
}
