<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class StudentDailyPracticeWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.student-daily-practice';
    
    protected int | string | array $columnSpan = [
        'md' => 1,
        'xl' => 1,
    ];

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getCurrentProgress(): array
    {
        return [
            'current' => 32,
            'total' => 50,
            'percentage' => 64,
            'subject' => 'คณิตศาสตร์'
        ];
    }

    public function getPracticeItems(): array
    {
        // Placeholder data - replace with actual practice data
        return [
            [
                'title' => 'บทที่ 1: พื้นฐานเศษส่วน',
                'status' => 'completed',
                'progress' => 100,
                'status_label' => 'ผ่านแล้ว',
                'status_color' => 'green',
                'icon' => 'heroicon-o-check',
                'locked' => false
            ],
            [
                'title' => 'บทที่ 2: การบวกเศษส่วน',
                'status' => 'in_progress',
                'progress' => 64,
                'status_label' => 'กำลังเรียน (32/50)',
                'status_color' => 'blue',
                'icon' => 'heroicon-o-plus',
                'locked' => false
            ],
            [
                'title' => 'บทที่ 3: การลบเศษส่วน',
                'status' => 'locked',
                'progress' => 0,
                'status_label' => 'ล็อกอยู่',
                'status_color' => 'gray',
                'icon' => 'heroicon-o-lock-closed',
                'locked' => true
            ],
            [
                'title' => 'บททดสอบ: เศษส่วน',
                'status' => 'locked',
                'progress' => 0,
                'status_label' => 'ล็อกอยู่',
                'status_color' => 'gray',
                'icon' => 'heroicon-o-lock-closed',
                'locked' => true
            ],
        ];
    }

    public function getRecommendedResources(): array
    {
        // Placeholder data - replace with actual resources
        return [
            [
                'title' => 'วิดีโอ: เทคนิคการบวกเศษส่วน',
                'description' => 'ระยะเวลา: 12 นาที',
                'type' => 'video',
                'color' => 'blue',
                'icon' => 'heroicon-o-video-camera'
            ],
            [
                'title' => 'ใบงาน: แบบฝึกหัดเศษส่วนเพิ่มเติม',
                'description' => 'PDF, 5 หน้า',
                'type' => 'worksheet',
                'color' => 'green',
                'icon' => 'heroicon-o-document-text'
            ],
            [
                'title' => 'เกม: เศษส่วนมหาสนุก',
                'description' => 'เล่นออนไลน์',
                'type' => 'game',
                'color' => 'purple',
                'icon' => 'heroicon-o-puzzle-piece'
            ],
            [
                'title' => 'เคล็ดลับ: การจำตัวส่วนร่วมมาก',
                'description' => 'อ่าน 5 นาที',
                'type' => 'tip',
                'color' => 'yellow',
                'icon' => 'heroicon-o-light-bulb'
            ],
        ];
    }
}
