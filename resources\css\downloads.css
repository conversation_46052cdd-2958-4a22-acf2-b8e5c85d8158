/* Downloads Page Specific Styles */

/* Font Family */
.font-sarabun {
    font-family: 'Sarabun', sans-serif;
}

/* Dropdown Styles */
.dropdown-content {
    display: none;
    position: absolute;
    background-color: white;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 50;
    border-radius: 8px;
    max-height: 300px;
    overflow-y: auto;
}

.dropdown:hover .dropdown-content {
    display: block;
}

.checkbox-item:hover {
    background-color: #f1f5f9;
}

/* Media Card Hover Effects */
.media-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.media-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Tab Active State */
.tab-active {
    color: #3b82f6;
    border-bottom: 3px solid #3b82f6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-container {
        overflow-x: auto;
    }
    
    .media-grid {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
}

/* Hide scrollbar for horizontal scroll */
.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
    display: none;
}

/* Media Type Button Hover Effects */
.media-type-btn {
    transition: all 0.3s ease;
}

.media-type-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Filter Button Styles */
.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn:hover {
    transform: translateY(-1px);
}

.filter-btn.active {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Badge Styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1.25rem;
}

.badge-blue {
    background-color: #dbeafe;
    color: #1e40af;
}

.badge-red {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-green {
    background-color: #dcfce7;
    color: #166534;
}

.badge-purple {
    background-color: #f3e8ff;
    color: #6b21a8;
}

.badge-yellow {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-indigo {
    background-color: #e0e7ff;
    color: #3730a3;
}

/* Price Tag Styles */
.price-tag {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-tag.free {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.price-tag.premium {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Rating Stars */
.rating-stars {
    display: flex;
    align-items: center;
    gap: 0.125rem;
}

.star {
    width: 1rem;
    height: 1rem;
    fill: currentColor;
}

.star.filled {
    color: #fbbf24;
}

.star.empty {
    color: #d1d5db;
}

/* Download Button Styles */
.download-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.download-btn:hover::before {
    left: 100%;
}

/* Search Input Styles */
.search-input {
    transition: all 0.3s ease;
}

.search-input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Table Hover Effects */
.table-row {
    transition: background-color 0.2s ease;
}

.table-row:hover {
    background-color: #f8fafc;
}

/* Loading Animation */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Staggered Animation Delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }

/* Gradient Background */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Card Shadow Hover */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Icon Bounce Animation */
.icon-bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0,0,0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}
