<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Team;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class DebugSuperAdmin extends Command
{
    protected $signature = 'debug:super-admin {email}';
    protected $description = 'Debug super admin user access and permissions';

    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email {$email} not found.");
            return 1;
        }

        $this->info("=== Super Admin Debug Information ===");
        $this->info("User ID: {$user->id}");
        $this->info("Name: {$user->name}");
        $this->info("Email: {$user->email}");
        $this->info("Team ID: " . ($user->team_id ?? 'NULL (Super Admin)'));
        $this->info("Is Active: " . ($user->is_active ? 'Yes' : 'No'));
        
        $this->info("\n=== Roles ===");
        $roles = $user->roles;
        if ($roles->isEmpty()) {
            $this->warn("No roles assigned!");
        } else {
            foreach ($roles as $role) {
                $this->info("- {$role->name} (ID: {$role->id})");
            }
        }

        $this->info("\n=== Role Checks ===");
        $this->info("Has super_admin role: " . ($user->hasRole('super_admin') ? 'Yes' : 'No'));
        $this->info("Is Super Admin (team_id = null + role): " . 
            ($user->team_id === null && $user->hasRole('super_admin') ? 'Yes' : 'No'));

        $this->info("\n=== Available Teams ===");
        $teams = Team::where('is_active', true)->get();
        if ($teams->isEmpty()) {
            $this->warn("No active teams found!");
        } else {
            foreach ($teams as $team) {
                $this->info("- {$team->name} (ID: {$team->id}, Slug: {$team->slug})");
            }
        }

        $this->info("\n=== Tenant Access ===");
        try {
            $tenants = $user->getTenants(app(\Filament\Panel::class));
            $this->info("Available tenants: " . $tenants->count());
            foreach ($tenants as $tenant) {
                $this->info("- {$tenant->name} (ID: {$tenant->id})");
            }
        } catch (\Exception $e) {
            $this->error("Error getting tenants: " . $e->getMessage());
        }

        $this->info("\n=== Permissions Sample ===");
        $samplePermissions = ['view_any_user', 'view_any_role', 'create_user'];
        foreach ($samplePermissions as $permission) {
            $this->info("Can {$permission}: " . ($user->can($permission) ? 'Yes' : 'No'));
        }

        $this->info("\n=== Database Check ===");
        $roleAssignments = \DB::table('model_has_roles')
            ->where('model_type', get_class($user))
            ->where('model_id', $user->id)
            ->get();
        
        $this->info("Role assignments in database: " . $roleAssignments->count());
        foreach ($roleAssignments as $assignment) {
            $role = Role::find($assignment->role_id);
            $this->info("- Role: {$role->name}, Team ID: " . ($assignment->team_id ?? 'NULL'));
        }

        return 0;
    }
}
