<?php

namespace App\Console\Commands;

use App\Models\MediaManager\Folder;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class TestFolderQuery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:test-folder-query';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the folder query used by FolderResource';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Testing Folder Query ===');

        // Test the exact query used by FolderResource
        $this->line("\n--- FolderResource Query (no request params) ---");
        
        $query = Folder::query();
        
        // Apply the same logic as FolderResource
        if(!request()->has('model_type') && !request()->has('collection')) {
            // This is the default case
            $query->where(function ($query) {
                $query->where(function ($subQuery) {
                    // Original condition for non-team folders
                    $subQuery->where('model_id', null)
                        ->where(function ($collectionQuery) {
                            $collectionQuery->where('collection', null)
                                ->orWhere('model_type', null);
                        });
                })->orWhere(function ($subQuery) {
                    // Team folders condition
                    $subQuery->where('model_id', null)
                        ->where('model_type', null)
                        ->whereNotNull('collection');
                });
            });
        }

        $folders = $query->get();
        
        $this->info("Found {$folders->count()} folders with FolderResource query:");
        if ($folders->count() > 0) {
            $this->table(
                ['ID', 'Name', 'Team ID', 'Collection', 'Model Type', 'Model ID'],
                $folders->map(function ($folder) {
                    return [
                        $folder->id,
                        $folder->name,
                        $folder->team_id ?? 'null',
                        $folder->collection ?? 'null',
                        $folder->model_type ?? 'null',
                        $folder->model_id ?? 'null',
                    ];
                })->toArray()
            );
        }

        // Test with global scopes disabled
        $this->line("\n--- Query without global scopes ---");
        $allFolders = Folder::withoutGlobalScopes()->get();
        $this->info("Total folders without global scopes: {$allFolders->count()}");

        // Test with global scopes enabled
        $this->line("\n--- Query with global scopes ---");
        $scopedFolders = Folder::all();
        $this->info("Total folders with global scopes: {$scopedFolders->count()}");

        // Show SQL query
        $this->line("\n--- SQL Query ---");
        $sqlQuery = Folder::query();
        $sqlQuery->where(function ($query) {
            $query->where(function ($subQuery) {
                $subQuery->where('model_id', null)
                    ->where(function ($collectionQuery) {
                        $collectionQuery->where('collection', null)
                            ->orWhere('model_type', null);
                    });
            })->orWhere(function ($subQuery) {
                $subQuery->where('model_id', null)
                    ->where('model_type', null)
                    ->whereNotNull('collection');
            });
        });
        
        $this->comment("SQL: " . $sqlQuery->toSql());
        $this->comment("Bindings: " . json_encode($sqlQuery->getBindings()));
    }
}
