# Automated Variant Generation System

## Overview

The product variant system now features automated generation from input tags, direct pricing instead of adjustments, and auto-generated hidden SKUs. Users can input variant options and automatically generate all combinations.

## Changes Made

### 1. Variant Input Tags

#### New Input Fields:
```php
Forms\Components\TagsInput::make('option1_variants')
    ->label(function (Forms\Get $get) {
        return ($get('variant_option1_label') ?: 'Option 1') . ' Variants';
    })
    ->placeholder('e.g., White, Black, Red')
    ->helperText('Enter variant values separated by commas or press Enter')
    ->live(onBlur: true)
    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
        static::generateVariantCombinations($set, $get);
    })

Forms\Components\TagsInput::make('option2_variants')
    ->label(function (Forms\Get $get) {
        return ($get('variant_option2_label') ?: 'Option 2') . ' Variants';
    })
    ->placeholder('e.g., <PERSON>, <PERSON>, L, XL')
    ->helperText('Enter variant values separated by commas or press Enter')
    ->live(onBlur: true)
    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
        static::generateVariantCombinations($set, $get);
    })
```

**Benefits:**
- **Easy Input**: Type values separated by commas or press Enter
- **Auto-Generation**: Variants created automatically when tags change
- **Dynamic Labels**: Input labels update based on option labels
- **Live Updates**: Combinations regenerate as you type

### 2. Automatic Variant Generation

#### Generation Logic:
```php
public static function generateVariantCombinations(Forms\Set $set, Forms\Get $get): void
{
    $option1Variants = $get('option1_variants') ?? [];
    $option2Variants = $get('option2_variants') ?? [];
    $baseSku = $get('sku') ?: 'PRODUCT';
    $basePrice = $get('price') ?? 0;

    if (empty($option1Variants)) {
        return;
    }

    $variants = [];
    $index = 1;

    if (empty($option2Variants)) {
        // Single dimension variants (only option1)
        foreach ($option1Variants as $option1) {
            $variants[] = [
                'sku' => $baseSku . '-V' . $index,
                'option1' => $option1,
                'option2' => '',
                'price' => $basePrice,
                'stock_quantity' => 0,
                'barcode' => '',
                'is_available' => true,
            ];
            $index++;
        }
    } else {
        // Two dimension variants (option1 × option2)
        foreach ($option1Variants as $option1) {
            foreach ($option2Variants as $option2) {
                $variants[] = [
                    'sku' => $baseSku . '-V' . $index,
                    'option1' => $option1,
                    'option2' => $option2,
                    'price' => $basePrice,
                    'stock_quantity' => 0,
                    'barcode' => '',
                    'is_available' => true,
                ];
                $index++;
            }
        }
    }

    $set('product_variants', $variants);
}
```

### 3. Changed Price Adjustment to Direct Price

#### Before:
```php
Forms\Components\TextInput::make('price_adjustment')
    ->label('Price +/-')
    ->numeric()
    ->default(0)
    ->rules(['numeric'])
    ->placeholder('0.00')
```

#### After:
```php
Forms\Components\TextInput::make('price')
    ->label('Price')
    ->numeric()
    ->default(function (Forms\Get $get) {
        return $get('../../price') ?: 0;
    })
    ->rules(['numeric', 'min:0'])
    ->placeholder('0.00')
    ->prefix('$')
```

**Benefits:**
- **Direct Pricing**: Set exact price for each variant
- **Base Price Default**: New variants start with base product price
- **Clear Display**: Dollar sign prefix for clarity
- **Flexible Pricing**: Each variant can have completely different price

### 4. Auto-Generated Hidden SKUs

#### Before:
```php
Forms\Components\TextInput::make('sku_suffix')
    ->label('SKU Suffix')
    ->placeholder('e.g., -RED-L')
```

#### After:
```php
Forms\Components\Hidden::make('sku')
    ->default(function (Forms\Get $get) {
        $baseSku = $get('../../sku') ?: 'PRODUCT';
        $index = $get('../../product_variants') ? count($get('../../product_variants')) : 0;
        return $baseSku . '-V' . ($index + 1);
    })
```

**Benefits:**
- **Automatic Generation**: SKUs created automatically
- **Consistent Format**: BaseSKU-V1, BaseSKU-V2, etc.
- **No Manual Entry**: Reduces errors and saves time
- **Hidden Field**: Cleaner interface

### 5. Updated Grid Layouts

#### Physical Products (5 Columns):
| Option 1 | Option 2 | Price | Stock | Barcode |
|----------|----------|-------|-------|---------|
| White    | S        | $24.99| 30    | 123456789101 |
| White    | M        | $24.99| 35    | 123456789102 |
| White    | L        | $24.99| 25    | 123456789103 |
| Black    | S        | $24.99| 20    | 123456789104 |
| Black    | M        | $24.99| 28    | 123456789105 |
| Black    | L        | $29.99| 15    | 123456789106 |

#### Digital Products (3 Columns):
| Option 1 | Option 2 | Price |
|----------|----------|-------|
| Personal | Windows  | $49.99|
| Personal | Mac      | $49.99|
| Commercial| Windows | $99.99|
| Commercial| Mac     | $99.99|
| Enterprise| Multi   | $199.99|

## Examples

### 1. T-Shirt Store Setup

#### Step 1: Set Labels
- **Option 1 Label**: "Color"
- **Option 2 Label**: "Size"

#### Step 2: Input Variants
- **Color Variants**: `White, Black, Navy`
- **Size Variants**: `S, M, L, XL`

#### Step 3: Auto-Generation
System automatically creates 12 combinations:
- White S, White M, White L, White XL
- Black S, Black M, Black L, Black XL  
- Navy S, Navy M, Navy L, Navy XL

#### Generated Variants:
| Color | Size | SKU      | Price | Stock | Barcode |
|-------|------|----------|-------|-------|---------|
| White | S    | SHIRT-V1 | $24.99| 0     |         |
| White | M    | SHIRT-V2 | $24.99| 0     |         |
| White | L    | SHIRT-V3 | $24.99| 0     |         |
| White | XL   | SHIRT-V4 | $24.99| 0     |         |
| Black | S    | SHIRT-V5 | $24.99| 0     |         |
| Black | M    | SHIRT-V6 | $24.99| 0     |         |
| Black | L    | SHIRT-V7 | $24.99| 0     |         |
| Black | XL   | SHIRT-V8 | $24.99| 0     |         |
| Navy  | S    | SHIRT-V9 | $24.99| 0     |         |
| Navy  | M    | SHIRT-V10| $24.99| 0     |         |
| Navy  | L    | SHIRT-V11| $24.99| 0     |         |
| Navy  | XL   | SHIRT-V12| $24.99| 0     |         |

### 2. Software Company Setup

#### Step 1: Set Labels
- **Option 1 Label**: "License Type"
- **Option 2 Label**: "Platform"

#### Step 2: Input Variants
- **License Type Variants**: `Personal, Commercial, Enterprise`
- **Platform Variants**: `Windows, Mac, Linux`

#### Step 3: Auto-Generation
System automatically creates 9 combinations:
- Personal Windows, Personal Mac, Personal Linux
- Commercial Windows, Commercial Mac, Commercial Linux
- Enterprise Windows, Enterprise Mac, Enterprise Linux

#### Generated Variants:
| License Type | Platform | SKU        | Price   |
|-------------|----------|------------|---------|
| Personal    | Windows  | SOFTWARE-V1| $49.99  |
| Personal    | Mac      | SOFTWARE-V2| $49.99  |
| Personal    | Linux    | SOFTWARE-V3| $49.99  |
| Commercial  | Windows  | SOFTWARE-V4| $99.99  |
| Commercial  | Mac      | SOFTWARE-V5| $99.99  |
| Commercial  | Linux    | SOFTWARE-V6| $99.99  |
| Enterprise  | Windows  | SOFTWARE-V7| $199.99 |
| Enterprise  | Mac      | SOFTWARE-V8| $199.99 |
| Enterprise  | Linux    | SOFTWARE-V9| $199.99 |

### 3. Single-Dimension Example - Paint Colors

#### Step 1: Set Labels
- **Option 1 Label**: "Color"
- **Option 2 Label**: (leave empty)

#### Step 2: Input Variants
- **Color Variants**: `Red, Blue, Green, Yellow, Purple`
- **Option 2 Variants**: (leave empty)

#### Step 3: Auto-Generation
System automatically creates 5 single-dimension variants:

#### Generated Variants:
| Color  | Option 2 | SKU     | Price | Stock | Barcode |
|--------|----------|---------|-------|-------|---------|
| Red    |          | PAINT-V1| $12.99| 0     |         |
| Blue   |          | PAINT-V2| $12.99| 0     |         |
| Green  |          | PAINT-V3| $12.99| 0     |         |
| Yellow |          | PAINT-V4| $12.99| 0     |         |
| Purple |          | PAINT-V5| $12.99| 0     |         |

## User Workflow

### Quick Setup Process:
1. ✅ **Set Option Labels**: "Color" and "Size"
2. ✅ **Input Option 1 Variants**: Type "White, Black, Navy"
3. ✅ **Input Option 2 Variants**: Type "S, M, L, XL"
4. ✅ **Auto-Generation**: 12 variants created automatically
5. ✅ **Bulk Price**: Click "Set All to Base Price" if needed
6. ✅ **Individual Adjustments**: Modify specific variant prices
7. ✅ **Stock & Barcodes**: Add stock levels and barcodes

### Manual Generation Button:
```php
Forms\Components\Actions\Action::make('generate_variants')
    ->label('Generate Variant Combinations')
    ->icon('heroicon-o-squares-plus')
    ->color('primary')
    ->action(function (Forms\Set $set, Forms\Get $get) {
        static::generateVariantCombinations($set, $get);
    })
    ->visible(function (Forms\Get $get) {
        $option1 = $get('option1_variants');
        return !empty($option1);
    })
```

## Technical Implementation

### Database Structure:
```json
{
    "option1_variants": ["White", "Black", "Navy"],
    "option2_variants": ["S", "M", "L", "XL"],
    "variant_option1_label": "Color",
    "variant_option2_label": "Size",
    "product_variants": [
        {
            "sku": "SHIRT-V1",
            "option1": "White",
            "option2": "S",
            "price": 24.99,
            "stock_quantity": 30,
            "barcode": "123456789101",
            "is_available": true
        }
    ]
}
```

### Bulk Price Update:
```php
->action(function (Forms\Set $set, Forms\Get $get) {
    $variants = $get('product_variants') ?? [];
    $basePrice = $get('price') ?? 0;
    
    foreach (array_keys($variants) as $index) {
        $set("product_variants.{$index}.price", $basePrice);
    }
})
```

## Benefits

### 1. Speed & Efficiency
- **Instant Generation**: Create dozens of variants in seconds
- **No Manual Entry**: Automatic SKU and price assignment
- **Bulk Operations**: Set all prices at once

### 2. Accuracy & Consistency
- **No Typos**: Automated generation prevents manual errors
- **Consistent Format**: All SKUs follow same pattern
- **Complete Coverage**: All combinations automatically included

### 3. Flexibility
- **Single or Double Dimension**: Works with 1 or 2 variant options
- **Custom Pricing**: Each variant can have unique price
- **Easy Modifications**: Add/remove variants by updating tags

### 4. User Experience
- **Intuitive Interface**: Tag input is familiar and easy
- **Live Updates**: See results immediately
- **Clean Layout**: Hidden SKUs reduce clutter

This automated variant generation system dramatically speeds up product setup while ensuring accuracy and consistency across all variant combinations!
