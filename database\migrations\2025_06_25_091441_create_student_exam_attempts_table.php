<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('student_exam_attempts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->foreignId('exam_id')->constrained('exams')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // student
            $table->integer('attempt_number')->default(1); // ครั้งที่ทำ
            $table->float('score_auto')->nullable(); // คะแนนที่ระบบตรวจให้อัตโนมัติ
            $table->float('score_manual')->nullable(); // คะแนนจากข้อเขียน (ครูให้ภายหลัง)
            $table->float('total_score')->nullable(); // คะแนนรวม
            $table->enum('status', ['in_progress', 'submitted', 'graded', 'expired'])->default('in_progress');
            $table->datetime('started_at')->nullable();
            $table->datetime('submitted_at')->nullable();
            $table->datetime('graded_at')->nullable();
            $table->foreignId('graded_by')->nullable()->constrained('users')->nullOnDelete(); // teacher who graded
            $table->text('teacher_feedback')->nullable(); // ความเห็นจากครู
            $table->json('exam_snapshot')->nullable(); // snapshot ของข้อสอบตอนทำ
            $table->timestamps();

            $table->index(['team_id', 'exam_id']);
            $table->index(['user_id', 'status']);
            $table->unique(['exam_id', 'user_id', 'attempt_number']); // ป้องกันการทำซ้ำ
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('student_exam_attempts');
    }
};
