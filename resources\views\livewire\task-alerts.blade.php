<div>
    @foreach($alerts as $index => $alert)
        <div class="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-4 max-w-sm"
             x-data="{ show: true }"
             x-show="show"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-x-full"
             x-transition:enter-end="opacity-100 transform translate-x-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-x-0"
             x-transition:leave-end="opacity-0 transform translate-x-full">

            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <x-heroicon-o-bell class="w-6 h-6 text-blue-500" />
                </div>

                <div class="ml-3 flex-1">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {{ $alert['title'] }}
                    </h3>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        {{ $alert['message'] }}
                    </p>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        {{ \Carbon\Carbon::parse($alert['timestamp'])->diffForHumans() }}
                    </p>
                </div>

                <div class="ml-4 flex-shrink-0">
                    <button wire:click="dismissAlert({{ $index }})"
                            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                        <x-heroicon-o-x-mark class="w-5 h-5" />
                    </button>
                </div>
            </div>
        </div>

        @if($alert['sound'] ?? false)
            <script>
                // Play notification sound
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('{{ $alert['title'] }}', {
                        body: '{{ $alert['message'] }}',
                        icon: '/favicon.ico'
                    });
                }

                // Play sound
                try {
                    const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                    audio.play().catch(() => {
                        // Ignore audio play errors
                    });
                } catch (e) {
                    // Ignore audio errors
                }
            </script>
        @endif
    @endforeach

    <script>
        // Request notification permission on page load
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }

        // Poll for new alerts every 30 seconds
        setInterval(() => {
            @this.call('checkForAlerts');
        }, 30000);
    </script>
</div>
