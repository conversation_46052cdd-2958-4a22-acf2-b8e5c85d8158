# Percentage Discounts & Product Variants Guide

## Overview

The wholesale pricing system has been updated to use percentage-based discounts with live price previews, and physical products now support color/size variants with individual pricing and stock management.

## Changes Made

### 1. Percentage-Based Wholesale Discounts

#### Before (Fixed Price):
```php
Forms\Components\TextInput::make('price')
    ->label('Wholesale Price')
    ->numeric()
    ->placeholder('e.g., 30.00')
```

#### After (Percentage Discount with Preview):
```php
Forms\Components\TextInput::make('discount_percentage')
    ->label('Discount %')
    ->numeric()
    ->rules(['numeric', 'min:0', 'max:100'])
    ->placeholder('e.g., 20')
    ->suffix('%')
    ->live(onBlur: true),

Forms\Components\Placeholder::make('calculated_price')
    ->label('Price After Discount')
    ->content(function (Forms\Get $get) {
        $basePrice = $get('../../price') ?? 0;
        $discount = $get('discount_percentage') ?? 0;
        
        if ($basePrice > 0 && $discount > 0) {
            $discountedPrice = $basePrice * (1 - ($discount / 100));
            return '$' . number_format($discountedPrice, 2);
        }
        
        return '$0.00';
    })
```

### 2. Product Variants System

#### New Feature for Physical Products:
```php
Forms\Components\Repeater::make('product_variants')
    ->label('Color & Size Options')
    ->schema([
        Forms\Components\TextInput::make('color')
            ->label('Color')
            ->required()
            ->placeholder('e.g., Red, Blue, Black'),

        Forms\Components\TextInput::make('size')
            ->label('Size')
            ->required()
            ->placeholder('e.g., S, M, L, XL'),

        Forms\Components\TextInput::make('sku_suffix')
            ->label('SKU Suffix')
            ->placeholder('e.g., -RED-L')
            ->helperText('Will be added to main SKU'),

        Forms\Components\TextInput::make('price_adjustment')
            ->label('Price Adjustment')
            ->numeric()
            ->default(0)
            ->placeholder('0.00')
            ->helperText('Additional cost (+) or discount (-) from base price'),

        Forms\Components\TextInput::make('stock_quantity')
            ->label('Stock Quantity')
            ->numeric()
            ->default(0)
            ->placeholder('0'),

        Forms\Components\Toggle::make('is_available')
            ->label('Available')
            ->default(true),
    ])
```

## Benefits

### 1. Percentage-Based Discounts

#### User Experience:
- **Intuitive Input**: Users think in percentages (20% off vs. calculating $40 from $50)
- **Live Preview**: See exact discounted price immediately
- **Consistent Margins**: Maintain profit margins across price changes
- **Easy Updates**: Change base price and all tiers update automatically

#### Business Logic:
- **Scalable Pricing**: Discounts scale with base price changes
- **Margin Protection**: Percentage ensures consistent profit margins
- **Competitive Pricing**: Easy to match competitor discount percentages

### 2. Product Variants

#### Inventory Management:
- **Individual Stock**: Track stock for each color/size combination
- **Price Flexibility**: Different prices for different variants (e.g., XXL +$5)
- **SKU Management**: Automatic SKU suffixes for variant tracking
- **Availability Control**: Enable/disable specific variants

#### Customer Experience:
- **Choice Options**: Multiple color and size combinations
- **Clear Pricing**: See price adjustments for different variants
- **Stock Visibility**: Know what's available in each variant

## Examples

### 1. Percentage Discount Examples

#### Physical Product (Office Chair - $100 base price):
```json
[
    {
        "min_quantity": 5,
        "discount_percentage": 10,
        "label": "Small Bulk"
    },
    {
        "min_quantity": 10,
        "discount_percentage": 20,
        "label": "Wholesale"
    },
    {
        "min_quantity": 25,
        "discount_percentage": 30,
        "label": "Volume Discount"
    }
]
```

**Pricing Structure:**
- **1-4 units**: $100.00 each (0% discount)
- **5-9 units**: $90.00 each (10% discount)
- **10-24 units**: $80.00 each (20% discount)
- **25+ units**: $70.00 each (30% discount)

#### Digital Product (Software License - $50 base price):
```json
[
    {
        "min_quantity": 3,
        "discount_percentage": 15,
        "label": "Team License"
    },
    {
        "min_quantity": 10,
        "discount_percentage": 30,
        "label": "Business License"
    },
    {
        "min_quantity": 25,
        "discount_percentage": 45,
        "label": "Enterprise License"
    }
]
```

**Pricing Structure:**
- **1-2 licenses**: $50.00 each (0% discount)
- **3-9 licenses**: $42.50 each (15% discount)
- **10-24 licenses**: $35.00 each (30% discount)
- **25+ licenses**: $27.50 each (45% discount)

### 2. Product Variant Examples

#### Clothing Item (T-Shirt - $25 base price):
```json
[
    {
        "color": "Black",
        "size": "M",
        "sku_suffix": "-BLK-M",
        "price_adjustment": 0.00,
        "stock_quantity": 25,
        "is_available": true
    },
    {
        "color": "Black",
        "size": "XXL",
        "sku_suffix": "-BLK-XXL",
        "price_adjustment": 5.00,
        "stock_quantity": 10,
        "is_available": true
    },
    {
        "color": "Red",
        "size": "L",
        "sku_suffix": "-RED-L",
        "price_adjustment": 0.00,
        "stock_quantity": 0,
        "is_available": false
    }
]
```

**Variant Pricing:**
- **Black/M**: $25.00 (base price + $0.00)
- **Black/XXL**: $30.00 (base price + $5.00)
- **Red/L**: Out of Stock

#### Shoes (Sneakers - $80 base price):
```json
[
    {
        "color": "White",
        "size": "US 9",
        "sku_suffix": "-WHT-9",
        "price_adjustment": 0.00,
        "stock_quantity": 15,
        "is_available": true
    },
    {
        "color": "Black",
        "size": "US 12",
        "sku_suffix": "-BLK-12",
        "price_adjustment": 0.00,
        "stock_quantity": 8,
        "is_available": true
    }
]
```

## Table Display Updates

### Wholesale Tiers Display

#### Before:
- "5+: $90.00 | 10+: $80.00"

#### After:
- "5+: 10% off | 10+: 20% off"

### Product Variants Display

#### Examples:
- **Few Variants**: "Black/M, White/L"
- **Many Variants**: "Black/M, White/L +8 more"
- **No Variants**: "None"

## Digital Product Security Stock

### Before:
```php
Forms\Components\TextInput::make('security_stock')
    ->label('Security Stock')
    ->helperText('Not applicable for digital products (set to 0)')
    ->numeric()
    ->default(0)
    ->required()
```

### After:
```php
Forms\Components\Hidden::make('security_stock')
    ->default(0)
```

**Benefits:**
- **Cleaner Forms**: No irrelevant fields for digital products
- **Automatic**: Always set to 0 for digital products
- **User-Friendly**: No confusion about inventory for digital items

## Migration & Data Updates

### Automatic Conversion

#### Existing Wholesale Pricing:
The seeder automatically converts existing fixed-price tiers to percentage-based:

```php
// Calculate discount percentage from existing data
$discountPercentage = round((($basePrice - $tierPrice) / $basePrice) * 100, 0);

$updatedPricing[] = [
    'min_quantity' => $tier['min_quantity'],
    'discount_percentage' => $discountPercentage,
    'label' => $tier['label'] ?? ''
];
```

#### Smart Variant Generation:
- **Clothing**: Colors × Sizes with XXL premium
- **Shoes**: Colors × Shoe sizes
- **Phone Cases**: Color options only
- **Bags**: Color × Size with size premiums
- **General**: Basic color options

## Technical Implementation

### Database Schema:
```sql
-- Updated wholesale_pricing structure
{
    "min_quantity": 10,
    "discount_percentage": 20,
    "label": "Wholesale"
}

-- New product_variants structure
{
    "color": "Black",
    "size": "L",
    "sku_suffix": "-BLK-L",
    "price_adjustment": 0.00,
    "stock_quantity": 25,
    "is_available": true
}
```

### Live Price Calculation:
```php
$discountedPrice = $basePrice * (1 - ($discount / 100));
```

### Form Validation:
- **Discount Percentage**: 0-100% range
- **Price Adjustment**: Numeric, can be negative
- **Stock Quantity**: Non-negative integer
- **Required Fields**: Color and size for variants

## User Workflow

### Setting Up Wholesale Pricing:
1. **Set Base Price**: Enter regular product price
2. **Add Discount Tier**: Click "Add Wholesale Tier"
3. **Enter Minimum Quantity**: e.g., 10
4. **Set Discount Percentage**: e.g., 20%
5. **See Live Preview**: Automatically shows $80.00 (if base is $100)
6. **Add Label**: Optional tier name
7. **Repeat**: Add multiple tiers as needed

### Setting Up Product Variants:
1. **Add Variant**: Click "Add Color/Size Variant"
2. **Enter Color**: e.g., "Black"
3. **Enter Size**: e.g., "L"
4. **Set SKU Suffix**: e.g., "-BLK-L"
5. **Price Adjustment**: e.g., +$5.00 for premium sizes
6. **Set Stock**: Individual stock quantity
7. **Toggle Availability**: Enable/disable variant

This enhanced system provides intuitive percentage-based pricing with live previews and comprehensive variant management for physical products!
