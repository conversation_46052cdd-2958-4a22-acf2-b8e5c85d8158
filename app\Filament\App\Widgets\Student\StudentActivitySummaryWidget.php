<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class StudentActivitySummaryWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.student-activity-summary';
    
    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getActivitySummary(): array
    {
        // Placeholder data - replace with actual activity data
        return [
            [
                'date' => '15 พ.ค. 2023',
                'subject' => 'คณิตศาสตร์',
                'time_spent' => '45 นาที',
                'score' => '92/100',
                'review_status' => 'reviewed',
                'review_label' => '✓ ทบทวนแล้ว',
                'review_color' => 'green'
            ],
            [
                'date' => '14 พ.ค. 2023',
                'subject' => 'วิทยาศาสตร์',
                'time_spent' => '30 นาที',
                'score' => '85/100',
                'review_status' => 'reviewed',
                'review_label' => '✓ ทบทวนแล้ว',
                'review_color' => 'green'
            ],
            [
                'date' => '13 พ.ค. 2023',
                'subject' => 'ภาษาไทย',
                'time_spent' => '60 นาที',
                'score' => '99/100',
                'review_status' => 'reviewed',
                'review_label' => '✓ ทบทวนแล้ว',
                'review_color' => 'green'
            ],
            [
                'date' => '12 พ.ค. 2023',
                'subject' => 'ภาษาอังกฤษ',
                'time_spent' => '40 นาที',
                'score' => '78/100',
                'review_status' => 'pending',
                'review_label' => '✗ ยังไม่ทบทวน',
                'review_color' => 'red'
            ],
            [
                'date' => '11 พ.ค. 2023',
                'subject' => 'คณิตศาสตร์',
                'time_spent' => '35 นาที',
                'score' => '88/100',
                'review_status' => 'reviewed',
                'review_label' => '✓ ทบทวนแล้ว',
                'review_color' => 'green'
            ],
        ];
    }
}
