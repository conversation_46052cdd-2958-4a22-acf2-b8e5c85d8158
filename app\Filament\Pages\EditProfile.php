<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;

class EditProfile extends Page implements HasForms, HasActions
{
    use InteractsWithForms, InteractsWithActions;

    protected static ?string $navigationIcon = 'heroicon-o-user-circle';

    protected static string $view = 'filament.pages.edit-profile';

    protected static ?string $title = 'Edit Profile';

    protected static ?string $navigationLabel = 'My Profile';

    protected static ?string $navigationGroup = 'Account';

    protected static ?int $navigationSort = 1;

    public ?array $profileData = [];
    public ?array $passwordData = [];
    public ?array $twoFactorData = [];

    public function mount(): void
    {
        $user = Auth::user();

        $this->profileData = [
            'name' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone,
            'avatar' => $user->avatar,
        ];

        $this->twoFactorData = [
            'two_factor_enabled' => $user->two_factor_secret !== null,
        ];
    }

    public function profileForm(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Profile Information')
                    ->schema([
                        Forms\Components\FileUpload::make('avatar')
                            ->label('Profile Picture')
                            ->image()
                            ->avatar()
                            ->directory('avatars')
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                    ])
                    ->columns(2),
            ])
            ->statePath('profileData');
    }

    public function passwordForm(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Change Password')
                    ->schema([
                        Forms\Components\TextInput::make('current_password')
                            ->label('Current Password')
                            ->password()
                            ->required()
                            ->currentPassword(),

                        Forms\Components\TextInput::make('password')
                            ->label('New Password')
                            ->password()
                            ->required()
                            ->rule(Password::default())
                            ->confirmed(),

                        Forms\Components\TextInput::make('password_confirmation')
                            ->label('Confirm New Password')
                            ->password()
                            ->required(),
                    ])
                    ->columns(1),
            ])
            ->statePath('passwordData');
    }

    public function twoFactorForm(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Two-Factor Authentication')
                    ->description('Add additional security to your account using two-factor authentication.')
                    ->schema([
                        Forms\Components\Toggle::make('two_factor_enabled')
                            ->label('Enable Two-Factor Authentication')
                            ->helperText('When enabled, you will be prompted for a secure, random token during authentication.')
                            ->live()
                            ->afterStateUpdated(function ($state) {
                                if ($state) {
                                    $this->enableTwoFactor();
                                } else {
                                    $this->disableTwoFactor();
                                }
                            }),

                        Forms\Components\Placeholder::make('two_factor_qr')
                            ->label('QR Code')
                            ->content(function () {
                                $user = Auth::user();
                                if ($user->two_factor_secret) {
                                    return new \Illuminate\Support\HtmlString(
                                        $user->twoFactorQrCodeSvg()
                                    );
                                }
                                return 'Enable two-factor authentication to see QR code.';
                            })
                            ->visible(fn () => Auth::user()->two_factor_secret !== null),

                        Forms\Components\Textarea::make('two_factor_recovery_codes')
                            ->label('Recovery Codes')
                            ->helperText('Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two-factor authentication device is lost.')
                            ->rows(5)
                            ->disabled()
                            ->visible(fn () => Auth::user()->two_factor_secret !== null)
                            ->default(function () {
                                $user = Auth::user();
                                return $user->recoveryCodes() ? implode("\n", json_decode(decrypt($user->two_factor_recovery_codes), true)) : '';
                            }),
                    ])
                    ->columns(1),
            ])
            ->statePath('twoFactorData');
    }

    public function updateProfile(): void
    {
        $data = $this->profileForm->getState();
        $user = Auth::user();

        $user->update([
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
        ]);

        if (isset($data['avatar'])) {
            $user->update(['avatar' => $data['avatar']]);
        }

        Notification::make()
            ->title('Profile updated successfully')
            ->success()
            ->send();
    }

    public function updatePassword(): void
    {
        $data = $this->passwordForm->getState();
        $user = Auth::user();

        $user->update([
            'password' => Hash::make($data['password']),
        ]);

        $this->passwordData = [];

        Notification::make()
            ->title('Password updated successfully')
            ->success()
            ->send();
    }

    public function enableTwoFactor(): void
    {
        $user = Auth::user();

        if (!$user->two_factor_secret) {
            $user->forceFill([
                'two_factor_secret' => encrypt(app('pragmarx.google2fa')->generateSecretKey()),
                'two_factor_recovery_codes' => encrypt(json_encode(collect(range(1, 8))->map(function () {
                    return \Illuminate\Support\Str::random(10) . '-' . \Illuminate\Support\Str::random(10);
                })->toArray())),
            ])->save();

            Notification::make()
                ->title('Two-factor authentication enabled')
                ->body('Please save your recovery codes in a safe place.')
                ->success()
                ->send();
        }
    }

    public function disableTwoFactor(): void
    {
        $user = Auth::user();

        $user->forceFill([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ])->save();

        Notification::make()
            ->title('Two-factor authentication disabled')
            ->warning()
            ->send();
    }

    public function regenerateRecoveryCodes(): void
    {
        $user = Auth::user();

        if ($user->two_factor_secret) {
            $user->forceFill([
                'two_factor_recovery_codes' => encrypt(json_encode(collect(range(1, 8))->map(function () {
                    return \Illuminate\Support\Str::random(10) . '-' . \Illuminate\Support\Str::random(10);
                })->toArray())),
            ])->save();

            Notification::make()
                ->title('Recovery codes regenerated')
                ->body('Please save your new recovery codes in a safe place.')
                ->success()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('updateProfile')
                ->label('Update Profile')
                ->action('updateProfile')
                ->color('primary'),

            Action::make('updatePassword')
                ->label('Update Password')
                ->action('updatePassword')
                ->color('warning'),

            Action::make('regenerateRecoveryCodes')
                ->label('Regenerate Recovery Codes')
                ->action('regenerateRecoveryCodes')
                ->color('danger')
                ->visible(fn () => Auth::user()->two_factor_secret !== null)
                ->requiresConfirmation()
                ->modalDescription('Are you sure you want to regenerate your recovery codes? Your old codes will no longer work.'),
        ];
    }
}
