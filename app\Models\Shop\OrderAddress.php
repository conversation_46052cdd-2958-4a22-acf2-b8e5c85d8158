<?php

namespace App\Models\Shop;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property string $addressable_type
 * @property int $addressable_id
 * @property string|null $country
 * @property string|null $street
 * @property string|null $city
 * @property string|null $state
 * @property string|null $zip
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model $addressable
 * @property-read \App\Models\Team|null $team
 * @method static \Database\Factories\Shop\OrderAddressFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress forCurrentTeam()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress forTeam(\App\Models\Team $team)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereAddressableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereAddressableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereStreet($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderAddress whereZip($value)
 * @mixin \Eloquent
 */
class OrderAddress extends Model
{
    use HasFactory;
    use BelongsToTeam;

    protected $table = 'shop_order_addresses';

    /** @return MorphTo<Model,self> */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }
}
