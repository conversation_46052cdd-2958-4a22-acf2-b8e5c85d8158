<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';
    
    protected static string $view = 'filament.app.pages.dashboard';

    /**
     * Get widgets based on user role
     */
    public function getWidgets(): array
    {
        $user = auth()->user();
        
        if (!$user) {
            return [];
        }

        // School Admin / Team Admin Dashboard
        if ($user->hasRole('team_admin') || $user->hasRole('school')) {
            return [
                \App\Filament\App\Widgets\School\SchoolOverviewWidget::class,
                \App\Filament\App\Widgets\School\StudentEnrollmentWidget::class,
                \App\Filament\App\Widgets\School\TeacherStatsWidget::class,
                \App\Filament\App\Widgets\School\RecentActivitiesWidget::class,
            ];
        }

        // Teacher Dashboard
        if ($user->hasRole('teacher')) {
            return [
                \App\Filament\App\Widgets\Teacher\TeacherStatsOverviewWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherCourseProgressWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherCalendarWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherQuickAccessWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherNotificationsWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherAssignmentManagementWidget::class,
                \App\Filament\App\Widgets\Teacher\TeacherStudentManagementWidget::class,
            ];
        }

        // Parent Dashboard
        if ($user->hasRole('parent')) {
            return [
                \App\Filament\App\Widgets\Parent\ParentOverviewWidget::class,
                \App\Filament\App\Widgets\Parent\ChildrenProgressWidget::class,
                \App\Filament\App\Widgets\Parent\ParentCalendarWidget::class,
                \App\Filament\App\Widgets\Parent\UpcomingEventsWidget::class,
                \App\Filament\App\Widgets\Parent\PaymentStatusWidget::class,
            ];
        }

        // Student Dashboard
        if ($user->hasRole('student')) {
            return [
                \App\Filament\App\Widgets\Student\StudentOverviewWidget::class,
                \App\Filament\App\Widgets\Student\MyGradesWidget::class,
                \App\Filament\App\Widgets\Student\StudentCalendarWidget::class,
                \App\Filament\App\Widgets\Student\StudentActivitySummaryWidget::class,
                \App\Filament\App\Widgets\Student\StudentDailyPracticeWidget::class,
                \App\Filament\App\Widgets\Student\AssignmentsDueWidget::class,
                \App\Filament\App\Widgets\Student\TodayScheduleWidget::class,
            ];
        }

        // Default widgets
        return [
            \Filament\Widgets\AccountWidget::class,
        ];
    }

    /**
     * Get the page title based on user role
     */
    public function getTitle(): string
    {
        $user = auth()->user();
        
        if (!$user) {
            return 'Dashboard';
        }

        if ($user->hasRole('team_admin') || $user->hasRole('school')) {
            return 'School Administration Dashboard';
        }

        if ($user->hasRole('teacher')) {
            return 'Teacher Dashboard';
        }

        if ($user->hasRole('parent')) {
            return 'Parent Dashboard';
        }

        if ($user->hasRole('student')) {
            return 'Student Dashboard';
        }

        return 'Dashboard';
    }

    /**
     * Get the page heading based on user role
     */
    public function getHeading(): string
    {
        $user = auth()->user();
        $teamName = $user?->team?->name ?? 'School';
        
        if (!$user) {
            return 'Welcome';
        }

        if ($user->hasRole('team_admin') || $user->hasRole('school')) {
            return "Welcome to {$teamName} Administration";
        }

        if ($user->hasRole('teacher')) {
            return "Welcome, {$user->name}";
        }

        if ($user->hasRole('parent')) {
            return "Welcome to {$teamName} Parent Portal";
        }

        if ($user->hasRole('student')) {
            return "Welcome to {$teamName} Student Portal";
        }

        return "Welcome to {$teamName}";
    }
}
