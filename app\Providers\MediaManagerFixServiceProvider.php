<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use TomatoPHP\FilamentMediaManager\Models\Folder;

class MediaManagerFixServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        //
    }

    public function boot(): void
    {
        // Override the problematic global scope after the application boots
        $this->app->booted(function () {
            // Remove the problematic 'user' global scope from the original Folder model
            if (class_exists(Folder::class)) {
                Folder::withoutGlobalScope('user');
                
                // Add our own config-based global scope
                Folder::addGlobalScope('user_config', function ($query) {
                    if(config('filament-media-manager.allow_user_access', false) && auth()->check()){
                        $query
                            ->where('user_id', auth()->id())
                            ->orWhere('is_public', false)
                            ->where('has_user_access', true)
                            ->whereHas('users', function ($query) {
                                $query->where('model_id', auth()->id())
                                    ->where('model_type', get_class(auth()->user()));
                            })
                            ->orWhere('is_public', true);
                    }
                });
            }
        });
    }
}
