<?php

namespace App\Models\Exam;

use App\Models\Team;
use App\Models\User;
use App\Models\Subject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;


class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'title',
        'description',
        'user_id',
        'subject_id',
        'type',
        'total_questions',
        'time_limit',
        'total_score',
        'randomize_questions',
        'randomize_choices',
        'show_results_immediately',
        'allow_multiple_attempts',
        'available_from',
        'available_until',
        'is_active',
    ];

    protected $casts = [
        'randomize_questions' => 'boolean',
        'randomize_choices' => 'boolean',
        'show_results_immediately' => 'boolean',
        'allow_multiple_attempts' => 'boolean',
        'is_active' => 'boolean',
        'available_from' => 'datetime',
        'available_until' => 'datetime',
        'total_score' => 'float',
    ];

    // Relationships
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function attempts(): HasMany
    {
        return $this->hasMany(StudentExamAttempt::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeAvailable($query)
    {
        $now = now();
        return $query->where(function ($q) use ($now) {
            $q->where('available_from', '<=', $now)
              ->orWhereNull('available_from');
        })->where(function ($q) use ($now) {
            $q->where('available_until', '>=', $now)
              ->orWhereNull('available_until');
        });
    }

    // Helper methods
    public function isAvailable(): bool
    {
        $now = now();

        if ($this->available_from && $now->lt($this->available_from)) {
            return false;
        }

        if ($this->available_until && $now->gt($this->available_until)) {
            return false;
        }

        return $this->is_active;
    }

    public function getRandomQuestions(int $count = null): \Illuminate\Database\Eloquent\Collection
    {
        $count = $count ?? $this->total_questions;

        return Question::where('team_id', $this->team_id)
            ->where('subject_id', $this->subject_id)
            ->where('is_active', true)
            ->inRandomOrder()
            ->limit($count)
            ->get();
    }
}
