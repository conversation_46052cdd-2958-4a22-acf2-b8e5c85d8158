<?php

namespace App\Filament\Pages;

use App\Filament\Settings\MainAppForm;
use CWSPS154\AppSettings\AppSettingsServiceProvider;
use CWSPS154\AppSettings\Settings\Forms\AppForm;
use Filament\Facades\Filament;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Gate;

/**
 * App Settings Page with Team-Specific Configuration
 *
 * Role-Based Cache Clearing:
 * - Regular Users: Can clear team settings cache only
 * - Super Admins: Can clear team settings cache + all Laravel cache (config, route, view, etc.)
 */
class AppSettings extends Page
{
    protected static string $view = 'app-settings::filament.pages.app-settings';

    public ?array $settings = [];

    public function mount(): void
    {
        // Clear cache when mounting to ensure fresh data
        $this->clearCurrentTeamCache();

        $settings = $this->getTeamSettings();
        $this->form->fill($settings);
    }

    /**
     * Get settings for the current team (always fresh)
     */
    protected function getTeamSettings(): array
    {
        $currentTenant = Filament::getTenant();
        $teamId = $currentTenant ? $currentTenant->id : null;

        // Always get fresh data to avoid cache issues
        return get_team_settings($teamId, null, true);
    }

    /**
     * Clear cache for the current team
     */
    protected function clearCurrentTeamCache(): void
    {
        $currentTenant = Filament::getTenant();
        $teamId = $currentTenant ? $currentTenant->id : null;

        clear_team_settings_cache($teamId);
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('Save')
                ->label(__('app-settings::app-settings.save'))
                ->color('primary')
                ->submit('save'),
        ];
    }

    public static function getTabs(): array
    {
        $tabs = [];
        $classes = self::getClassesInNamespace('Filament\\Settings\\Forms');
        $sortableClasses = [];

        if (method_exists(MainAppForm::class, 'getTab') &&
            method_exists(MainAppForm::class, 'getSortOrder')) {
            $sortableClasses[] = MainAppForm::class;
        }

        foreach ($classes as $class) {
            if (method_exists($class, 'getTab') && method_exists($class, 'getSortOrder')) {
                $sortableClasses[] = $class;
            }
        }

        usort($sortableClasses, function ($a, $b) {
            return $a::getSortOrder() <=> $b::getSortOrder();
        });

        foreach ($sortableClasses as $class) {
            $tabs[] = $class::getTab();
        }

        return $tabs;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs(self::getTabs())
                    ->persistTabInQueryString(),
            ])
            ->statePath('settings');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $currentTenant = Filament::getTenant();
        $teamId = $currentTenant ? $currentTenant->id : null;

        // Clear cache before saving
        $this->clearCurrentTeamCache();

        foreach ($data as $tab => $values) {
            $this->processValues($tab, $values, '', $teamId);
        }

        // Clear cache again after saving to ensure fresh data
        $this->clearCurrentTeamCache();

        // Also clear any potential global cache
        Cache::forget('settings_data.all');
        Cache::forget('app_settings_cache');

        $teamName = $currentTenant ? $currentTenant->name : 'Global';
        $this->successNotification(__('app-settings::app-settings.save-success') . " for {$teamName}");

        // Refresh the page to show updated data
        redirect(request()->header('Referer'));
    }

    private function processValues($tab, $values, $prefix = '', $teamId = null): void
    {
        if (is_array($values)) {
            foreach ($values as $field => $value) {
                $key = $prefix ? "{$prefix}.{$field}" : $field;

                if (is_array($value)) {
                    if (array_keys($value) === range(0, count($value) - 1)) {
                        foreach ($value as $index => $subValue) {
                            $this->processValues($tab, $subValue, "{$key}.{$index}", $teamId);
                        }
                    } else {
                        $this->processValues($tab, $value, $key, $teamId);
                    }
                } else {
                    \App\Models\AppSettings::updateOrCreate(
                        [
                            'tab' => $tab,
                            'key' => $key,
                            'team_id' => $teamId
                        ],
                        ['value' => $value]
                    );

                    // Aggressive cache clearing for this specific setting
                    $cacheKeys = [
                        $teamId ? "settings_data.{$teamId}.{$tab}.{$key}" : "settings_data.{$tab}.{$key}",
                        $teamId ? "settings_data.{$teamId}.{$key}" : "settings_data.{$key}",
                        $teamId ? "settings_data.{$teamId}.all" : 'settings_data.all',
                        "settings_data.{$tab}.{$key}",
                        'settings_data.all',
                        'app_settings_cache',
                        $teamId ? "app_settings_cache_{$teamId}" : null,
                    ];

                    foreach (array_filter($cacheKeys) as $cacheKey) {
                        Cache::forget($cacheKey);
                    }
                }
            }
        }
    }

    private function successNotification(string $title): void
    {
        Notification::make()
            ->title($title)
            ->success()
            ->send();
    }

    public static function getNavigationLabel(): string
    {
        return __('app-settings::app-settings.app.settings');
    }

    public function getTitle(): string|Htmlable
    {
        $currentTenant = Filament::getTenant();
        $teamName = $currentTenant ? $currentTenant->name : 'Global';

        return __('app-settings::app-settings.app.settings') . " - {$teamName}";
    }

    public function getSubheading(): string|Htmlable|null
    {
        $currentTenant = Filament::getTenant();
        $isSuperAdmin = $this->isSuperAdmin();

        $subheading = "";
        // if ($currentTenant) {
        //     $subheading = "Configure settings for {$currentTenant->name}. These settings will only apply to this team.";
        // } else {
        //     $subheading = "Configure global application settings.";
        // }

        // Add role-specific information
        if ($isSuperAdmin) {
            $lastCacheCleared = Cache::get('last_cache_cleared', 'Never');
            $lastClearedBy = Cache::get('last_cache_cleared_by', 'Unknown');

            if ($lastCacheCleared !== 'Never') {
                $subheading .= " | Last global cache cleared: {$lastCacheCleared} by {$lastClearedBy}";
            }
            // $subheading .= " | Super Admin: Can clear all cache types";
        } else {
            // $subheading .= " | Regular User: Can clear team settings cache only";
        }

        return $subheading;
    }

    /**
     * Refresh settings data (clear cache and reload)
     */
    public function refreshSettings(): void
    {
        $this->clearCurrentTeamCache();

        // Reload the form with fresh data
        $settings = $this->getTeamSettings();
        $this->form->fill($settings);

        $this->successNotification('Settings refreshed successfully!');
    }

    /**
     * Clear team settings cache only (available to all users)
     */
    public function clearTeamSettingsCache(): void
    {
        try {
            $startTime = microtime(true);
            $currentTenant = Filament::getTenant();
            $teamId = $currentTenant ? $currentTenant->id : null;
            $teamName = $currentTenant ? $currentTenant->name : 'Global';

            // Clear only current team's settings cache
            clear_team_settings_cache($teamId);

            // Reload the form with fresh data
            $settings = $this->getTeamSettings();
            $this->form->fill($settings);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            $this->successNotification("🔄 Team settings cache cleared for {$teamName}! - Completed in {$duration}ms");

        } catch (\Exception $e) {
            $this->errorNotification('Failed to clear team cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear all application cache (nuclear option - Super Admin Only)
     */
    public function clearAllCache(): void
    {
        // Double-check super admin access
        if (!$this->isSuperAdmin()) {
            $this->errorNotification('Access denied. Only super admins can clear all cache.');
            return;
        }

        try {
            $startTime = microtime(true);

            // Use our custom command for comprehensive cache clearing
            Artisan::call('settings:clear-cache', ['--nuclear' => true]);

            // Clear current team cache specifically
            $this->clearCurrentTeamCache();

            // Get Laravel cache clearing results
            $cacheResults = clear_laravel_cache();

            // Reload the form with fresh data
            $settings = $this->getTeamSettings();
            $this->form->fill($settings);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            // Create detailed success message
            $clearedItems = [];
            foreach ($cacheResults as $type => $status) {
                if ($status === 'cleared') {
                    $clearedItems[] = ucfirst($type);
                }
            }

            // Record when cache was last cleared (global)
            Cache::put('last_cache_cleared', now()->format('Y-m-d H:i:s'), 3600);
            Cache::put('last_cache_cleared_by', auth()->user()->name ?? 'Unknown', 3600);

            $message = '🚀 ALL CACHE CLEARED (Super Admin) - (' . implode(', ', $clearedItems) . ', All Team Settings)';
            $message .= " - Completed in {$duration}ms";

            $this->successNotification($message);

        } catch (\Exception $e) {
            $this->errorNotification('Failed to clear all cache: ' . $e->getMessage());
        }
    }

    /**
     * Show error notification
     */
    private function errorNotification(string $message): void
    {
        \Filament\Notifications\Notification::make()
            ->title('Error')
            ->body($message)
            ->danger()
            ->send();
    }

    /**
     * Get header actions for the page
     */
    protected function getHeaderActions(): array
    {
        $actions = [];

        // Team settings cache clear - available to all users
        $actions[] = \Filament\Actions\Action::make('clearTeamCache')
            ->label('Clear Team Settings Cache')
            ->icon('heroicon-o-arrow-path')
            ->color('warning')
            ->action('clearTeamSettingsCache')
            ->tooltip('Clear settings cache for current team only')
            ->requiresConfirmation()
            ->modalHeading('Clear Team Settings Cache')
            ->modalDescription('This will clear the settings cache for the current team. Are you sure?')
            ->modalSubmitActionLabel('Yes, Clear Team Cache');

        // Super admin only - clear all cache
        if ($this->isSuperAdmin()) {
            $actions[] = \Filament\Actions\Action::make('clearAllCache')
                ->label('Clear All Cache')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->action('clearAllCache')
                ->tooltip('Clear all cache (config, route, view, settings, etc.) - Super Admin Only')
                ->requiresConfirmation()
                ->modalHeading('Clear All Cache (Super Admin)')
                ->modalDescription('This will clear ALL application cache including config, routes, views, settings, and more across all teams. This is a powerful operation that affects the entire application. Are you sure?')
                ->modalSubmitActionLabel('Yes, Clear All Cache');
        }

        return $actions;
    }

    /**
     * Check if current user is super admin
     */
    protected function isSuperAdmin(): bool
    {
        $user = auth()->user();
        return $user && $user->team_id === null && $user->hasRole('super_admin');
    }

    public static function getNavigationIcon(): string|Htmlable|null
    {
        return 'heroicon-o-cog-8-tooth';
    }

    public static function getNavigationGroup(): ?string
    {
        return __('app-settings::app-settings.system');
    }

    public static function getNavigationSort(): ?int
    {
        return 100;
    }

    public static function canAccess(): bool
    {
        $plugin = Filament::getCurrentPanel()?->getPlugin(AppSettingsServiceProvider::$name);
        $access = $plugin->getCanAccess();
        if (! empty($access) && is_array($access) && isset($access['ability'], $access['arguments'])) {
            return Gate::allows($access['ability'], $access['arguments']);
        }

        return $access;
    }

    protected static function getClassesInNamespace(string $namespace): array
    {
        $composerClassMap = require base_path('vendor/composer/autoload_classmap.php');
        $classes = [];
        foreach ($composerClassMap as $class => $path) {
            if (str_contains($class, $namespace)) {
                $classes[] = $class;
            }
        }

        return $classes;
    }
}
