<?php

namespace Database\Factories;

use App\Models\TeachingSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

class TeachingScheduleFactory extends Factory
{
    protected $model = TeachingSchedule::class;

    public function definition(): array
    {
        $startTime = $this->faker->dateTimeBetween('now', '+1 week');
        $endTime = $this->faker->dateTimeBetween($startTime, $startTime->format('Y-m-d') . ' +4 hours');
        
        return [
            'start_time' => $startTime,
            'end_time' => $endTime,
            'notes' => $this->faker->sentence(),
            'status' => $this->faker->randomElement(['scheduled', 'in_progress', 'completed', 'cancelled']),
            'is_recurring' => $this->faker->boolean(60),
            'recurring_pattern' => $this->faker->boolean(60) ? [
                'frequency' => 'weekly',
                'interval' => 1,
                'days' => $this->faker->randomElements(['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], $this->faker->numberBetween(1, 3)),
                'start_date' => now()->format('Y-m-d'),
                'end_date' => now()->addMonths(3)->format('Y-m-d'),
            ] : null,
        ];
    }
}
