# Social Authentication Setup Guide

This guide explains how to set up social authentication for the EduNest registration and login pages.

## Configuration Options

You can enable/disable individual social login providers by setting these environment variables:

```env
# Social Authentication Master Switch
SOCIAL_AUTH_ENABLED=true

# Individual Provider Enable/Disable Switches
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true
PHONE_LOGIN_ENABLED=true
```

## Required Environment Variables

Add these variables to your `.env` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/google/callback

# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_REDIRECT_URI=http://127.0.0.1:8000/auth/microsoft/callback

# Apple Sign In Configuration
APPLE_CLIENT_ID=your_apple_client_id_here
APPLE_TEAM_ID=your_apple_team_id_here
APPLE_KEY_ID=your_apple_key_id_here
APPLE_PRIVATE_KEY_PATH=storage/app/private/apple_private_key.p8
APPLE_REDIRECT_URI=http://127.0.0.1:8000/auth/apple/callback

# LINE Login Configuration
LINE_CLIENT_ID=your_line_channel_id_here
LINE_CLIENT_SECRET=your_line_channel_secret_here
LINE_REDIRECT_URI=http://127.0.0.1:8000/auth/line/callback

# Phone Authentication (SMS) Configuration
SMS_PROVIDER=twilio
# For Twilio
TWILIO_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# Alternative SMS providers
# For AWS SNS
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=ap-southeast-1

# For Firebase (Google)
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id_here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your_firebase_client_email_here
FIREBASE_CLIENT_ID=your_firebase_client_id_here
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
```

## Setup Instructions

### 1. Google OAuth Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set application type to "Web application"
6. Add authorized redirect URIs:
   - `http://127.0.0.1:8000/auth/google/callback`
   - `http://localhost:8000/auth/google/callback`
   - `https://yourdomain.com/auth/google/callback` (for production)
7. Copy Client ID and Client Secret to `.env`

### 2. Microsoft OAuth Setup

1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to "Azure Active Directory" → "App registrations"
3. Click "New registration"
4. Set redirect URI to `http://127.0.0.1:8000/auth/microsoft/callback`
5. Go to "Certificates & secrets" → "New client secret"
6. Copy Application (client) ID and Client Secret to `.env`

### 3. Apple Sign In Setup

1. Go to [Apple Developer Portal](https://developer.apple.com/)
2. Navigate to "Certificates, Identifiers & Profiles"
3. Create a new App ID with "Sign In with Apple" capability
4. Create a Service ID for web authentication
5. Configure the Service ID with your domain and redirect URL
6. Create a private key for Sign In with Apple
7. Download the private key file and place it in `storage/app/private/`
8. Copy Team ID, Key ID, and Client ID to `.env`

### 4. LINE Login Setup

1. Go to [LINE Developers Console](https://developers.line.biz/)
2. Create a new provider or select existing one
3. Create a new channel with "LINE Login" type
4. Configure the channel settings:
   - App name and description
   - App icon and images
5. In "LINE Login" tab, add redirect URIs:
   - `http://127.0.0.1:8000/auth/line/callback`
   - `http://localhost:8000/auth/line/callback`
   - `https://yourdomain.com/auth/line/callback` (for production)
6. Copy Channel ID and Channel Secret to `.env`
7. Set required scopes: `profile`, `openid`, `email` (if needed)

### 5. Phone Authentication Setup

#### Option A: Twilio (Recommended)

1. Sign up at [Twilio](https://www.twilio.com/)
2. Get your Account SID and Auth Token from the dashboard
3. Purchase a phone number for sending SMS
4. Add credentials to `.env`

#### Option B: AWS SNS

1. Set up AWS account and IAM user with SNS permissions
2. Add AWS credentials to `.env`

#### Option C: Firebase (Google)

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project
3. Enable Authentication → Phone authentication
4. Download service account key JSON
5. Extract credentials and add to `.env`

## Laravel Socialite Configuration

Install Laravel Socialite for OAuth providers:

```bash
composer require laravel/socialite
```

Add to `config/services.php`:

```php
'google' => [
    'client_id' => env('GOOGLE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_CLIENT_SECRET'),
    'redirect' => env('GOOGLE_REDIRECT_URI'),
],

'microsoft' => [
    'client_id' => env('MICROSOFT_CLIENT_ID'),
    'client_secret' => env('MICROSOFT_CLIENT_SECRET'),
    'redirect' => env('MICROSOFT_REDIRECT_URI'),
],

'apple' => [
    'client_id' => env('APPLE_CLIENT_ID'),
    'client_secret' => '', // Will be generated from private key
    'redirect' => env('APPLE_REDIRECT_URI'),
    'team_id' => env('APPLE_TEAM_ID'),
    'key_id' => env('APPLE_KEY_ID'),
    'private_key' => env('APPLE_PRIVATE_KEY_PATH'),
],

'line' => [
    'client_id' => env('LINE_CLIENT_ID'),
    'client_secret' => env('LINE_CLIENT_SECRET'),
    'redirect' => env('LINE_REDIRECT_URI'),
],
```

## Configuration Management

### Enable/Disable Providers

You can easily enable or disable social login providers by setting environment variables:

```env
# Disable all social authentication
SOCIAL_AUTH_ENABLED=false

# Disable specific providers
GOOGLE_LOGIN_ENABLED=false
MICROSOFT_LOGIN_ENABLED=false
APPLE_LOGIN_ENABLED=false
LINE_LOGIN_ENABLED=false
PHONE_LOGIN_ENABLED=false
```

### Dynamic Provider Loading

The system automatically detects enabled providers and only shows buttons for configured and enabled providers. This means:

1. **No Configuration Required**: If a provider is not configured, it won't appear
2. **Easy Toggle**: Change `PROVIDER_LOGIN_ENABLED=false` to hide a provider
3. **Clean UI**: Only enabled providers show up in the login/register forms
4. **Automatic Updates**: No code changes needed to enable/disable providers

## Security Considerations

1. **Environment Variables**: Never commit `.env` file to version control
2. **HTTPS**: Use HTTPS in production for all OAuth callbacks
3. **State Parameter**: Always validate OAuth state parameter
4. **Rate Limiting**: Implement rate limiting for OTP requests
5. **Token Storage**: Store access tokens securely
6. **Scope Limitation**: Request only necessary OAuth scopes

## Testing

For development testing, you can use these test credentials:

- **Phone OTP**: Use `123456` as test OTP code
- **Google**: Use Google's test accounts
- **Microsoft**: Use Microsoft's test accounts
- **Apple**: Use Apple's sandbox environment

## Production Deployment

Before going to production:

1. Update all redirect URIs to use your production domain
2. Enable production mode for all OAuth providers
3. Set up proper SSL certificates
4. Configure rate limiting and security headers
5. Set up monitoring and logging for authentication events

## Troubleshooting

Common issues and solutions:

1. **Invalid Redirect URI**: Ensure redirect URIs match exactly in provider settings
2. **CORS Issues**: Configure CORS properly for your domain
3. **SSL Certificate**: Ensure valid SSL certificate for HTTPS callbacks
4. **Rate Limiting**: Implement proper rate limiting for SMS/OTP
5. **Token Expiry**: Handle token refresh properly

## Support

For additional help:
- Google OAuth: [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- Microsoft OAuth: [Microsoft Identity Platform](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- Apple Sign In: [Apple Sign In Documentation](https://developer.apple.com/sign-in-with-apple/)
- Twilio SMS: [Twilio SMS Documentation](https://www.twilio.com/docs/sms)
