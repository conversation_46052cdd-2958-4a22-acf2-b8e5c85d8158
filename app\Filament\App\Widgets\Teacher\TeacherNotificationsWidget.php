<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherNotificationsWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-notifications';
    
    protected int | string | array $columnSpan = [
        'md' => 1,
        'xl' => 1,
    ];

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getNotifications(): array
    {
        // Placeholder data - replace with actual notifications from database
        return [
            [
                'title' => 'ส่งแผนการสอนวิชาคณิตศาสตร์',
                'description' => 'กำหนดส่งภายใน 2 วัน',
                'type' => 'urgent', // urgent, warning, info
                'icon' => 'heroicon-o-exclamation-triangle',
                'color' => 'red'
            ],
            [
                'title' => 'ตรวจการบ้านวิชาภาษาไทย',
                'description' => 'มีนักเรียนส่งแล้ว 28 คน',
                'type' => 'warning',
                'icon' => 'heroicon-o-clipboard-document-check',
                'color' => 'yellow'
            ],
            [
                'title' => 'ประชุมครูประจำเดือน',
                'description' => 'วันศุกร์ที่ 26 พ.ค. เวลา 15:30',
                'type' => 'info',
                'icon' => 'heroicon-o-calendar-days',
                'color' => 'green'
            ],
        ];
    }
}
