<?php

namespace App\Filament\App\Widgets\School;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class StudentEnrollmentWidget extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('New Enrollments', '45')
                ->description('This month')
                ->color('success'),

            Stat::make('Pending Applications', '12')
                ->description('Awaiting approval')
                ->color('warning'),
        ];
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && ($user->hasRole('team_admin') || $user->hasRole('school'));
    }
}
