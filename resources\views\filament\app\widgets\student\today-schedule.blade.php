<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-clock class="w-5 h-5 text-primary-600 mr-2" />
                📅 ตารางเรียนวันนี้
            </div>
        </x-slot>

        <div class="space-y-3">
            @foreach($this->getTodayClasses() as $class)
                <div class="p-4 rounded-lg transition-all duration-200 hover:shadow-md
                    @if($class['status'] === 'current') bg-green-50 border-2 border-green-200 dark:bg-green-900/20 dark:border-green-700
                    @elseif($class['status'] === 'upcoming') bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-700
                    @else bg-gray-50 border border-gray-200 dark:bg-gray-800 dark:border-gray-700
                    @endif">
                    
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-4">
                            <div class="p-3 rounded-lg
                                @if($class['status'] === 'current') bg-green-100 dark:bg-green-900/50
                                @elseif($class['status'] === 'upcoming') bg-blue-100 dark:bg-blue-900/50
                                @else bg-gray-100 dark:bg-gray-700
                                @endif">
                                @if($class['status'] === 'current')
                                    <x-heroicon-o-play class="w-6 h-6 text-green-600 dark:text-green-400" />
                                @elseif($class['status'] === 'upcoming')
                                    <x-heroicon-o-clock class="w-6 h-6 text-blue-600 dark:text-blue-400" />
                                @else
                                    <x-heroicon-o-check class="w-6 h-6 text-gray-500 dark:text-gray-400" />
                                @endif
                            </div>
                            
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">{{ $class['subject'] }}</h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">ครู: {{ $class['teacher'] }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-500">{{ $class['room'] }}</p>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <div class="text-lg font-medium
                                @if($class['status'] === 'current') text-green-600 dark:text-green-400
                                @elseif($class['status'] === 'upcoming') text-blue-600 dark:text-blue-400
                                @else text-gray-500 dark:text-gray-400
                                @endif">
                                {{ $class['time'] }}
                            </div>
                            
                            @if($class['status'] === 'current')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                                    <span class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></span>
                                    กำลังเรียน
                                </span>
                            @elseif($class['status'] === 'upcoming')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                                    จะเริ่มเร็วๆ นี้
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400">
                                    เสร็จแล้ว
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
