<?php

namespace App\Filament\App\Widgets\School;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class RecentActivitiesWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.school.recent-activities';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && ($user->hasRole('team_admin') || $user->hasRole('school'));
    }

    public function getActivities(): array
    {
        // Placeholder data - replace with actual activities
        return [
            [
                'type' => 'enrollment',
                'message' => 'New student enrolled: <PERSON>',
                'time' => '2 hours ago',
                'icon' => 'heroicon-o-user-plus',
                'color' => 'success'
            ],
            [
                'type' => 'assignment',
                'message' => 'Math assignment submitted by Grade 10A',
                'time' => '4 hours ago',
                'icon' => 'heroicon-o-document-text',
                'color' => 'info'
            ],
            [
                'type' => 'payment',
                'message' => 'Tuition payment received from <PERSON>',
                'time' => '1 day ago',
                'icon' => 'heroicon-o-currency-dollar',
                'color' => 'warning'
            ],
        ];
    }
}
