<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'lessonable_id',
        'lessonable_type',
        'title',
        'chapter',
        'content',
        'objectives',
        'summary',
        'duration_minutes',
        'sort_order',
        'is_published',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'duration_minutes' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Get the parent model (book or course) that this lesson belongs to
     */
    public function lessonable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the book that this lesson belongs to (if it belongs to a book)
     */
    public function book()
    {
        return $this->lessonable_type === Book::class ? $this->lessonable : null;
    }

    /**
     * Get the course that this lesson belongs to (if it belongs to a course)
     */
    public function course()
    {
        return $this->lessonable_type === Course::class ? $this->lessonable : null;
    }

    /**
     * Get the subject through the parent relationship
     */
    public function subject()
    {
        return $this->lessonable?->subject;
    }

    /**
     * Get the team through the parent relationship
     */
    public function team()
    {
        return $this->lessonable?->team;
    }

    /**
     * Get the assignments for this lesson
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class);
    }

    /**
     * Scope a query to only include published lessons
     */
    public function scopePublished(Builder $query): Builder
    {
        return $query->where('is_published', true);
    }

    /**
     * Scope a query to filter by book
     */
    public function scopeForBook(Builder $query, int $bookId): Builder
    {
        return $query->where('lessonable_type', Book::class)
            ->where('lessonable_id', $bookId);
    }

    /**
     * Scope a query to filter by course
     */
    public function scopeForCourse(Builder $query, int $courseId): Builder
    {
        return $query->where('lessonable_type', Course::class)
            ->where('lessonable_id', $courseId);
    }

    /**
     * Scope a query to order by sort order
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order', 'asc');
    }

    /**
     * Get the display title with chapter
     */
    public function getDisplayTitleAttribute(): string
    {
        if ($this->chapter) {
            return "{$this->chapter}: {$this->title}";
        }

        return $this->title;
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration_minutes) {
            return 'Duration not set';
        }

        $hours = intval($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;

        if ($hours > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $minutes . ' minutes';
    }

    /**
     * Get content preview (first 150 characters)
     */
    public function getContentPreviewAttribute(): string
    {
        return \Str::limit(strip_tags($this->content), 150);
    }

    /**
     * Check if lesson has objectives
     */
    public function hasObjectives(): bool
    {
        return !empty($this->objectives);
    }

    /**
     * Check if lesson has summary
     */
    public function hasSummary(): bool
    {
        return !empty($this->summary);
    }
}
