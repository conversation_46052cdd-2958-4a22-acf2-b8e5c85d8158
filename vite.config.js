import { defineConfig } from 'vite'
import laravel, { refreshPaths } from 'laravel-vite-plugin'

export default defineConfig({
    plugins: [
        laravel.default({
            input: [
                'resources/css/app.css',
                'resources/css/layout.css',
                'resources/css/homepage.css',
                'resources/css/downloads.css',
                'resources/css/student-dashboard.css',
                'resources/css/teacher-dashboard.css',
                'resources/css/teacher-eventlist.css',
                'resources/css/teacher-eventshow.css',
                'resources/css/teacher-timetable.css',
                'resources/css/register.css',
                'resources/css/login.css',
                'resources/css/profile.css',
                'resources/css/sidebar.css',
                'resources/js/app.js',
                'resources/js/layout.js',
                'resources/js/animated-svg.js',
                'resources/js/homepage.js',
                'resources/js/downloads.js',
                'resources/js/student-dashboard.js',
                'resources/js/teacher-dashboard.js',
                'resources/js/teacher-eventlist.js',
                'resources/js/teacher-eventshow.js',
                'resources/js/teacher-timetable.js',
                'resources/js/register.js',
                'resources/js/login.js',
                'resources/js/profile.js',
                'resources/js/sidebar.js',
                
                'resources/css/filament/app/theme.css',
                'resources/css/filament/backend/theme.css'
            ],
            refresh: [
                ...refreshPaths,
                'app/Filament/**',
                'app/Forms/Components/**',
                'app/Livewire/**',
                'app/Infolists/Components/**',
                'app/Providers/Filament/**',
                'app/Tables/Columns/**',
            ],
        }),
    ],
})
