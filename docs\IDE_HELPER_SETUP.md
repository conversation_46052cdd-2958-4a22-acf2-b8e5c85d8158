# Laravel IDE Helper Setup

This project uses [barryvdh/laravel-ide-helper](https://github.com/barryvdh/laravel-ide-helper) to provide better IDE autocompletion and type hints for <PERSON><PERSON>'s dynamic methods.

## What's Included

The IDE helper package generates several files that help your IDE understand <PERSON><PERSON>'s magic methods:

- `_ide_helper.php` - Contains helper definitions for Laravel facades and container bindings
- `_ide_helper_models.php` - Contains PHPDoc annotations for Eloquent models (written directly to model files)
- `.phpstorm.meta.php` - Contains metadata for PhpStorm IDE

## Generated Files

These files are automatically generated and should not be edited manually:
- `_ide_helper.php`
- `.phpstorm.meta.php`

These files are added to `.gitignore` to prevent them from being committed to version control.

## Available Commands

### Generate All IDE Helper Files
```bash
composer run ide-helper
```

### Individual Commands
```bash
# Generate main helper file
composer run ide-helper:generate
# or
php artisan ide-helper:generate

# Generate model annotations
composer run ide-helper:models
# or
php artisan ide-helper:models --write

# Generate PhpStorm meta file
composer run ide-helper:meta
# or
php artisan ide-helper:meta
```

## When to Regenerate

You should regenerate the IDE helper files when:

1. **After adding new models** - Run `composer run ide-helper:models` to update model annotations
2. **After installing new packages** - Run `composer run ide-helper` to update all helper files
3. **After adding new facades or service providers** - Run `composer run ide-helper:generate`
4. **When IDE shows "undefined method" errors** - Usually fixed by regenerating the appropriate helper files

## Configuration

The IDE helper configuration is located in `config/ide-helper.php`. You can customize:

- Which models to include/exclude
- Which methods to include/exclude
- Output format preferences
- And more...

## Troubleshooting

### "Undefined method 'user'" Error
This error typically occurs when the IDE doesn't recognize Laravel's auth helper methods. Running `composer run ide-helper` should resolve this.

### "Undefined property '$allowSubFolders'" Error
This error occurs when the IDE doesn't recognize properties on Filament plugin instances. The IDE helper includes specific type hints for the FilamentMediaManagerPlugin to resolve this.

### Model Relationship Issues
If your IDE doesn't recognize model relationships, run:
```bash
composer run ide-helper:models
```

### Facade Method Issues
If your IDE doesn't recognize facade methods, run:
```bash
composer run ide-helper:generate
```

## Benefits

With IDE helper properly set up, you get:

- ✅ Autocompletion for `auth()->user()` and other helper methods
- ✅ Type hints for Eloquent model properties and relationships
- ✅ Autocompletion for facade methods
- ✅ Proper type hints for Filament plugin properties (e.g., `filament('filament-media-manager')->allowSubFolders`)
- ✅ Better code navigation and refactoring support
- ✅ Reduced "undefined method" and "undefined property" warnings in your IDE

## VS Code / Intelephense Configuration

For VS Code users with the Intelephense extension, additional configuration has been provided:

### Files Created
- `.vscode/settings.json` - VS Code workspace settings optimized for Laravel development
- `_ide_helper_intelephense.php` - Additional type hints specifically for Intelephense

### VS Code Settings Highlights
- Optimized Intelephense stubs for better PHP support
- Proper include paths for IDE helper files
- Configured diagnostics for better error reporting
- Disabled basic PHP suggestions in favor of Intelephense

## Notes

- The helper files are regenerated automatically when you run `composer install` or `composer update`
- Model annotations are written directly to your model files as PHPDoc comments
- The package respects your existing PHPDoc comments and only adds what's missing
- VS Code users should restart the PHP language server after initial setup: `Ctrl+Shift+P` → "PHP: Restart Language Server"
