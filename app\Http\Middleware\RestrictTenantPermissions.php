<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RestrictTenantPermissions
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Allow super admins (team_id = null) to access everything
        if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
            return $next($request);
        }

        // Check if the current route is for role or permission management
        $restrictedRoutes = [
            'filament.admin.resources.roles.',
            'filament.admin.resources.permissions.',
            'shield/roles',
        ];

        $currentRoute = $request->route()->getName();

        foreach ($restrictedRoutes as $restrictedRoute) {
            if (str_contains($currentRoute, $restrictedRoute)) {
                abort(403, 'Access denied. Tenant users cannot manage roles and permissions.');
            }
        }

        return $next($request);
    }
}
