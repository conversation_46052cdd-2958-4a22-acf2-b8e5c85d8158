/* Collapsible Sidebar Styles for Filament */

/* Toggle button styles */
.fi-sidebar-toggle-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: currentColor;
    transition: all 0.2s ease;
}

.fi-sidebar-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

/* Collapsed sidebar styles */
.fi-sidebar-collapsed {
    width: 4rem !important;
    min-width: 4rem !important;
    overflow: hidden;
    transition: all 0.3s ease;
}

.fi-sidebar-collapsed .fi-sidebar-nav {
    width: 4rem;
}

/* Hide text when collapsed */
.fi-sidebar-collapsed .fi-sidebar-item-label,
.fi-sidebar-collapsed .fi-sidebar-group-label,
.fi-sidebar-collapsed .fi-brand-text,
.fi-sidebar-collapsed .fi-user-name {
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* Show only icons when collapsed */
.fi-sidebar-collapsed .fi-sidebar-item-icon {
    margin-right: 0;
    justify-content: center;
}

/* Hover expansion */
.fi-sidebar-collapsed.fi-sidebar-hover-expanded {
    width: 16rem !important;
    min-width: 16rem !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    z-index: 50;
}

.fi-sidebar-collapsed.fi-sidebar-hover-expanded .fi-sidebar-nav {
    width: 16rem;
}

.fi-sidebar-collapsed.fi-sidebar-hover-expanded .fi-sidebar-item-label,
.fi-sidebar-collapsed.fi-sidebar-hover-expanded .fi-sidebar-group-label,
.fi-sidebar-collapsed.fi-sidebar-hover-expanded .fi-brand-text,
.fi-sidebar-collapsed.fi-sidebar-hover-expanded .fi-user-name {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.3s ease 0.1s, visibility 0.3s ease 0.1s;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .fi-sidebar-collapsed {
        width: 0 !important;
        min-width: 0 !important;
    }
    
    .fi-sidebar-collapsed.fi-sidebar-hover-expanded {
        width: 16rem !important;
        min-width: 16rem !important;
    }
}

/* Smooth transitions for all sidebar elements */
.fi-sidebar,
.fi-sidebar-nav,
.fi-sidebar-item,
.fi-sidebar-group {
    transition: all 0.3s ease;
}

/* Ensure proper spacing for icons */
.fi-sidebar-collapsed .fi-sidebar-item {
    justify-content: center;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Brand logo adjustments */
.fi-sidebar-collapsed .fi-brand {
    justify-content: center;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.fi-sidebar-collapsed .fi-brand-logo {
    margin-right: 0;
}
