# Social Account Connection System Guide

This guide explains the social account connection feature that allows users to link multiple social accounts to their profile for unified login access.

## Overview

The social account connection system allows users to:
- **Link multiple social accounts** to their existing profile
- **Login with any connected account** (email/password or any social provider)
- **Manage connected accounts** from their profile page
- **Safely disconnect accounts** with validation checks

## Features

### ✅ **Multi-Account Linking**
- Users can connect Google, Microsoft, Apple, and LINE accounts
- Each social account is stored separately in the `social_accounts` JSON field
- Original provider info is maintained for backward compatibility

### ✅ **Smart Login Detection**
- System detects if user is connecting (logged in) vs. logging in (guest)
- Uses session flag `social_connect_intent` to differentiate
- Prevents accidental account creation during connection

### ✅ **Safety Validations**
- Cannot disconnect if it's the only login method
- Prevents connecting accounts already linked to other users
- Requires at least one authentication method (password, phone, or social account)

### ✅ **Environment-Based Configuration**
- Providers are shown/hidden based on `LOGIN_ENABLED` environment variables
- Respects existing social authentication configuration

## Database Schema

### New Fields Added to `users` Table:
```sql
social_accounts JSON NULL          -- Stores connected social accounts
social_accounts_updated_at TIMESTAMP NULL  -- Last update timestamp
```

### Social Accounts JSON Structure:
```json
{
  "google": {
    "provider_id": "*********",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatar": "https://...",
    "connected_at": "2025-06-19T07:30:00.000Z"
  },
  "microsoft": {
    "provider_id": "*********",
    "email": "<EMAIL>",
    "name": "John Doe",
    "avatar": "https://...",
    "connected_at": "2025-06-19T08:15:00.000Z"
  }
}
```

## Configuration

### Environment Variables
The system respects existing `LOGIN_ENABLED` environment variables:

```env
# Enable/disable individual social providers
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true

# Provider credentials (existing)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
# ... etc for other providers
```

### Services Configuration
Uses existing `config/services.php` structure:
```php
'social_auth' => [
    'providers' => [
        'google' => [
            'enabled' => env('GOOGLE_LOGIN_ENABLED', false),
            // ... other config
        ],
        // ... other providers
    ],
],
```

## User Interface

### Profile Show Page (`/profile`)
- **Connected Accounts Section** displays all available providers
- **Visual indicators** show connection status with provider icons
- **Connect/Disconnect buttons** for each provider
- **Account information** shows connected email/name and connection date
- **Safety notice** explains unified login benefits

### Profile Edit Page (`/profile/edit`)
- **Same interface** as profile show page
- **Integrated with profile editing** workflow
- **JavaScript confirmations** for disconnect actions

### Provider Icons and Colors
- **Google**: `fab fa-google` - `#db4437`
- **Microsoft**: `fab fa-microsoft` - `#00a1f1`
- **Apple**: `fab fa-apple` - `#000000`
- **LINE**: `fab fa-line` - `#00c300`

## API Endpoints

### Connection Routes
```php
GET  /profile/connect/{provider}           // Initiate connection
GET  /profile/connect/{provider}/callback  // Handle connection callback
POST /profile/disconnect/{provider}        // Disconnect account
```

### Authentication Flow
```php
GET  /auth/{provider}                      // Login/Connect (auto-detected)
GET  /auth/{provider}/callback             // Handle login/connect callback
```

## User Flows

### 1. **Account Connection Flow**
```
User Profile → Click "Connect" → OAuth Flow → Account Linked → Return to Profile
```

### 2. **Account Disconnection Flow**
```
User Profile → Click "Disconnect" → Confirmation → Safety Check → Account Removed
```

### 3. **Login with Connected Account**
```
Login Page → Click Social Button → OAuth Flow → Auto-Login → Dashboard
```

### 4. **Safety Validation Flow**
```
Disconnect Request → Check Other Methods → Allow/Deny → Show Message
```

## Implementation Details

### Controllers

#### `SocialAccountController`
- **`connect()`**: Initiates OAuth flow with connection intent
- **`callback()`**: Handles OAuth callback for account linking
- **`disconnect()`**: Removes social account with safety checks

#### `SocialAuthController` (Updated)
- **Enhanced callback**: Detects connection vs. login intent
- **Dual account storage**: Updates both old and new formats
- **Backward compatibility**: Maintains existing functionality

### Models

#### `User` Model (Enhanced)
- **`getConnectedSocialAccounts()`**: Returns connected accounts array
- **`hasSocialAccount()`**: Checks if provider is connected
- **`addSocialAccount()`**: Adds/updates social account connection
- **`removeSocialAccount()`**: Removes social account connection
- **`getAvailableSocialProviders()`**: Returns enabled providers with UI config

### Frontend Components

#### JavaScript Functions
- **`disconnectSocialAccount()`**: Handles disconnect confirmation and form submission
- **FontAwesome integration**: Provides social provider icons
- **Responsive design**: Works on desktop and mobile

#### CSS Styling
- **Provider cards**: Clean, consistent design for each provider
- **Status indicators**: Clear visual feedback for connection status
- **Interactive buttons**: Hover effects and transitions
- **Color coding**: Provider-specific colors for brand recognition

## Security Features

### ✅ **Account Protection**
- Cannot disconnect if it's the only login method
- Validates user has password, phone, or other social accounts
- Prevents account lockout scenarios

### ✅ **Duplicate Prevention**
- Checks if social account is already connected to another user
- Prevents account hijacking or conflicts
- Clear error messages for conflicts

### ✅ **Session Management**
- Uses secure session flags for connection intent
- Clears session data after completion
- Prevents CSRF attacks with token validation

### ✅ **Data Integrity**
- Maintains both old and new account formats
- Atomic updates with proper error handling
- Consistent data structure across providers

## Testing

### Test Scenarios

1. **Connect New Account**:
   - Login with existing user
   - Go to profile page
   - Click "Connect" for any enabled provider
   - Complete OAuth flow
   - Verify account appears as connected

2. **Login with Connected Account**:
   - Logout from current session
   - Go to login page
   - Click social login for connected provider
   - Verify automatic login to same account

3. **Disconnect Account**:
   - Ensure user has multiple login methods
   - Click "Disconnect" for any connected account
   - Confirm in dialog
   - Verify account is removed

4. **Safety Validation**:
   - Try to disconnect the only login method
   - Verify error message appears
   - Confirm account remains connected

### Test Users
Use existing test users and connect social accounts:
```bash
# Login with test user
Email: <EMAIL>
Password: password

# Then connect social accounts via profile page
```

## Configuration Examples

### Enable All Providers
```env
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true
```

### Enable Only Google and Microsoft
```env
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=false
LINE_LOGIN_ENABLED=false
```

### Disable All Social Login
```env
GOOGLE_LOGIN_ENABLED=false
MICROSOFT_LOGIN_ENABLED=false
APPLE_LOGIN_ENABLED=false
LINE_LOGIN_ENABLED=false
```

## Benefits

### For Users
- **Convenience**: Login with any connected account
- **Flexibility**: Choose preferred login method
- **Security**: Multiple authentication options
- **Control**: Manage connections from profile

### For Administrators
- **Configuration**: Easy enable/disable via environment
- **Monitoring**: Track connection activity
- **Security**: Built-in safety validations
- **Compatibility**: Works with existing authentication

## Future Enhancements

1. **Account Merging**: Merge duplicate accounts with same email
2. **Login History**: Track which method was used for each login
3. **Primary Account**: Set preferred login method
4. **Bulk Management**: Admin tools for managing user connections
5. **Analytics**: Usage statistics for different providers

## Support

The social account connection system is fully integrated with the existing authentication system and maintains backward compatibility. Users can seamlessly connect and manage their social accounts while administrators have full control over which providers are available.

For questions or issues, check the configuration files and test the connection flow with the provided test scenarios.
