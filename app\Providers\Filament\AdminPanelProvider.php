<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Auth\Login;
use App\Filament\Pages\Dashboard;
use App\Http\Middleware\Authenticate;
use App\Models\Team;
use CWSPS154\AppSettings\AppSettingsPlugin;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationItem;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\SpatieLaravelTranslatablePlugin;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use App\Filament\Plugins\CustomMediaManagerPlugin;
use \MixCode\FilamentMulti2fa\FilamentMulti2faPlugin;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('backend')
            ->path('backend')
            ->darkMode(true) // Force dark theme
            ->login(Login::class)
            ->sidebarCollapsibleOnDesktop() 
            ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->unsavedChangesAlerts()
            ->brandLogo(fn() => view('filament.app.logo'))
            ->brandLogoHeight('1.25rem')
            ->navigationGroups([
                'System Management',
                'User Management',
                'Content',
                'Content Management',
                'Planning',
                'Shop',
                'Blog',
                // 'Filament Shield',
            ])
            ->navigationItems($this->getPostTypeNavigationItems())
            ->databaseNotifications()
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                \App\Http\Middleware\HandleSuperAdminAccess::class,
            ])
            ->tenant(Team::class, ownershipRelationship: 'team', slugAttribute: 'slug')
            ->tenantMiddleware([
                \App\Http\Middleware\EnforceTenantAccess::class,
                \BezhanSalleh\FilamentShield\Middleware\SyncShieldTenant::class,
                \App\Http\Middleware\RestrictTenantPermissions::class,
            ], isPersistent: true)
            // ->tenantRegistration(RegisterTeam::class)
            ->plugins([
                SpatieLaravelTranslatablePlugin::make()->defaultLocales(['en', 'th'])
                ,AppSettingsPlugin::make()
                    // ->appFormClass(\App\Filament\Settings\Forms\MainAppForm::class),
                ,FilamentShieldPlugin::make()
                ,CustomMediaManagerPlugin::make()
                    ->allowSubFolders()
                    ->allowUserAccess()
                ,FilamentMulti2faPlugin::make()
            ]); //required to enable this extension
    }

    /**
     * Get navigation items for post types
     */
    private function getPostTypeNavigationItems(): array
    {
        $postTypes = config('post-types.types', []);
        $navigationItems = [];

        foreach ($postTypes as $type => $config) {
            if ($config['enabled'] ?? true) {
                $navigationItems[] = NavigationItem::make($config['name'] . ' Posts')
                    ->icon($config['icon'] ?? 'heroicon-o-document-text')
                    ->group($config['navigation_group'] ?? 'Content')
                    ->sort($config['navigation_sort'] ?? 0)
                    ->url(fn (): string => \App\Filament\Resources\Blog\PostResource::getUrl('index', ['type' => $type]))
                    ->isActiveWhen(fn (): bool =>
                        request()->routeIs('filament.backend.resources.blog.posts.*') &&
                        request()->get('type') === $type
                    )
                    ->visible(fn (): bool =>
                        \App\Filament\Resources\Blog\PostResource::canAccess()
                    );
            }
        }

        return $navigationItems;
    }
}
