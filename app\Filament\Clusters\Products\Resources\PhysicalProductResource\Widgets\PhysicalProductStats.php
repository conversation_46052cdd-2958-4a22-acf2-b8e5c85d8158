<?php

namespace App\Filament\Clusters\Products\Resources\PhysicalProductResource\Widgets;

use App\Filament\Clusters\Products\Resources\PhysicalProductResource\Pages\ListPhysicalProducts;
use App\Models\Shop\Product;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class PhysicalProductStats extends BaseWidget
{
    use InteractsWithPageTable;

    protected static ?string $pollingInterval = null;

    protected function getTablePage(): string
    {
        return ListPhysicalProducts::class;
    }

    protected function getStats(): array
    {
        $query = $this->getPageTableQuery();
        $totalProducts = $query->count();
        $totalInventory = $query->sum('qty');
        $averagePrice = $query->avg('price');
        $lowStockCount = $query->whereColumn('qty', '<=', 'security_stock')->count();
        $outOfStockCount = $query->where('qty', 0)->count();
        $requiresShippingCount = $query->where('requires_shipping', true)->count();
        
        // Physical product specific stats
        $withSpecificationsCount = $query->whereNotNull('specifications')->count();
        $withDimensionsCount = $query->whereNotNull('weight_value')->count(); // Keep for backward compatibility

        return [
            Stat::make('Total Physical Products', $totalProducts)
                ->description('Physical inventory items')
                ->descriptionIcon('heroicon-m-cube')
                ->color('primary')
                ->chart([5, 12, 18, 25, 32, 28, $totalProducts])
                ->chartColor('primary'),

            Stat::make('Total Inventory', number_format($totalInventory))
                ->description('Units in stock')
                ->descriptionIcon('heroicon-m-archive-box')
                ->color('success')
                ->chart([100, 250, 400, 350, 500, 450, $totalInventory])
                ->chartColor('success'),

            Stat::make('Average Price', '$' . number_format($averagePrice, 2))
                ->description('Average product price')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('info'),

            Stat::make('Low Stock Alert', $lowStockCount)
                ->description("{$outOfStockCount} out of stock")
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($lowStockCount > 0 ? 'warning' : 'success'),

            Stat::make('Shipping Required', $requiresShippingCount)
                ->description('Products requiring shipping')
                ->descriptionIcon('heroicon-m-truck')
                ->color('gray'),

            Stat::make('With Specifications', $withSpecificationsCount)
                ->description("{$withDimensionsCount} with legacy dimensions")
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('indigo'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
