# Global Layout Structure - EduNest

## Overview

The header and footer CSS and JavaScript have been separated into global files that are used across all frontend pages. This improves maintainability, reduces code duplication, and ensures consistent behavior across the application.

## File Structure

### Global Layout Files

#### CSS
- **`resources/css/layout.css`** - Global header and footer styles
  - Header styles (navigation, logo, mobile menu)
  - Footer styles (links, social icons, sections)
  - Background shapes and animations
  - Scroll-to-top button
  - Loading states and notifications
  - Responsive design utilities

#### JavaScript
- **`resources/js/layout.js`** - Global header and footer functionality
  - Mobile menu toggle and navigation
  - Header scroll effects (hide/show on scroll)
  - Scroll-to-top button functionality
  - Page transition loading states
  - Active navigation highlighting
  - Notification system
  - Keyboard navigation improvements

### Frontend Layout Template
- **`resources/views/layouts/frontend.blade.php`** - Main layout template
  - Includes global layout CSS and JS files
  - Uses proper CSS classes from layout.css
  - Consistent header and footer structure

### Page-Specific Files
Each page now only includes its specific styles and functionality:

#### Homepage
- **`resources/css/homepage.css`** - Homepage-specific styles only
- **`resources/js/homepage.js`** - Homepage-specific functionality only
- Removed duplicate header/footer/mobile menu code

#### Teacher Pages
- **`resources/css/teacher-*.css`** - Page-specific styles
- **`resources/js/teacher-*.js`** - Page-specific functionality
- All use global layout for header/footer

#### Other Pages
- **`resources/css/downloads.css`** - Downloads page styles
- **`resources/css/student-dashboard.css`** - Student dashboard styles
- All inherit global layout functionality

## Key Features

### Global Layout CSS (`layout.css`)

1. **Header Styles**
   - `.header` - Main header container with backdrop blur
   - `.header.scrolled` - Enhanced shadow when scrolled
   - `.logo` - Animated logo with gradient text
   - `.nav-link` - Navigation links with hover effects
   - `.mobile-menu` - Slide-in mobile menu
   - `.menu-toggle` - Hamburger menu button animation

2. **Footer Styles**
   - `.footer` - Main footer with gradient background
   - `.footer-section` - Footer content sections
   - `.footer-link` - Footer links with hover animations
   - `.social-link` - Social media icons with hover effects

3. **Utility Classes**
   - `.gradient-text` - Gradient text effect
   - `.gradient-bg` - Gradient background
   - `.hover-lift` - Hover lift animation
   - `.fade-in`, `.slide-in-left`, `.slide-in-right` - Animation classes
   - `.scroll-to-top` - Scroll to top button

### Global Layout JavaScript (`layout.js`)

1. **Mobile Menu Management**
   - Smooth slide-in/out animations
   - Overlay background with blur
   - Focus trap for accessibility
   - Escape key to close
   - Auto-close on window resize

2. **Header Behavior**
   - Hide/show header on scroll
   - Add shadow when scrolled
   - Smooth transitions

3. **Navigation**
   - Active page highlighting
   - Smooth scrolling for anchor links
   - Keyboard navigation support

4. **Utility Functions**
   - `window.LayoutUtils.showLoading()` - Show loading overlay
   - `window.LayoutUtils.hideLoading()` - Hide loading overlay
   - `window.LayoutUtils.closeMobileMenu()` - Close mobile menu
   - `window.LayoutUtils.showNotification()` - Show notifications
   - `window.LayoutUtils.animateOnScroll()` - Scroll animations

## Usage

### In Blade Templates

```blade
@extends('layouts.frontend')

@section('title', 'Page Title')

@push('styles')
    @vite('resources/css/page-specific.css')
@endpush

@push('scripts')
    @vite('resources/js/page-specific.js')
@endpush

@section('content')
    <!-- Page content -->
@endsection
```

### In Page-Specific JavaScript

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // Use global layout utilities
    window.LayoutUtils.showNotification('Success message', 'success');
    
    // Close mobile menu programmatically
    window.LayoutUtils.closeMobileMenu();
    
    // Show/hide loading
    window.LayoutUtils.showLoading();
    setTimeout(() => {
        window.LayoutUtils.hideLoading();
    }, 2000);
});
```

### CSS Class Usage

```html
<!-- Use global utility classes -->
<h1 class="gradient-text">Title with gradient</h1>
<button class="gradient-bg hover-lift">Button</button>
<div class="fade-in">Animated content</div>
```

## Benefits

1. **Maintainability**
   - Single source of truth for header/footer styles and behavior
   - Easy to update global navigation across all pages
   - Consistent user experience

2. **Performance**
   - Reduced code duplication
   - Smaller page-specific files
   - Better caching of global assets

3. **Developer Experience**
   - Clear separation of concerns
   - Reusable utility functions
   - Consistent API across pages

4. **Accessibility**
   - Focus management in mobile menu
   - Keyboard navigation support
   - ARIA labels and proper semantics

## Migration Notes

- Removed duplicate mobile menu code from individual pages
- Updated header/footer HTML to use semantic CSS classes
- Page-specific files now only contain unique functionality
- All pages automatically inherit global layout improvements

## Browser Support

- Modern browsers with CSS Grid and Flexbox support
- Graceful degradation for older browsers
- Responsive design for all screen sizes
- Touch-friendly mobile interactions

## Future Enhancements

- Dark mode support in global layout
- Advanced animation preferences
- Progressive Web App features
- Enhanced accessibility features
