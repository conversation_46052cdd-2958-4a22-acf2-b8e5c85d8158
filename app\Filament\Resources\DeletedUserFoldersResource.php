<?php

namespace App\Filament\Resources;

use App\Filament\Resources\DeletedUserFoldersResource\Pages;
use App\Models\MediaManager\Folder;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DeletedUserFoldersResource extends Resource
{
    protected static ?string $model = Folder::class;

    protected static ?string $navigationIcon = 'heroicon-o-trash';

    protected static ?string $navigationLabel = 'Deleted User Folders';

    protected static ?string $modelLabel = 'Deleted User Folder';

    protected static ?string $pluralModelLabel = 'Deleted User Folders';

    protected static ?string $navigationGroup = 'Content';

    protected static ?int $navigationSort = 99;

    public static function canAccess(): bool
    {
        return auth()->user()?->hasRole('super_admin') ?? false;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->deletedUsers()
            ->withoutGlobalScopes([
                'team_folder',
                'user_folder',
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->maxLength(1000),
                Forms\Components\Select::make('team_id')
                    ->relationship('team', 'name')
                    ->required(),
                Forms\Components\Toggle::make('is_deleted_user')
                    ->label('Is Deleted User Folder')
                    ->disabled(),
                Forms\Components\Toggle::make('is_personal')
                    ->label('Is Personal Folder')
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('team.name')
                    ->label('Team')
                    ->sortable(),
                Tables\Columns\TextColumn::make('owner.name')
                    ->label('Original Owner')
                    ->sortable()
                    ->default('N/A'),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->sortable()
                    ->default('N/A'),
                Tables\Columns\TextColumn::make('media_count')
                    ->label('Media Files')
                    ->getStateUsing(function (Folder $record) {
                        return $record->media()->count();
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('team_id')
                    ->relationship('team', 'name')
                    ->label('Team'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->label('Permanently Delete')
                    ->requiresConfirmation()
                    ->modalHeading('Permanently Delete Folder')
                    ->modalDescription('This will permanently delete the folder and all its media files. This action cannot be undone.')
                    ->action(function (Folder $record) {
                        // Delete all media in this folder
                        $record->media()->delete();
                        // Delete the folder
                        $record->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Permanently Delete Selected')
                        ->requiresConfirmation()
                        ->modalHeading('Permanently Delete Folders')
                        ->modalDescription('This will permanently delete the selected folders and all their media files. This action cannot be undone.')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                // Delete all media in this folder
                                $record->media()->delete();
                                // Delete the folder
                                $record->delete();
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeletedUserFolders::route('/'),
            'create' => Pages\CreateDeletedUserFolders::route('/create'),
            'view' => Pages\ViewDeletedUserFolders::route('/{record}'),
            'edit' => Pages\EditDeletedUserFolders::route('/{record}/edit'),
        ];
    }
}
