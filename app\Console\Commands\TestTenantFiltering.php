<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Models\User;
use App\Models\Shop\Product;
use App\Models\Blog\Post;
use Filament\Facades\Filament;
use Illuminate\Console\Command;

class TestTenantFiltering extends Command
{
    protected $signature = 'test:tenant-filtering {user_email} {tenant_slug?}';
    protected $description = 'Test tenant filtering for a specific user and tenant';

    public function handle()
    {
        $userEmail = $this->argument('user_email');
        $tenantSlug = $this->argument('tenant_slug');

        $user = User::where('email', $userEmail)->first();
        if (!$user) {
            $this->error("User with email {$userEmail} not found.");
            return 1;
        }

        $this->info("=== Testing Tenant Filtering ===");
        $this->info("User: {$user->name} ({$user->email})");
        $this->info("User Team ID: " . ($user->team_id ?? 'NULL (Super Admin)'));

        // Set the user context
        auth()->login($user);

        if ($tenantSlug) {
            $tenant = Team::where('slug', $tenantSlug)->first();
            if (!$tenant) {
                $this->error("Tenant with slug {$tenantSlug} not found.");
                return 1;
            }

            $this->info("Setting tenant context: {$tenant->name} (ID: {$tenant->id})");
            
            // Simulate Filament tenant context
            app()->instance('filament.tenant', $tenant);
            
            $this->info("\n=== With Tenant Context ===");
        } else {
            $this->info("\n=== Without Tenant Context ===");
        }

        // Test data filtering
        $this->testDataFiltering();

        return 0;
    }

    private function testDataFiltering()
    {
        $models = [
            'Users' => User::class,
            'Products' => Product::class,
            'Blog Posts' => Post::class,
        ];

        foreach ($models as $name => $modelClass) {
            if (class_exists($modelClass)) {
                try {
                    $count = $modelClass::count();
                    $this->info("📊 {$name}: {$count} records visible");
                    
                    // Show first few records with team_id
                    $records = $modelClass::take(3)->get(['id', 'team_id', 'name']);
                    foreach ($records as $record) {
                        $recordName = $record->name ?? $record->title ?? "Record {$record->id}";
                        $this->line("   - {$recordName} (team_id: {$record->team_id})");
                    }
                } catch (\Exception $e) {
                    $this->error("❌ {$name}: Error - " . $e->getMessage());
                }
            }
        }

        // Test raw queries to see actual data
        $this->info("\n=== Raw Data (bypassing scopes) ===");
        $totalUsers = \DB::table('users')->count();
        $totalProducts = \DB::table('shop_products')->count();
        $this->info("📊 Total Users (raw): {$totalUsers}");
        $this->info("📊 Total Products (raw): {$totalProducts}");

        // Show team distribution
        $teamDistribution = \DB::table('users')
            ->select('team_id', \DB::raw('count(*) as count'))
            ->groupBy('team_id')
            ->get();
        
        $this->info("\n=== User Team Distribution ===");
        foreach ($teamDistribution as $dist) {
            $teamName = $dist->team_id ? Team::find($dist->team_id)?->name ?? "Team {$dist->team_id}" : 'Super Admins';
            $this->line("   - {$teamName}: {$dist->count} users");
        }
    }
}
