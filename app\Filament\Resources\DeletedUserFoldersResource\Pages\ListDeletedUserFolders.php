<?php

namespace App\Filament\Resources\DeletedUserFoldersResource\Pages;

use App\Filament\Resources\DeletedUserFoldersResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDeletedUserFolders extends ListRecords
{
    protected static string $resource = DeletedUserFoldersResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            Actions\Action::make('clear_all_deleted')
                ->label('Clear All Deleted User Folders')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('Clear All Deleted User Folders')
                ->modalDescription('This will permanently delete ALL folders belonging to deleted users and their media files. This action cannot be undone.')
                ->action(function () {
                    $deletedFolders = \App\Models\MediaManager\Folder::deletedUsers()
                        ->withoutGlobalScopes(['team_folder', 'user_folder'])
                        ->get();

                    foreach ($deletedFolders as $folder) {
                        // Delete all media in this folder
                        $folder->media()->delete();
                        // Delete the folder
                        $folder->delete();
                    }

                    \Filament\Notifications\Notification::make()
                        ->title('Deleted user folders cleared')
                        ->body("Successfully deleted {$deletedFolders->count()} folders and their media files.")
                        ->success()
                        ->send();
                })
                ->visible(fn () => \App\Models\MediaManager\Folder::deletedUsers()->withoutGlobalScopes(['team_folder', 'user_folder'])->exists()),
        ];
    }
}
