<?php

namespace App\Console\Commands;

use App\Models\MediaManager\Folder;
use App\Models\MediaManager\Media;
use Illuminate\Console\Command;

class CheckMediaManagerData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:check-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check Media Manager folders and media data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('=== Media Manager Data Check ===');

        // Check folders
        $this->line("\n--- Folders ---");
        $folders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->get();
        
        if ($folders->isEmpty()) {
            $this->warn('No folders found!');
        } else {
            $this->info("Found {$folders->count()} folders:");
            
            $this->table(
                ['ID', 'Name', 'Team ID', 'Parent ID', 'Collection', 'Is Personal'],
                $folders->map(function ($folder) {
                    return [
                        $folder->id,
                        $folder->name,
                        $folder->team_id ?? 'null',
                        $folder->parent_id ?? 'null',
                        $folder->collection ?? 'null',
                        $folder->is_personal ? 'Yes' : 'No',
                    ];
                })->toArray()
            );
        }

        // Check media
        $this->line("\n--- Media ---");
        $media = Media::withoutGlobalScopes(['team_media'])->get();
        
        if ($media->isEmpty()) {
            $this->warn('No media found!');
        } else {
            $this->info("Found {$media->count()} media files:");
            
            $this->table(
                ['ID', 'Name', 'Collection', 'Team ID', 'Model Type', 'Model ID'],
                $media->take(10)->map(function ($m) {
                    return [
                        $m->id,
                        $m->name,
                        $m->collection_name,
                        $m->team_id ?? 'null',
                        $m->model_type ?? 'null',
                        $m->model_id ?? 'null',
                    ];
                })->toArray()
            );
            
            if ($media->count() > 10) {
                $this->comment("... and " . ($media->count() - 10) . " more media files");
            }
        }

        // Check team-specific data
        $this->line("\n--- Team-Specific Data ---");
        $teams = \App\Models\Team::all();
        
        foreach ($teams as $team) {
            $teamFolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $team->id)
                ->count();
            $teamMedia = Media::withoutGlobalScopes(['team_media'])
                ->where('team_id', $team->id)
                ->count();
                
            $this->line("Team: {$team->name} (ID: {$team->id}) - Folders: {$teamFolders}, Media: {$teamMedia}");
        }

        // Check current user context
        $this->line("\n--- Current User Context ---");
        $user = auth()->user();
        if ($user) {
            $this->info("Authenticated as: {$user->name} (ID: {$user->id})");
            $this->info("User team_id: " . ($user->team_id ?? 'null'));
            $this->info("User roles: " . $user->roles->pluck('name')->join(', '));
        } else {
            $this->warn("No authenticated user");
        }

        // Check Filament tenant
        if (function_exists('filament') && filament()->hasTenancy()) {
            $tenant = filament()->getTenant();
            if ($tenant) {
                $this->info("Current tenant: {$tenant->name} (ID: {$tenant->id})");
            } else {
                $this->info("No current tenant");
            }
        } else {
            $this->info("Tenancy not available or not configured");
        }
    }
}
