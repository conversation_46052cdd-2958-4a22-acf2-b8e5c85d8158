# User-Level Media Management System

## Overview

The media manager now implements sophisticated user-level access control with personal folders and deleted user management. This system provides three levels of access:

1. **Super Admins**: Can see all media across all teams
2. **Team Admins**: Can see all media within their team
3. **Regular Users**: Can only see media they created or own

## Features Implemented

### 1. User-Level Access Control

#### Access Levels
- **Super Admin**: Full access to all media and folders across all teams
- **Team Admin**: Access to all media and folders within their team
- **Regular User**: Access only to their own media and personal folders

#### Personal Folders
- Each user automatically gets a personal folder named with their `user_id`
- Personal folders are created automatically when users are created
- Only the owner can see their personal folder (plus team/super admins)

### 2. Deleted User Management

#### Automatic Folder Renaming
- When a user is deleted, their personal folder is renamed to `{user_id}-deleted`
- Folder is marked with `is_deleted_user = true`
- Description is updated to indicate the user was deleted

#### Super Admin Cleanup Tools
- **Deleted User Folders Resource**: Special admin interface for managing deleted user folders
- **"Clear All Deleted User Folders" Button**: Bulk delete all folders from deleted users
- **Individual Cleanup**: Delete specific deleted user folders

### 3. Database Schema Updates

#### Media Table Additions
```sql
- created_by (foreign key to users)
- user_id (foreign key to users) 
```

#### Folders Table Additions
```sql
- created_by (foreign key to users)
- owner_id (foreign key to users)
- is_personal (boolean)
- is_deleted_user (boolean)
```

## How It Works

### User Access Filtering

#### Global Scopes
Both Media and Folder models use global scopes to automatically filter results:

1. **Team Filtering**: Applied first to limit to current team
2. **User Filtering**: Applied second based on user role:
   - Super Admin: No user filtering
   - Team Admin: No user filtering (sees all team content)
   - Regular User: Only sees their own content

#### Query Examples
```php
// Regular user sees only their media
$userMedia = Media::all(); // Automatically filtered

// Team admin sees all team media
$teamMedia = Media::all(); // Filtered by team only

// Super admin sees everything
$allMedia = Media::withoutGlobalScopes()->get();
```

### Personal Folder Management

#### Automatic Creation
```php
// Personal folders are created automatically via UserObserver
User::create([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'team_id' => 1,
]); // Personal folder "1" is created automatically
```

#### Folder Structure
```
Team 1 Folders:
├── 1 (John's personal folder)
├── 2 (Jane's personal folder)
├── Product Images (shared folder)
└── Documents (shared folder)
```

### Deleted User Handling

#### User Deletion Process
1. User is deleted from system
2. UserObserver triggers `deleting` event
3. Personal folders are renamed: `1` → `1-deleted`
4. Folders are marked as `is_deleted_user = true`
5. Descriptions are updated with deletion notice

#### Cleanup Process
Super admins can access the "Deleted User Folders" resource to:
- View all folders from deleted users
- See media count in each folder
- Delete individual folders
- Bulk delete all deleted user folders

## Usage Examples

### 1. Using Team-Aware Media Manager

```php
use App\Forms\Components\TeamMediaManagerInput;

// In your Filament resource form
TeamMediaManagerInput::make('attachments')
    ->label('File Attachments')
    ->disk('public')
    ->teamAware() // Enables user-level filtering
    ->schema([
        Forms\Components\TextInput::make('title')
            ->required(),
        Forms\Components\Textarea::make('description'),
    ]),
```

### 2. Direct Model Usage

```php
use App\Models\MediaManager\Media;
use App\Models\MediaManager\Folder;

// Get current user's media (automatically filtered)
$myMedia = Media::all();

// Get current user's folders (automatically filtered)  
$myFolders = Folder::all();

// Super admin: Get all media without filtering
$allMedia = Media::withoutGlobalScopes(['team_media', 'user_media'])->get();

// Get personal folders only
$personalFolders = Folder::personal()->get();

// Get deleted user folders (super admin only)
$deletedFolders = Folder::deletedUsers()->get();
```

### 3. Creating Personal Content

```php
// Media is automatically assigned to current user
$media = Media::create([
    'name' => 'My Document',
    'file_name' => 'document.pdf',
    // created_by and user_id are set automatically
    // team_id is set automatically from user's team
]);

// Create a personal folder
$folder = Folder::createPersonalFolder($userId, $teamId);
```

## Admin Interface

### Deleted User Folders Resource

Located at `/backend/deleted-user-folders` (super admin only):

#### Features
- **List View**: Shows all folders from deleted users
- **Columns**: Folder name, team, original owner, media count, dates
- **Filters**: Filter by team
- **Actions**: View, edit, delete individual folders
- **Bulk Actions**: Delete multiple folders at once
- **Header Action**: "Clear All Deleted User Folders" button

#### Permissions
- Only accessible to users with `super_admin` role
- Bypasses all global scopes to show deleted user content

## Security Features

### 1. Multi-Layer Filtering
- **Database Level**: Global scopes prevent accidental data leakage
- **Component Level**: Form components respect user permissions
- **UI Level**: Admin interfaces check user roles

### 2. Automatic Assignment
- **Team Assignment**: New media automatically gets user's team_id
- **User Assignment**: New media automatically gets created_by and user_id
- **Folder Ownership**: Personal folders automatically set owner_id

### 3. Role-Based Access
- **Super Admin**: Can bypass all filtering when needed
- **Team Admin**: Sees all team content but respects team boundaries
- **Regular User**: Strict isolation to own content only

## Migration and Setup

### Running the Setup

```bash
# Run migrations to add user fields
php artisan migrate

# Create personal folders for existing users
php artisan db:seed --class=PersonalFoldersSeeder

# Clear caches
php artisan config:clear
```

### Updating Existing Media

If you have existing media that needs user assignment:

```php
use App\Models\MediaManager\Media;
use App\Models\User;

// Assign existing media to random users in same team
Media::whereNull('created_by')->chunk(100, function ($mediaItems) {
    foreach ($mediaItems as $media) {
        if ($media->team_id) {
            $randomUser = User::where('team_id', $media->team_id)->inRandomOrder()->first();
            if ($randomUser) {
                $media->update([
                    'created_by' => $randomUser->id,
                    'user_id' => $randomUser->id,
                ]);
            }
        }
    }
});
```

## Troubleshooting

### Common Issues

1. **Users can't see any media**
   - Check if user has correct team_id assigned
   - Verify user role permissions
   - Ensure media has correct created_by/user_id values

2. **Personal folders not created**
   - Run PersonalFoldersSeeder for existing users
   - Check UserObserver is registered in AppServiceProvider
   - Verify user has team_id when created

3. **Deleted user folders not showing**
   - Ensure user has super_admin role
   - Check if folders are properly marked as is_deleted_user
   - Verify UserObserver is working on user deletion

### Debug Commands

```bash
# Check user roles
php artisan tinker
>>> User::with('roles')->find(1)

# Check personal folders
>>> Folder::personal()->get()

# Check deleted user folders  
>>> Folder::deletedUsers()->get()

# Check media ownership
>>> Media::with(['creator', 'owner'])->get()
```
