<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CourseResource\Pages;
use App\Models\Course;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CourseResource extends Resource
{
    protected static ?string $model = Course::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 5;

    protected static ?string $navigationLabel = 'Courses';

    protected static ?string $modelLabel = 'Course';

    protected static ?string $pluralModelLabel = 'Courses';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'courses';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('instructor')
                            ->maxLength(255)
                            ->placeholder('Instructor name')
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->placeholder('Course description and overview')
                            ->columnSpanFull(),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Course Details')
                    ->schema([
                        Forms\Components\Select::make('type')
                            ->options([
                                'online' => 'Online',
                                'onsite' => 'On-site',
                                'hybrid' => 'Hybrid',
                            ])
                            ->default('online')
                            ->required()
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('difficulty_level')
                            ->label('Difficulty Level')
                            ->options([
                                'beginner' => 'Beginner',
                                'intermediate' => 'Intermediate',
                                'advanced' => 'Advanced',
                            ])
                            ->default('beginner')
                            ->required()
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('duration_hours')
                            ->label('Duration (hours)')
                            ->numeric()
                            ->minValue(0.5)
                            ->step(0.5)
                            ->placeholder('Total course duration')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('max_students')
                            ->label('Max Students')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(1000)
                            ->placeholder('Maximum enrollment')
                            ->columnSpan(1),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Pricing')
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->numeric()
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('0.00 for free course')
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('currency')
                            ->options([
                                'USD' => 'USD ($)',
                                'EUR' => 'EUR (€)',
                                'GBP' => 'GBP (£)',
                                'THB' => 'THB (฿)',
                                'JPY' => 'JPY (¥)',
                                'CNY' => 'CNY (¥)',
                            ])
                            ->default('USD')
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Schedule')
                    ->schema([
                        Forms\Components\DatePicker::make('start_date')
                            ->label('Start Date')
                            ->columnSpan(1),
                        
                        Forms\Components\DatePicker::make('end_date')
                            ->label('End Date')
                            ->after('start_date')
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Textarea::make('objectives')
                            ->label('Learning Objectives')
                            ->rows(3)
                            ->placeholder('What students will learn from this course')
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('requirements')
                            ->label('Prerequisites')
                            ->rows(3)
                            ->placeholder('Course requirements and prerequisites')
                            ->columnSpan(1),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->columnSpan(1),
                        
                        Forms\Components\Toggle::make('is_featured')
                            ->label('Featured')
                            ->default(false)
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Sort Order')
                            ->numeric()
                            ->default(0)
                            ->columnSpan(1),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->badge()
                    ->color('primary')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'online' => 'success',
                        'onsite' => 'warning',
                        'hybrid' => 'info',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('difficulty_level')
                    ->label('Level')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'beginner' => 'success',
                        'intermediate' => 'warning',
                        'advanced' => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('formatted_price')
                    ->label('Price')
                    ->sortable(['price']),
                
                Tables\Columns\TextColumn::make('instructor')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('lessons_count')
                    ->label('Lessons')
                    ->counts('lessons')
                    ->badge()
                    ->color('info')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'upcoming' => 'info',
                        'ongoing' => 'success',
                        'completed' => 'gray',
                        'scheduled' => 'warning',
                        default => 'gray',
                    }),
                
                Tables\Columns\IconColumn::make('is_featured')
                    ->label('Featured')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    }),
                
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'online' => 'Online',
                        'onsite' => 'On-site',
                        'hybrid' => 'Hybrid',
                    ]),
                
                Tables\Filters\SelectFilter::make('difficulty_level')
                    ->label('Difficulty Level')
                    ->options([
                        'beginner' => 'Beginner',
                        'intermediate' => 'Intermediate',
                        'advanced' => 'Advanced',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('Featured'),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            CourseResource\RelationManagers\LessonsRelationManager::class,
            CourseResource\RelationManagers\LiveVideosRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourses::route('/'),
            'create' => Pages\CreateCourse::route('/create'),
            'view' => Pages\ViewCourse::route('/{record}'),
            'edit' => Pages\EditCourse::route('/{record}/edit'),
        ];
    }
}
