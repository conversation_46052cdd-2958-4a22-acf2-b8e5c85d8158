<?php

namespace App\Filament\Resources\Blog\PostResource\Pages;

use App\Filament\Resources\Blog\PostResource;
use App\Models\Blog\Author;
use App\Models\Blog\Category;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Str;

class CreatePost extends CreateRecord
{
    protected static string $resource = PostResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set the type from URL parameter if available
        if ($type = request()->get('type')) {
            $data['type'] = $type;
        }

        // Set team_id from current tenant
        $data['team_id'] = \Filament\Facades\Filament::getTenant()?->id;

        return $data;
    }

    public function getTitle(): string
    {
        $type = request()->get('type');
        if ($type) {
            $config = config("post-types.types.{$type}", []);
            return 'Create ' . ($config['name'] ?? ucfirst($type)) . ' Post';
        }

        return 'Create Post';
    }

    protected function getRedirectUrl(): string
    {
        $type = request()->get('type');
        return static::getResource()::getUrl('index', $type ? ['type' => $type] : []);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('bulkCreateAuthors')
                ->label('Quick Create Authors')
                ->icon('heroicon-o-users')
                ->color('info')
                ->form([
                    \Filament\Forms\Components\Textarea::make('author_data')
                        ->label('Author Data')
                        ->placeholder('Enter author data, one per line in format: Name <<EMAIL>>' . "\n" . 'John Doe <<EMAIL>>' . "\n" . 'Jane Smith <<EMAIL>>')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one author per line in format: Name <<EMAIL>>'),
                ])
                ->action(function (array $data) {
                    $lines = array_filter(array_map('trim', explode("\n", $data['author_data'])));
                    $created = 0;

                    foreach ($lines as $line) {
                        if (!empty($line) && preg_match('/^(.+?)\s*<(.+?)>$/', $line, $matches)) {
                            $name = trim($matches[1]);
                            $email = trim($matches[2]);

                            if (!Author::where('email', $email)->exists()) {
                                Author::create([
                                    'name' => $name,
                                    'email' => $email,
                                    'bio' => null,
                                    'github_handle' => null,
                                    'twitter_handle' => null,
                                    'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                ]);
                                $created++;
                            }
                        }
                    }

                    Notification::make()
                        ->title("Created {$created} authors successfully!")
                        ->success()
                        ->send();
                }),

            Actions\Action::make('bulkCreateCategories')
                ->label('Quick Create Categories')
                ->icon('heroicon-o-folder')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Textarea::make('category_names')
                        ->label('Category Names')
                        ->placeholder('Enter category names, one per line:' . "\n" . 'Technology' . "\n" . 'Business' . "\n" . 'Lifestyle')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one category name per line. Categories will be created automatically.'),

                    \Filament\Forms\Components\Toggle::make('make_visible')
                        ->label('Make all categories visible')
                        ->default(true),
                ])
                ->action(function (array $data) {
                    $names = array_filter(array_map('trim', explode("\n", $data['category_names'])));
                    $created = 0;

                    foreach ($names as $name) {
                        if (!empty($name)) {
                            $type = request()->get('type', config('post-types.default_type', 'blog'));
                            Category::create([
                                'name' => $name,
                                'slug' => Str::slug($name . '-' . $type . '-' . \Filament\Facades\Filament::getTenant()?->slug),
                                'description' => null,
                                'is_visible' => $data['make_visible'] ?? true,
                                'type' => $type,
                                'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                            ]);
                            $created++;
                        }
                    }

                    Notification::make()
                        ->title("Created {$created} categories successfully!")
                        ->success()
                        ->send();
                }),
        ];
    }
}
