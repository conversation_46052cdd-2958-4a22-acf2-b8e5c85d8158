<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Models\MediaManager\Media;

class LiveVideo extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'team_id',
        'user_id',
        'liveable_id',
        'liveable_type',
        'title',
        'description',
        'stream_key',
        'stream_url',
        'watch_url',
        'stream_settings',
        'scheduled_start_time',
        'scheduled_end_time',
        'actual_start_time',
        'actual_end_time',
        'status',
        'is_recording_enabled',
        'recording_path',
        'recorded_media_id',
        'is_public',
        'allowed_roles',
        'access_password',
        'max_viewers',
        'total_views',
        'viewer_analytics',
        'metadata',
        'is_active',
    ];

    protected $casts = [
        'scheduled_start_time' => 'datetime',
        'scheduled_end_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'is_recording_enabled' => 'boolean',
        'is_public' => 'boolean',
        'is_active' => 'boolean',
        'stream_settings' => 'array',
        'allowed_roles' => 'array',
        'viewer_analytics' => 'array',
        'metadata' => 'array',
        'max_viewers' => 'integer',
        'total_views' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($liveVideo) {
            if (empty($liveVideo->stream_key)) {
                $liveVideo->stream_key = 'live_' . Str::random(32);
            }
        });
    }

    /**
     * Get the team that owns the live video
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user (teacher) who created the live video
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the teacher (alias for user)
     */
    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the parent model (course or book) that this live video belongs to
     */
    public function liveable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the recorded media file
     */
    public function recordedMedia(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'recorded_media_id');
    }

    /**
     * Get the course that this live video belongs to (if it belongs to a course)
     */
    public function course()
    {
        return $this->liveable_type === Course::class ? $this->liveable : null;
    }

    /**
     * Get the book that this live video belongs to (if it belongs to a book)
     */
    public function book()
    {
        return $this->liveable_type === Book::class ? $this->liveable : null;
    }

    /**
     * Get the subject through the parent relationship
     */
    public function subject()
    {
        return $this->liveable?->subject;
    }

    /**
     * Scope a query to only include active live videos
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by status
     */
    public function scopeStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include live videos for a specific team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope a query to only include scheduled live videos
     */
    public function scopeScheduled(Builder $query): Builder
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope a query to only include currently live videos
     */
    public function scopeLive(Builder $query): Builder
    {
        return $query->where('status', 'live');
    }

    /**
     * Check if the live video is currently live
     */
    public function isLive(): bool
    {
        return $this->status === 'live';
    }

    /**
     * Check if the live video is scheduled
     */
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    /**
     * Check if the live video has ended
     */
    public function hasEnded(): bool
    {
        return $this->status === 'ended';
    }

    /**
     * Start the live video
     */
    public function start(): void
    {
        $this->update([
            'status' => 'live',
            'actual_start_time' => now(),
        ]);
    }

    /**
     * End the live video
     */
    public function end(): void
    {
        $this->update([
            'status' => 'ended',
            'actual_end_time' => now(),
        ]);
    }

    /**
     * Get the duration in minutes
     */
    public function getDurationAttribute(): ?int
    {
        if ($this->actual_start_time && $this->actual_end_time) {
            return $this->actual_start_time->diffInMinutes($this->actual_end_time);
        }

        if ($this->scheduled_start_time && $this->scheduled_end_time) {
            return $this->scheduled_start_time->diffInMinutes($this->scheduled_end_time);
        }

        return null;
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('recordings')
            ->acceptsMimeTypes(['video/mp4', 'video/webm', 'video/avi', 'video/mov']);

        $this->addMediaCollection('thumbnails')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }
}
