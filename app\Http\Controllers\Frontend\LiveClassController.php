<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\LiveVideo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LiveClassController extends Controller
{
    /**
     * Display a listing of live classes for the authenticated user
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Get live videos based on user's team and role
        $query = LiveVideo::with(['teacher', 'liveable'])
            ->where('team_id', $user->team_id)
            ->where('is_active', true);

        // Filter by status if requested
        $status = $request->get('status', 'all');
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Filter by access permissions
        $query->where(function ($q) use ($user) {
            $q->where('is_public', true)
              ->orWhere('user_id', $user->id) // User is the teacher
              ->orWhereJsonContains('allowed_roles', $user->getRoleNames()->toArray());
        });

        $liveVideos = $query->orderBy('scheduled_start_time', 'desc')
                           ->paginate(12);

        return view('frontend.live-classes.index', compact('liveVideos', 'status'));
    }

    /**
     * Show a specific live class
     */
    public function show(LiveVideo $liveVideo)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has permission to view this live class
        if (!$this->canViewLiveClass($liveVideo, $user)) {
            abort(403, 'You do not have permission to view this live class.');
        }

        return view('frontend.live-classes.show', compact('liveVideo'));
    }

    /**
     * Join a live class (redirect to watch page)
     */
    public function join(LiveVideo $liveVideo)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has permission to join this live class
        if (!$this->canViewLiveClass($liveVideo, $user)) {
            abort(403, 'You do not have permission to join this live class.');
        }

        // Check if the class is live or scheduled
        if ($liveVideo->status === 'ended') {
            return redirect()->route('frontend.live-classes.show', $liveVideo)
                           ->with('error', 'This live class has ended.');
        }

        if ($liveVideo->status === 'cancelled') {
            return redirect()->route('frontend.live-classes.show', $liveVideo)
                           ->with('error', 'This live class has been cancelled.');
        }

        // Redirect to the watch page
        return redirect()->route('live-videos.watch', $liveVideo);
    }

    /**
     * Display upcoming live classes
     */
    public function upcoming()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $upcomingClasses = LiveVideo::with(['teacher', 'liveable'])
            ->where('team_id', $user->team_id)
            ->where('status', 'scheduled')
            ->where('scheduled_start_time', '>', now())
            ->where('is_active', true)
            ->where(function ($q) use ($user) {
                $q->where('is_public', true)
                  ->orWhere('user_id', $user->id)
                  ->orWhereJsonContains('allowed_roles', $user->getRoleNames()->toArray());
            })
            ->orderBy('scheduled_start_time', 'asc')
            ->limit(10)
            ->get();

        return view('frontend.live-classes.upcoming', compact('upcomingClasses'));
    }

    /**
     * Display currently live classes
     */
    public function live()
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        $liveClasses = LiveVideo::with(['teacher', 'liveable'])
            ->where('team_id', $user->team_id)
            ->where('status', 'live')
            ->where('is_active', true)
            ->where(function ($q) use ($user) {
                $q->where('is_public', true)
                  ->orWhere('user_id', $user->id)
                  ->orWhereJsonContains('allowed_roles', $user->getRoleNames()->toArray());
            })
            ->orderBy('actual_start_time', 'desc')
            ->get();

        return view('frontend.live-classes.live', compact('liveClasses'));
    }

    /**
     * Check if user can view a live class
     */
    private function canViewLiveClass(LiveVideo $liveVideo, $user): bool
    {
        // Check if user is in the same team
        if ($user->team_id !== $liveVideo->team_id) {
            return false;
        }

        // Public classes can be viewed by anyone in the team
        if ($liveVideo->is_public) {
            return true;
        }

        // Teacher can always view their own classes
        if ($liveVideo->user_id === $user->id) {
            return true;
        }

        // Check if user has allowed role
        if ($liveVideo->allowed_roles && $user->hasAnyRole($liveVideo->allowed_roles)) {
            return true;
        }

        return false;
    }
}
