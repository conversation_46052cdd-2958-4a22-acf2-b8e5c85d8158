<?php

namespace App\Models\MediaManager;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Builder;
use TomatoPHP\FilamentMediaManager\Models\Folder as BaseFolder;

/**
 * 
 *
 * @property int $id
 * @property int|null $parent_id
 * @property int|null $team_id
 * @property int|null $created_by
 * @property int|null $owner_id
 * @property bool $is_personal
 * @property bool $is_deleted_user
 * @property string|null $model_type
 * @property int|null $model_id
 * @property string $name
 * @property string|null $collection
 * @property string|null $description
 * @property string|null $icon
 * @property string|null $color
 * @property bool|null $is_protected
 * @property string|null $password
 * @property bool|null $is_hidden
 * @property bool|null $is_favorite
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property bool|null $is_public
 * @property bool|null $has_user_access
 * @property int|null $user_id
 * @property string|null $user_type
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Folder> $children
 * @property-read int|null $children_count
 * @property-read \App\Models\User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, BaseFolder> $folders
 * @property-read int|null $folders_count
 * @property-read mixed $team_media
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \App\Models\MediaManager\Media> $media
 * @property-read int|null $media_count
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent|null $model
 * @property-read \App\Models\User|null $owner
 * @property-read Folder|null $parent
 * @property-read \App\Models\Team|null $team
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent|null $user
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 * @method static Builder<static>|Folder deletedUsers()
 * @method static Builder<static>|Folder forCurrentTeam()
 * @method static Builder<static>|Folder forTeam($teamId)
 * @method static Builder<static>|Folder forUser($userId)
 * @method static Builder<static>|Folder newModelQuery()
 * @method static Builder<static>|Folder newQuery()
 * @method static Builder<static>|Folder personal()
 * @method static Builder<static>|Folder query()
 * @method static Builder<static>|Folder whereCollection($value)
 * @method static Builder<static>|Folder whereColor($value)
 * @method static Builder<static>|Folder whereCreatedAt($value)
 * @method static Builder<static>|Folder whereCreatedBy($value)
 * @method static Builder<static>|Folder whereDescription($value)
 * @method static Builder<static>|Folder whereHasUserAccess($value)
 * @method static Builder<static>|Folder whereIcon($value)
 * @method static Builder<static>|Folder whereId($value)
 * @method static Builder<static>|Folder whereIsDeletedUser($value)
 * @method static Builder<static>|Folder whereIsFavorite($value)
 * @method static Builder<static>|Folder whereIsHidden($value)
 * @method static Builder<static>|Folder whereIsPersonal($value)
 * @method static Builder<static>|Folder whereIsProtected($value)
 * @method static Builder<static>|Folder whereIsPublic($value)
 * @method static Builder<static>|Folder whereModelId($value)
 * @method static Builder<static>|Folder whereModelType($value)
 * @method static Builder<static>|Folder whereName($value)
 * @method static Builder<static>|Folder whereOwnerId($value)
 * @method static Builder<static>|Folder whereParentId($value)
 * @method static Builder<static>|Folder wherePassword($value)
 * @method static Builder<static>|Folder whereTeamId($value)
 * @method static Builder<static>|Folder whereUpdatedAt($value)
 * @method static Builder<static>|Folder whereUserId($value)
 * @method static Builder<static>|Folder whereUserType($value)
 * @mixin \Eloquent
 */
class Folder extends BaseFolder
{
    use BelongsToTeam;

    protected $fillable = [
        'parent_id',
        'model_type',
        'model_id',
        'name',
        'collection',
        'description',
        'icon',
        'color',
        'is_protected',
        'password',
        'is_hidden',
        'is_favorite',
        'is_public',
        'has_user_access',
        'user_id',
        'user_type',
        'team_id',
        'created_by',
        'owner_id',
        'is_personal',
        'is_deleted_user',
    ];

    protected $casts = [
        'is_protected' => 'boolean',
        'is_hidden' => 'boolean',
        'is_favorite' => 'boolean',
        'is_public' => 'boolean',
        'has_user_access' => 'boolean',
        'is_personal' => 'boolean',
        'is_deleted_user' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        // Team-based filtering
        static::addGlobalScope('team_folder', function (Builder $query) {
            $user = auth()->user();

            // Skip team filtering if we're in media manager context
            if (request()->routeIs('filament.*.resources.folders.*') ||
                request()->routeIs('filament.*.resources.media.*') ||
                request()->has('folder_id') ||
                request()->has('model_type')) {
                return;
            }

            // Skip team filtering for super admins - they should see all folders
            if ($user && $user->hasRole('super_admin')) {
                return;
            }

            // Apply team filtering based on current tenant or user's team
            if (filament()->hasTenancy() && filament()->getTenant()) {
                $query->where('team_id', filament()->getTenant()->id);
            } elseif ($user && $user->team_id) {
                $query->where('team_id', $user->team_id);
            }
        });

        // User-level filtering - TEMPORARILY DISABLED FOR TESTING
        // static::addGlobalScope('user_folder', function (Builder $query) {
        //     $user = auth()->user();
        //
        //     if (!$user) {
        //         return;
        //     }

        //     // Skip user filtering if we're in media manager context
        //     if (request()->routeIs('filament.*.resources.media.*') ||
        //         request()->routeIs('filament.*.resources.folders.*') ||
        //         request()->has('folder_id')) {
        //         return;
        //     }

        //     // Super admins can see all folders (no filtering)
        //     if ($user->hasRole('super_admin')) {
        //         return;
        //     }

        //     // Team admins can see all folders in their team
        //     if ($user->hasRole('team_admin')) {
        //         return; // Team filtering already applied above
        //     }

        //     // Regular users can only see:
        //     // 1. Their own personal folders
        //     // 2. Public folders
        //     // 3. Folders they created
        //     // 4. Folders they own
        //     $query->where(function ($query) use ($user) {
        //         $query->where('is_personal', true)
        //               ->where('owner_id', $user->id)
        //               ->orWhere('is_public', true)
        //               ->orWhere('created_by', $user->id)
        //               ->orWhere('owner_id', $user->id);
        //     });
        // });

        // Auto-set user fields when creating folders
        static::creating(function (Folder $folder) {
            $user = auth()->user();

            if ($user) {
                if (!$folder->created_by) {
                    $folder->created_by = $user->id;
                }

                if (!$folder->owner_id && $folder->is_personal) {
                    $folder->owner_id = $user->id;
                }

                if (!$folder->team_id && $user->team_id) {
                    $folder->team_id = $user->team_id;
                }
            }
        });
    }

    /**
     * Scope to filter folders by team
     */
    public function scopeForTeam(Builder $query, $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope to filter folders by current team
     */
    public function scopeForCurrentTeam(Builder $query): Builder
    {
        if (filament()->hasTenancy() && filament()->getTenant()) {
            return $query->where('team_id', filament()->getTenant()->id);
        }

        $user = auth()->user();
        if ($user && $user->team_id) {
            return $query->where('team_id', $user->team_id);
        }

        return $query;
    }

    /**
     * Get the user who created this folder
     */
    public function creator()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get the user who owns this folder
     */
    public function owner()
    {
        return $this->belongsTo(\App\Models\User::class, 'owner_id');
    }

    /**
     * Get the parent folder
     */
    public function parent()
    {
        return $this->belongsTo(static::class, 'parent_id');
    }

    /**
     * Get the child folders
     */
    public function children()
    {
        return $this->hasMany(static::class, 'parent_id');
    }

    /**
     * Get media files for this folder filtered by team
     */
    public function getTeamMediaAttribute()
    {
        return Media::forCurrentTeam()
            ->forFolder($this->id)
            ->get();
    }

    /**
     * Create a personal folder for a user
     */
    public static function createPersonalFolder($userId, $teamId = null)
    {
        $user = \App\Models\User::find($userId);
        if (!$user) {
            return null;
        }

        $teamId = $teamId ?: $user->team_id;

        return static::create([
            'name' => (string) $userId,
            'collection' => "user_{$userId}_personal",
            'description' => "Personal folder for {$user->name}",
            'icon' => 'heroicon-o-user',
            'color' => '#6B7280',
            'is_personal' => true,
            'is_public' => false,
            'team_id' => $teamId,
            'created_by' => $userId,
            'owner_id' => $userId,
        ]);
    }

    /**
     * Mark folder as belonging to deleted user
     */
    public function markAsDeletedUser()
    {
        $this->update([
            'name' => $this->name . '-deleted',
            'is_deleted_user' => true,
            'description' => ($this->description ?: '') . ' [User deleted]',
        ]);
    }

    /**
     * Scope to get folders of deleted users
     */
    public function scopeDeletedUsers(Builder $query): Builder
    {
        return $query->where('is_deleted_user', true);
    }

    /**
     * Scope to get personal folders
     */
    public function scopePersonal(Builder $query): Builder
    {
        return $query->where('is_personal', true);
    }

    /**
     * Scope to filter folders by user
     */
    public function scopeForUser(Builder $query, $userId): Builder
    {
        return $query->where(function ($query) use ($userId) {
            $query->where('created_by', $userId)
                  ->orWhere('owner_id', $userId);
        });
    }
}
