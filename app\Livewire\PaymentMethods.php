<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use TomatoPHP\FilamentPayments\Models\Payment;
use TomatoPHP\FilamentPayments\Models\PaymentMethod;
use Filament\Notifications\Notification;

class PaymentMethods extends Component
{
    public $paymentMethods;
    public $paymentHistory;
    public $showAddPaymentMethod = false;

    public function mount()
    {
        $user = Auth::user();
        $this->paymentMethods = $user->paymentMethods ?? collect();
        $this->paymentHistory = Payment::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    public function addPaymentMethod()
    {
        $this->showAddPaymentMethod = true;
    }

    public function removePaymentMethod($methodId)
    {
        // Implementation depends on payment gateway
        Notification::make()
            ->title('Payment method removed')
            ->success()
            ->send();

        $this->mount(); // Refresh data
    }

    public function setDefaultPaymentMethod($methodId)
    {
        // Implementation depends on payment gateway
        Notification::make()
            ->title('Default payment method updated')
            ->success()
            ->send();

        $this->mount(); // Refresh data
    }

    public function downloadInvoice($paymentId)
    {
        $payment = Payment::find($paymentId);

        if ($payment && $payment->user_id === Auth::id()) {
            // Generate and download invoice
            return response()->download($payment->invoice_path ?? '');
        }
    }

    public function render()
    {
        return view('livewire.payment-methods', [
            'paymentMethods' => $this->paymentMethods,
            'paymentHistory' => $this->paymentHistory,
        ]);
    }
}
