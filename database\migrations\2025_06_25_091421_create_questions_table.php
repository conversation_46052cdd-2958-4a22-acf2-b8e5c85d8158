<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->text('question_text');
            $table->enum('question_type', ['multiple_choice', 'short_answer', 'essay'])->default('multiple_choice');
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // teacher
            $table->foreignId('subject_id')->nullable()->constrained('subjects')->nullOnDelete();
            $table->float('points')->default(1); // คะแนนของข้อนี้
            $table->text('explanation')->nullable(); // คำอธิบายเฉลย
            $table->string('difficulty_level')->default('medium'); // easy, medium, hard
            $table->json('tags')->nullable(); // แท็กสำหรับจัดหมวดหมู่
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['team_id', 'question_type']);
            $table->index(['user_id', 'subject_id']);
            $table->index(['is_active', 'difficulty_level']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
