<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create roles
        $roles = [
            'super_admin' => 'Super Administrator',
            'school' => 'School Administrator',
            'teacher' => 'Teacher',
            'parent' => 'Parent',
            'student' => 'Student'
        ];

        foreach ($roles as $name => $displayName) {
            Role::firstOrCreate(
                ['name' => $name],
                ['guard_name' => 'web']
            );
        }

        // Create permissions
        $permissions = [
            // User management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',

            // Student management
            'view_students',
            'create_students',
            'edit_students',
            'delete_students',

            // Teacher management
            'view_teachers',
            'create_teachers',
            'edit_teachers',
            'delete_teachers',

            // Parent management
            'view_parents',
            'create_parents',
            'edit_parents',
            'delete_parents',

            // School management
            'view_schools',
            'create_schools',
            'edit_schools',
            'delete_schools',

            // Course management
            'view_courses',
            'create_courses',
            'edit_courses',
            'delete_courses',

            // Class management
            'view_classes',
            'create_classes',
            'edit_classes',
            'delete_classes',

            // Assignment management
            'view_assignments',
            'create_assignments',
            'edit_assignments',
            'delete_assignments',
            'submit_assignments',
            'grade_assignments',

            // Grade management
            'view_grades',
            'create_grades',
            'edit_grades',
            'delete_grades',
            'view_own_grades',
            'view_child_grades',

            // Attendance management
            'view_attendance',
            'create_attendance',
            'edit_attendance',
            'delete_attendance',
            'view_own_attendance',
            'view_child_attendance',

            // Report management
            'view_reports',
            'create_reports',
            'edit_reports',
            'delete_reports',
            'view_own_reports',
            'view_child_reports',

            // Communication
            'send_messages',
            'view_messages',
            'delete_messages',

            // Profile management
            'view_own_profile',
            'edit_own_profile',
            'view_profiles',
            'edit_profiles',

            // Dashboard access
            'access_admin_dashboard',
            'access_teacher_dashboard',
            'access_parent_dashboard',
            'access_student_dashboard',
            'access_school_dashboard',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission],
                ['guard_name' => 'web']
            );
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();
    }

    private function assignPermissionsToRoles()
    {
        // Super Admin - All permissions
        $superAdmin = Role::findByName('super_admin');
        $superAdmin->givePermissionTo(Permission::all());

        // School Admin - School management and oversight
        $school = Role::findByName('school');
        $school->givePermissionTo([
            'view_users', 'create_users', 'edit_users',
            'view_students', 'create_students', 'edit_students',
            'view_teachers', 'create_teachers', 'edit_teachers',
            'view_parents', 'create_parents', 'edit_parents',
            'view_courses', 'create_courses', 'edit_courses',
            'view_classes', 'create_classes', 'edit_classes',
            'view_assignments', 'view_grades', 'view_attendance',
            'view_reports', 'create_reports',
            'send_messages', 'view_messages',
            'view_profiles', 'edit_profiles',
            'view_own_profile', 'edit_own_profile',
            'access_admin_dashboard', 'access_school_dashboard'
        ]);

        // Teacher - Teaching and student management
        $teacher = Role::findByName('teacher');
        $teacher->givePermissionTo([
            'view_students', 'view_parents',
            'view_courses', 'view_classes',
            'view_assignments', 'create_assignments', 'edit_assignments', 'grade_assignments',
            'view_grades', 'create_grades', 'edit_grades',
            'view_attendance', 'create_attendance', 'edit_attendance',
            'view_reports', 'create_reports',
            'send_messages', 'view_messages',
            'view_own_profile', 'edit_own_profile',
            'access_teacher_dashboard'
        ]);

        // Parent - View child information
        $parent = Role::findByName('parent');
        $parent->givePermissionTo([
            'view_child_grades', 'view_child_attendance', 'view_child_reports',
            'send_messages', 'view_messages',
            'view_own_profile', 'edit_own_profile',
            'access_parent_dashboard'
        ]);

        // Student - View own information
        $student = Role::findByName('student');
        $student->givePermissionTo([
            'view_assignments', 'submit_assignments',
            'view_own_grades', 'view_own_attendance', 'view_own_reports',
            'send_messages', 'view_messages',
            'view_own_profile', 'edit_own_profile',
            'access_student_dashboard'
        ]);
    }
}
