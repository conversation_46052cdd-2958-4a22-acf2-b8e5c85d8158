<?php

namespace App\Filament\Resources\MediaManager;

use Illuminate\Support\Str;
use TomatoPHP\FilamentIcons\Components\IconPicker;
use App\Filament\Resources\MediaManager\FolderResource\Pages;
use TomatoPHP\FilamentMediaManager\Resources\FolderResource\RelationManagers;
use TomatoPHP\FilamentMediaManager\Models\Folder;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FolderResource extends Resource
{

    protected static bool $isScopedToTenant = false;


    protected static ?string $navigationIcon = 'heroicon-o-folder';


    public static function getModel(): string
    {
        return config('filament-media-manager.model.folder'); // TODO: Change the autogenerated stub
    }

    public static function getNavigationLabel(): string
    {
        return trans('filament-media-manager::messages.folders.title');
    }

    public static function getPluralLabel(): ?string
    {
        if(request()->has('model_type') && !request()->has('collection')){
            return str(request()->get('model_type'))->afterLast('\\')->title();
        }
        else if(request()->has('model_type') && request()->has('collection')){
            return str(request()->get('collection'))->title();
        }
        else {
            return trans('filament-media-manager::messages.folders.title');
        }
    }

    public static function getLabel(): ?string
    {
        return trans('filament-media-manager::messages.folders.single'); // TODO: Change the autogenerated stub
    }

    public static function getNavigationGroup(): ?string
    {
        return trans('filament-media-manager::messages.folders.group'); // TODO: Change the autogenerated stub
    }
    
    public static function getNavigationSort(): ?int
    {
        return config('filament-media-manager.navigation_sort', null);
    }
    
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')->visible(config('filament-media-manager.allow_user_access', false))->default(auth()->id()),
                Forms\Components\Hidden::make('user_type')->visible(config('filament-media-manager.allow_user_access', false))->default(get_class(auth()->user())),
                Forms\Components\TextInput::make('name')
                    ->label(trans('filament-media-manager::messages.folders.columns.name'))
                    ->columnSpanFull()
                    ->lazy()
                    ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                        $set('collection', Str::slug($get('name')));
                    })
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('collection')
                    ->label(trans('filament-media-manager::messages.folders.columns.collection'))
                    ->columnSpanFull()
                    ->unique()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('description')
                    ->label(trans('filament-media-manager::messages.folders.columns.description'))
                    ->columnSpanFull()
                    ->maxLength(255),
                IconPicker::make('icon')
                    ->label(trans('filament-media-manager::messages.folders.columns.icon')),
                Forms\Components\ColorPicker::make('color')
                    ->label(trans('filament-media-manager::messages.folders.columns.color')),
                Forms\Components\Toggle::make('is_protected')
                    ->label(trans('filament-media-manager::messages.folders.columns.is_protected'))
                    ->live()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('password')
                    ->label(trans('filament-media-manager::messages.folders.columns.password'))
                    ->hidden(fn(Forms\Get $get) => !$get('is_protected'))
                    ->password()
                    ->revealable()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('password_confirmation')
                    ->label(trans('filament-media-manager::messages.folders.columns.password_confirmation'))
                    ->hidden(fn(Forms\Get $get) => !$get('is_protected'))
                    ->password()
                    ->required()
                    ->revealable()
                    ->maxLength(255)
            ])->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                if(request()->has('model_type') && !request()->has('collection')){
                    $query->where('model_type', request()->get('model_type'))
                        ->where('model_id', null)
                        ->whereNotNull('collection');
                }
                else if(request()->has('model_type') && request()->has('collection')){
                    $query->where('model_type', request()->get('model_type'))
                        ->whereNotNull('model_id')
                        ->where('collection', request()->get('collection'));
                }
                else {
                    // Show main folders (either no model_type/model_id, or team folders)
                    $query->where(function ($query) {
                        $query->where(function ($subQuery) {
                            // Original condition for non-team folders
                            $subQuery->where('model_id', null)
                                ->where(function ($collectionQuery) {
                                    $collectionQuery->where('collection', null)
                                        ->orWhere('model_type', null);
                                });
                        })->orWhere(function ($subQuery) {
                            // Team folders condition
                            $subQuery->where('model_id', null)
                                ->where('model_type', null)
                                ->whereNotNull('collection');
                        });
                    });
                }
            })
            ->content(function () {
                return view('filament-media-manager::pages.folders');
            })
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\TextColumn::make('name')
                        ->label(trans('filament-media-manager::messages.folders.columns.name'))
                        ->sortable()
                        ->searchable(),
                    Tables\Columns\TextColumn::make('description')
                        ->label(trans('filament-media-manager::messages.folders.columns.description'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('icon')
                        ->label(trans('filament-media-manager::messages.folders.columns.icon'))
                        ->sortable()
                        ->searchable(),
                    Tables\Columns\TextColumn::make('color')
                        ->label(trans('filament-media-manager::messages.folders.columns.color'))
                        ->sortable()
                        ->searchable(),
                    Tables\Columns\IconColumn::make('is_protected')
                        ->label(trans('filament-media-manager::messages.folders.columns.is_protected'))
                        ->sortable()
                        ->boolean(),
                    Tables\Columns\TextColumn::make('created_at')
                        ->dateTime()
                        ->sortable()
                        ->toggleable(isToggledHiddenByDefault: true),
                    Tables\Columns\TextColumn::make('updated_at')
                        ->dateTime()
                        ->sortable()
                        ->toggleable(isToggledHiddenByDefault: true),
                ])
            ])
            ->defaultPaginationPageOption(12)
            ->paginationPageOptions([
                "12",
                "24",
                "48",
                "96",
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFolders::route('/')
        ];
    }
}
