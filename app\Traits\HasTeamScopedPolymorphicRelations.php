<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Relations\MorphToMany;

trait HasTeamScopedPolymorphicRelations
{
    /**
     * Create a morph-to-many relationship without team scope conflicts
     */
    protected function morphToManyWithoutTeamScope(string $related, string $name, ?string $table = null, ?string $foreignPivotKey = null, ?string $relatedPivotKey = null, ?string $parentKey = null, ?string $relatedKey = null, bool $inverse = false): MorphToMany
    {
        $relation = $this->morphToMany($related, $name, $table, $foreignPivotKey, $relatedPivotKey, $parentKey, $relatedKey, $inverse);
        
        // Remove team scope from the related model to avoid column ambiguity
        return $relation->withoutGlobalScope('team');
    }

    /**
     * Create a morphed-by-many relationship without team scope conflicts
     */
    protected function morphedByManyWithoutTeamScope(string $related, string $name, ?string $table = null, ?string $foreignPivotKey = null, ?string $relatedPivotKey = null, ?string $parentKey = null, ?string $relatedKey = null): MorphToMany
    {
        $relation = $this->morphedByMany($related, $name, $table, $foreignPivotKey, $relatedPivotKey, $parentKey, $relatedKey);
        
        // Remove team scope from the related model to avoid column ambiguity
        return $relation->withoutGlobalScope('team');
    }
}
