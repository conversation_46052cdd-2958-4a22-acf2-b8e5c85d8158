<?php

namespace Database\Factories;

use App\Models\Course;
use Illuminate\Database\Eloquent\Factories\Factory;

class CourseFactory extends Factory
{
    protected $model = Course::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'type' => $this->faker->randomElement(['online', 'onsite', 'hybrid']),
            'price' => $this->faker->randomFloat(2, 50, 500),
            'currency' => 'USD',
            'duration_hours' => $this->faker->numberBetween(20, 120),
            'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'max_students' => $this->faker->numberBetween(10, 50),
            'start_date' => $this->faker->dateTimeBetween('now', '+3 months'),
            'end_date' => $this->faker->dateTimeBetween('+3 months', '+6 months'),
            'instructor' => $this->faker->name(),
            'requirements' => $this->faker->sentence(),
            'objectives' => $this->faker->paragraph(),
            'is_active' => true,
            'is_featured' => $this->faker->boolean(30),
            'sort_order' => $this->faker->numberBetween(1, 10),
        ];
    }
}
