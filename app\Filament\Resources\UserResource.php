<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'User Management';

    protected static ?int $navigationSort = 1;

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'users';

    public static function isScopedToTenant(): bool
    {
        // All users should be scoped to tenant
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    protected static function isSuperAdmin(): bool
    {
        $user = Auth::user();
        return $user && $user->team_id === null && $user->hasRole('super_admin');
    }

    protected static function canManageRoles(): bool
    {
        return static::isSuperAdmin() || Auth::user()?->hasRole('team_admin') ?? false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('User Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->label('Full Name'),

                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->label('Email Address'),

                        Forms\Components\TextInput::make('mobile')
                            ->tel()
                            ->maxLength(255)
                            ->label('Mobile Number'),

                        // Team selection - only visible to super admins
                        Forms\Components\Select::make('team_id')
                            ->relationship('team', 'name')
                            ->searchable()
                            ->preload()
                            ->placeholder('No team (Super Admin)')
                            ->visible(fn() => static::isSuperAdmin())
                            ->helperText('Super admins can assign users to any team or leave empty for global access.'),

                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->required(fn(string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->same('passwordConfirmation')
                            ->dehydrated(fn($state) => filled($state))
                            ->label('Password'),

                        Forms\Components\TextInput::make('passwordConfirmation')
                            ->password()
                            ->required(fn(string $context): bool => $context === 'create')
                            ->minLength(8)
                            ->dehydrated(false)
                            ->label('Confirm Password'),
                            
                        // Role assignment - restricted for tenant users
                        Forms\Components\Select::make('roles')
                            ->relationship(
                                name: 'roles',
                                titleAttribute: 'name',
                                modifyQueryUsing: function ($query) {
                                    // If user is not super admin, only show tenant-safe roles
                                    if (!static::isSuperAdmin()) {
                                        return $query->whereIn('name', ['team_admin', 'team_member', 'team_editor']);
                                    }
                                    return $query;
                                }
                            )
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->visible(fn() => static::canManageRoles())
                            ->helperText(function () {
                                if (!static::isSuperAdmin()) {
                                    return 'You can only assign tenant-level roles.';
                                }
                                return null;
                            }),
                                            
                        // Using CheckboxList Component
                        // Forms\Components\CheckboxList::make('roles')
                        //     ->relationship('roles', 'name')
                        //     ->searchable(),

                        // Using Select Component
                        // Forms\Components\Select::make('roles')
                        //     ->relationship('roles', 'name')
                        //     ->saveRelationshipsUsing(function (Model $record, $state) {
                        //         $record->roles()->syncWithPivotValues($state, [config('permission.column_names.team_foreign_key') => getPermissionsTeamId()]);
                        //     })
                        //     ->multiple()
                        //     ->preload()
                        //     ->searchable(),

                        // Using CheckboxList Component
                        // Forms\Components\CheckboxList::make('roles')
                        //     ->relationship(name: 'roles', titleAttribute: 'name')
                        //     ->saveRelationshipsUsing(function (Model $record, $state) {
                        //         $record->roles()->syncWithPivotValues($state, [config('permission.column_names.team_foreign_key') => getPermissionsTeamId()]);
                        //     })
                        //     ->searchable(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->default(true)
                            ->label('Active User')
                            ->helperText('Inactive users cannot access the system.'),

                        Forms\Components\DateTimePicker::make('email_verified_at')
                            ->label('Email Verified At')
                            ->displayFormat('M j, Y H:i')
                            ->helperText('Leave empty if email is not verified.'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->label('Full Name'),

                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->label('Email Address')
                    ->icon('heroicon-m-envelope'),

                Tables\Columns\TextColumn::make('mobile')
                    ->searchable()
                    ->label('Mobile Number')
                    ->icon('heroicon-m-phone')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('team.name')
                    ->label('Team')
                    ->placeholder('Global (Super Admin)')
                    ->badge()
                    ->color(fn ($state) => $state ? 'primary' : 'gray')
                    ->sortable()
                    ->toggleable()
                    ->description(fn ($record) => $record->team_id ? "ID: {$record->team_id}" : 'No team restriction'),

                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->sortable()
                    ->label('Status')
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('email_verified_at')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->label('Email Verified')
                    ->placeholder('Not verified')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('last_seen')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->label('Last Seen')
                    ->placeholder('Never')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->label('Created')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('M j, Y H:i')
                    ->sortable()
                    ->label('Updated')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active users only')
                    ->falseLabel('Inactive users only')
                    ->native(false),

                Tables\Filters\Filter::make('email_verified')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('email_verified_at'))
                    ->label('Email Verified'),

                Tables\Filters\Filter::make('email_unverified')
                    ->query(fn(Builder $query): Builder => $query->whereNull('email_verified_at'))
                    ->label('Email Unverified'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
