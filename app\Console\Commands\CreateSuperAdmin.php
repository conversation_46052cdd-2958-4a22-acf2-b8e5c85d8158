<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class CreateSuperAdmin extends Command
{
    protected $signature = 'make:super-admin {email} {--name=} {--password=}';
    protected $description = 'Create a super admin user';

    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->option('name') ?: $this->ask('Enter the user\'s name');
        $password = $this->option('password') ?: $this->secret('Enter password');

        // Check if user already exists
        $user = User::where('email', $email)->first();

        if ($user) {
            $this->info("User with email {$email} already exists.");
            // Super admins should have team_id = null
            if ($user->team_id !== null) {
                $user->update(['team_id' => null]);
                $this->info("Updated user to super admin (team_id = null).");
            }
        } else {
            // Create new super admin user with team_id = null
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
                'is_active' => true,
                'team_id' => null, // Super admins have no team restriction
            ]);

            $this->info("Super admin user {$name} created successfully (team_id = null).");
        }

        // Ensure super_admin role exists
        $superAdminRole = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web'
        ]);

        // Assign super admin role without team context (global role)
        if (!$user->hasRole('super_admin')) {
            try {
                // Temporarily disable teams for super admin role assignment
                $originalTeamsConfig = config('permission.teams');
                config(['permission.teams' => false]);

                // Clear cache to ensure config change takes effect
                app(PermissionRegistrar::class)->forgetCachedPermissions();

                // Assign the role without team context
                $user->assignRole('super_admin');

                // Restore original teams config
                config(['permission.teams' => $originalTeamsConfig]);
                app(PermissionRegistrar::class)->forgetCachedPermissions();

                $this->info("Super admin role assigned to {$user->name} (global access).");
            } catch (\Exception $e) {
                // Restore config in case of error
                config(['permission.teams' => $originalTeamsConfig ?? true]);
                app(PermissionRegistrar::class)->forgetCachedPermissions();

                $this->error("Failed to assign super admin role: " . $e->getMessage());
                $this->info("Trying alternative method...");

                // Alternative: Direct database insertion
                try {
                    DB::table('model_has_roles')->insert([
                        'role_id' => $superAdminRole->id,
                        'model_type' => get_class($user),
                        'model_id' => $user->id,
                        'team_id' => null,
                    ]);
                    $this->info("Super admin role assigned via direct database insertion.");
                } catch (\Exception $e2) {
                    $this->error("Alternative method also failed: " . $e2->getMessage());
                    $this->info("Please run: php artisan migrate");
                    return 1;
                }
            }
        } else {
            $this->info("{$user->name} already has super admin role.");
        }

        $this->info('Super admin setup completed!');
        $this->info("Email: {$user->email}");
        $this->info("Name: {$user->name}");
        $this->info("Team: Global (no team restriction)");
    }
}
