<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            My Teaching Schedule
        </x-slot>

        <x-slot name="headerEnd">
            <x-filament::button
                tag="a"
                href="{{ route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()]) }}"
                size="sm"
                color="gray"
                outlined
            >
                View All Tasks
            </x-filament::button> 
        </x-slot>

        <div class="space-y-6">
            <!-- Compact Timetable -->
            <div class="timetable-grid bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                {{-- <div class="grid grid-cols-8 gap-px bg-gray-200 dark:bg-gray-600 text-xs sm:text-sm"> --}}
                    <!-- Time column header -->
                    <div class="bg-gray-50 dark:bg-gray-700 px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 text-center">
                        Time
                    </div>

                    <!-- Day headers -->
                    @foreach(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] as $index => $dayName)
                        @php
                            $date = $startOfWeek->copy()->addDays($index);
                            $isToday = $date->isToday();
                        @endphp
                        <div class="timetable-cell bg-gray-50 dark:bg-gray-700 px-2 py-1 text-xs font-medium text-center {{ $isToday ? 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'text-gray-500 dark:text-gray-400' }}">
                            <div>{{ $dayName }}</div>
                            <div class="text-xs {{ $isToday ? 'text-blue-500' : 'text-gray-400' }}">
                                {{ $date->format('j') }}
                            </div>
                        </div>
                    @endforeach
                {{-- </div> --}}

                <!-- Time slots (compact - only show hours with content or key hours) -->
                @php
                    $keyHours = [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
                    $hasContentHours = [];

                    // Find hours that have content
                    foreach($weekData as $dayData) {
                        foreach($dayData['slots'] as $timeSlot => $items) {
                            if (!empty($items)) {
                                $hour = (int) substr($timeSlot, 0, 2);
                                $hasContentHours[] = $hour;
                            }
                        }
                    }

                    $displayHours = array_unique(array_merge($keyHours, $hasContentHours));
                    sort($displayHours);
                @endphp

                @foreach($displayHours as $hour)
                    @php $timeSlot = sprintf('%02d:00', $hour); @endphp
                    {{-- <div class="grid grid-cols-8 gap-px bg-gray-200 dark:bg-gray-600"> --}}
                        <!-- Time label -->
                        <div class="timetable-cell  bg-white dark:bg-gray-800 px-2 py-1 text-xs text-gray-500 dark:text-gray-400 text-center border-r">
                            {{ sprintf('%02d:00', $hour) }}
                        </div>

                        <!-- Day cells -->
                        @foreach(range(0, 6) as $dayIndex)
                            @php
                                $date = $startOfWeek->copy()->addDays($dayIndex);
                                $dateKey = $date->format('Y-m-d');
                                $items = $weekData[$dateKey]['slots'][$timeSlot] ?? [];
                                $isToday = $date->isToday();
                            @endphp
                            <div class="timetable-cell  relative {{ $isToday ? 'bg-blue-50 dark:bg-blue-900/20' : '' }}">
                                @foreach($items as $item)
                                    <div
                                        class="draggable-item {{ $item['type'] === 'task' ? 'task-item' : 'schedule-item' }} priority-{{ $item['priority'] ?? 'medium' }} cursor-pointer"
                                        data-id="{{ $item['id'] }}"
                                        data-type="{{ $item['type'] }}"
                                        title="{{ $item['title'] }} - {{ $item['start_time']->format('H:i') }}{{ $item['end_time'] ? '-' . $item['end_time']->format('H:i') : '' }}"
                                    >
                                        <div class="font-medium text-gray-900 dark:text-gray-100 truncate">
                                            {{ Str::limit($item['title'], 15, "...") }}
                                        </div>
                                        <div class="text-gray-600 dark:text-gray-400 flex items-center gap-1 text-xs">
                                            <span class="font-mono font-medium">{{ $item['start_time']->format('H:i') }}</span>
                                            @if($item['end_time'])
                                                <span class="text-gray-500">-{{ $item['end_time']->format('H:i') }}</span>
                                            @endif
                                            @if(($item['priority'] ?? '') === 'high' || ($item['priority'] ?? '') === 'urgent')
                                                ‼️
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    {{-- </div> --}}
                @endforeach
            </div>

            <!-- Today's Tasks Table -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                        Today's Tasks ({{ $today->format('M j, Y') }})
                    </h3>
                    @if($totalTodayTasks > 5)
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                            Showing 5 of {{ $totalTodayTasks }} tasks
                        </span>
                    @endif
                </div>
                
                @if($todayTasks->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Time</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Task</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Priority</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($todayTasks as $task)
                                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-gray-100">
                                                {{ $task->start_datetime->format('H:i') }}
                                                @if($task->end_datetime)
                                                    - {{ $task->end_datetime->format('H:i') }}
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                {{ $task->title }}
                                            </div>
                                            @if($task->description)
                                                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    {{ Str::limit($task->description, 50) }}
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                {{ $task->location ?: '-' }}
                                            </div>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $task->priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' : '' }}
                                                {{ $task->priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' : '' }}
                                                {{ $task->priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : '' }}
                                                {{ $task->priority === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : '' }}
                                            ">
                                                {{ ucfirst($task->priority) }}
                                            </span>
                                        </td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                                {{ $task->status === 'pending' ? 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' : '' }}
                                                {{ $task->status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : '' }}
                                            ">
                                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="px-4 py-8 text-center">
                        <div class="text-gray-500 dark:text-gray-400">
                            <x-heroicon-o-calendar-days class="w-8 h-8 mx-auto mb-2" />
                            <p class="text-sm">No tasks scheduled for today</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- View More Button -->
            <div class="text-center">
                <x-filament::button
                    tag="a"
                    href="{{ route('filament.backend.resources.tasks.index', ['tenant' => filament()->getTenant()]) }}"
                    color="primary"
                    size="sm"
                >
                    <x-heroicon-o-calendar class="w-4 h-4 mr-2" />
                    View Full Timetable
                </x-filament::button>
            </div>
        </div>
    </x-filament::section>

    <!-- Task Detail Popover -->
    <div id="task-popover" class="fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-50 p-4 hidden max-w-sm">
        <div id="popover-content">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>

    <!-- Task Detail Modal -->
    <div id="task-detail-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
                <div id="modal-content"></div>
            </div>
        </div>
    </div>

    <style>
        
        .time-slot {
            border-bottom: 1px solid #e5e7eb;
        }

        .time-slot:last-child {
            border-bottom: none;
        }

        .timetable-grid {
            display: grid;
            grid-template-columns: 80px repeat(7, 1fr);
            gap: 1px;
            background-color: #e5e7eb;
        }



        .timetable-cell {
            background-color: white;
            min-height: 60px;
            padding: 4px;
            position: relative;
        }

        .dark .timetable-cell {
            background-color: #374151;
        }

        .dark .timetable-grid {
            background-color: #4b5563;
        }
        .draggable-item {
            cursor: pointer;
            transition: opacity 0.2s ease, transform 0.2s ease;
        }

        .draggable-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 5;
            filter: brightness(1.05);
        }

        .drop-zone {
            min-height: 60px;
            transition: background-color 0.2s ease;
        }

        .task-item {
            border-radius: 4px;
            padding: 4px 8px;
            margin: 1px 0;
            font-size: 12px;
            line-height: 1.3;
            border-left: 3px solid;
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            position: relative;
            overflow: hidden;
        }

        .task-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.05));
        }

        .task-item.priority-high {
            border-left-color: #EF4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .task-item.priority-urgent {
            border-left-color: #DC2626;
            background-color: rgba(220, 38, 38, 0.15);
        }

        .task-item.priority-medium {
            border-left-color: #F59E0B;
            background-color: rgba(245, 158, 11, 0.1);
        }

        .task-item.priority-low {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .schedule-item {
            border-left-color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        #task-popover {
            animation: popoverFadeIn 0.15s ease-out;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        @keyframes popoverFadeIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-5px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
    </style>



    <script>
        document.addEventListener('DOMContentLoaded', function() {
             setTimeout(() => {
                console.log('DOM fully loaded and parsed');
                // }, 10);

                // Task/Schedule popover functionality
                let popoverData = {
                    show: false,
                    currentTaskId: null,
                    currentTaskType: null,
                    position: { x: 0, y: 0 }
                };

                // Store task data for popover
                const taskData = @json($weekData);

                // Add click event listeners to all draggable items
                document.addEventListener('click', function(e) {
                    alert("test");
                    console.log('Click detected on:', e.target);
                    const taskItem = e.target.closest('.draggable-item');
                    console.log('Task item found:', taskItem);

                    if (taskItem) {
                        e.stopPropagation();
                        const taskId = taskItem.getAttribute('data-id');
                        const taskType = taskItem.getAttribute('data-type');
                        console.log('Task ID:', taskId, 'Type:', taskType);

                        // Find the task data
                        const task = findTaskById(taskId, taskType);
                        console.log('Task data found:', task);

                        if (task) {
                            showTaskPopover(e, task);
                        }
                    }
                });

                function findTaskById(taskId, taskType) {
                    for (const dateKey in taskData) {
                        for (const timeSlot in taskData[dateKey].slots) {
                            const items = taskData[dateKey].slots[timeSlot];
                            for (const item of items) {
                                if (item.id == taskId && item.type === taskType) {
                                    return item;
                                }
                            }
                        }
                    }
                    return null;
                }

                function showTaskPopover(event, task) {
                    event.stopPropagation();
                    event.preventDefault();

                    // Build popover content with task details
                    const content = document.getElementById('popover-content');
                    const startTime = new Date(task.start_time).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    const endTime = task.end_time ? new Date(task.end_time).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    }) : null;

                    let html = `
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium text-gray-900 dark:text-gray-100 text-sm">${task.title}</h4>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${task.type === 'task' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'}">
                                    ${task.type === 'task' ? 'Task' : 'Class'}
                                </span>
                            </div>

                            <div class="space-y-2 text-sm">
                                <div class="flex items-center text-gray-600 dark:text-gray-400">
                                    <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span>${startTime}${endTime ? ' - ' + endTime : ''}</span>
                                </div>

                                ${task.location ? `
                                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span>${task.location}</span>
                                    </div>
                                ` : ''}

                                ${task.priority ? `
                                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                                        </svg>
                                        <span>Priority: ${task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}</span>
                                    </div>
                                ` : ''}

                                ${task.status ? `
                                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>Status: ${task.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                                    </div>
                                ` : ''}

                                ${task.assigned_to ? `
                                    <div class="flex items-center text-gray-600 dark:text-gray-400">
                                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span>Assigned to: ${task.assigned_to}</span>
                                    </div>
                                ` : ''}

                                ${(task.description || task.notes) ? `
                                    <div class="text-gray-600 dark:text-gray-400 text-xs mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                                        <div class="font-medium mb-1">Description:</div>
                                        <div>${task.description || task.notes}</div>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;

                    content.innerHTML = html;

                    // Position the popover
                    const rect = event.target.closest('.draggable-item').getBoundingClientRect();
                    const popoverWidth = 320;
                    const popoverHeight = 200;

                    let left = rect.right + 10;
                    let top = rect.top;

                    // Adjust if popover would go off-screen
                    if (left + popoverWidth > window.innerWidth) {
                        left = rect.left - popoverWidth - 10;
                    }

                    if (top + popoverHeight > window.innerHeight) {
                        top = rect.bottom - popoverHeight;
                    }

                    if (top < 0) {
                        top = rect.bottom + 10;
                    }

                    // Update popover position and show it
                    const popover = document.getElementById('task-popover');
                    if (popover) {
                        popover.style.left = left + 'px';
                        popover.style.top = top + 'px';
                        popover.classList.remove('hidden');
                    }
                }

                function closeTaskPopover() {
                    const popover = document.getElementById('task-popover');
                    if (popover) {
                        popover.classList.add('hidden');
                    }
                }

                // Close popover when clicking outside
                document.addEventListener('click', function(e) {
                    const popover = document.getElementById('task-popover');
                    if (popover && !popover.contains(e.target) && !e.target.closest('.draggable-item')) {
                        closeTaskPopover();
                    }
                });
            }, 10);
        });
    </script>
</x-filament-widgets::widget>
