# Social Authentication Configuration for EduNest
# Copy these variables to your .env file and replace with your actual credentials
#
# IMPORTANT: Add these to your main .env file for the login/register pages to work properly

# Social Authentication Master Switch
SOCIAL_AUTH_ENABLED=true

# Individual Provider Enable/Disable Switches
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true
PHONE_LOGIN_ENABLED=true

# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/google/callback

# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_REDIRECT_URI=http://127.0.0.1:8000/auth/microsoft/callback

# Apple Sign In Configuration
APPLE_CLIENT_ID=your.apple.client.id.here
APPLE_TEAM_ID=YOUR_TEAM_ID
APPLE_KEY_ID=YOUR_KEY_ID
APPLE_PRIVATE_KEY_PATH=storage/app/private/apple_private_key.p8
APPLE_REDIRECT_URI=http://127.0.0.1:8000/auth/apple/callback

# LINE Login Configuration
LINE_CLIENT_ID=your_line_channel_id_here
LINE_CLIENT_SECRET=your_line_channel_secret_here
LINE_REDIRECT_URI=http://127.0.0.1:8000/auth/line/callback

# Phone Authentication (SMS) Configuration
SMS_PROVIDER=twilio

# Twilio Configuration (Recommended)
TWILIO_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********

# Alternative: AWS SNS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=ap-southeast-1
AWS_SNS_REGION=ap-southeast-1

# Alternative: Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id_here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_firebase_client_id_here
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Session Configuration for Social Auth
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_DRIVER=file

# Security Settings
BCRYPT_ROUNDS=12
HASH_VERIFY=true

# Rate Limiting for Authentication
THROTTLE_LOGIN_ATTEMPTS=5
THROTTLE_LOGIN_DECAY_MINUTES=1
THROTTLE_OTP_ATTEMPTS=3
THROTTLE_OTP_DECAY_MINUTES=5
