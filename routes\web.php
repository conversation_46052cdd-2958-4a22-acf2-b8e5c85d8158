<?php

use App\Livewire\Form;
use App\Http\Controllers\HomepageController;

// Homepage route
\Illuminate\Support\Facades\Route::get('/', [HomepageController::class, 'index'])->name('homepage');

// Downloads page route
\Illuminate\Support\Facades\Route::get('/downloads', [HomepageController::class, 'downloads'])->name('downloads');

// Student dashboard page route
\Illuminate\Support\Facades\Route::get('/student-dashboard', [HomepageController::class, 'studentDashboard'])->name('student-dashboard');

// Teacher dashboard page route
\Illuminate\Support\Facades\Route::get('/teacher-dashboard', [HomepageController::class, 'teacherDashboard'])->name('teacher-dashboard');

// Teacher event list page route
\Illuminate\Support\Facades\Route::get('/teacher-eventlist', [HomepageController::class, 'teacherEventList'])->name('teacher-eventlist');

// Teacher event show page route
\Illuminate\Support\Facades\Route::get('/teacher-eventshow', [HomepageController::class, 'teacherEventShow'])->name('teacher-eventshow');

// Teacher timetable page route
\Illuminate\Support\Facades\Route::get('/teacher-timetable', [HomepageController::class, 'teacherTimetable'])->name('teacher-timetable');

// Register page route
\Illuminate\Support\Facades\Route::get('/register', [HomepageController::class, 'register'])->name('register');

// Registration form submission
use App\Http\Controllers\Auth\RegisterController;
\Illuminate\Support\Facades\Route::post('/register', [RegisterController::class, 'store'])->name('register.store');
\Illuminate\Support\Facades\Route::post('/api/register', [RegisterController::class, 'apiStore'])->name('register.api');

// Login page route
\Illuminate\Support\Facades\Route::get('/login', [HomepageController::class, 'login'])->name('login');

// Login form submission
use App\Http\Controllers\Auth\LoginController;
\Illuminate\Support\Facades\Route::post('/login', [LoginController::class, 'authenticate'])->name('login.authenticate');
\Illuminate\Support\Facades\Route::post('/api/login', [LoginController::class, 'apiAuthenticate'])->name('login.api');

// Social Authentication Routes
use App\Http\Controllers\SocialAuthController;

// Social login redirects
\Illuminate\Support\Facades\Route::get('/auth/{provider}', [SocialAuthController::class, 'redirectToProvider'])->name('social.redirect');

// Social login callbacks
\Illuminate\Support\Facades\Route::get('/auth/{provider}/callback', [SocialAuthController::class, 'handleProviderCallback'])->name('social.callback');

// Live Streaming Routes
use App\Http\Controllers\LiveStreamController;

\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    // Stream management (for teachers/admins)
    \Illuminate\Support\Facades\Route::get('/live-videos/{liveVideo}/stream', [LiveStreamController::class, 'stream'])->name('live-videos.stream');

    // Watch stream (for viewers)
    \Illuminate\Support\Facades\Route::get('/live-videos/{liveVideo}/watch', [LiveStreamController::class, 'watch'])->name('live-videos.watch');
});

// Phone authentication API routes
\Illuminate\Support\Facades\Route::post('/api/auth/phone/send-otp', [SocialAuthController::class, 'sendPhoneOtp'])->name('phone.send-otp');
\Illuminate\Support\Facades\Route::post('/api/auth/phone/verify-otp', [SocialAuthController::class, 'verifyPhoneOtp'])->name('phone.verify-otp');

// Profile Routes
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SocialAccountController;

\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    \Illuminate\Support\Facades\Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    \Illuminate\Support\Facades\Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    \Illuminate\Support\Facades\Route::post('/profile/skip', [ProfileController::class, 'skip'])->name('profile.skip');

    // Social Account Management Routes
    \Illuminate\Support\Facades\Route::get('/profile/connect/{provider}', [SocialAccountController::class, 'connect'])->name('social.connect');
    \Illuminate\Support\Facades\Route::get('/profile/connect/{provider}/callback', [SocialAccountController::class, 'callback'])->name('social.connect.callback');
    \Illuminate\Support\Facades\Route::post('/profile/disconnect/{provider}', [SocialAccountController::class, 'disconnect'])->name('social.disconnect');
});

// Logout Route
\Illuminate\Support\Facades\Route::post('/logout', function () {
    Auth::logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();
    return redirect('/');
})->name('logout');

// Debug route (remove in production)
\Illuminate\Support\Facades\Route::get('/debug-profile', function () {
    if (!Auth::check()) {
        return 'Not logged in';
    }

    $user = Auth::user();
    $profile = $user->getOrCreateProfile();
    $role = $user->getPrimaryRole();

    return [
        'user_id' => $user->id,
        'user_email' => $user->email,
        'user_name' => $user->name,
        'role' => $role,
        'profile_exists' => !!$profile,
        'profile_id' => $profile?->id,
        'needs_completion' => $user->needsProfileCompletion(),
        'dashboard_route' => $user->getDashboardRoute(),
        'has_profile_image' => !!($profile && $profile->profile_image),
        'has_avatar' => !!$user->avatar,
        'is_super_admin' => $user->hasRole('super_admin'),
        'is_school' => $user->hasRole('school'),
    ];
})->middleware('auth');

// Test header route (remove in production)
\Illuminate\Support\Facades\Route::get('/test-header', function () {
    return view('test-header');
})->middleware('auth');

// SVG Demo page route (can be disabled via SVG_DEMO_ENABLED=false in .env)
\Illuminate\Support\Facades\Route::get('/svg-demo', function () {
    return view('svg-demo');
})->name('svg-demo')->middleware(\App\Http\Middleware\SvgDemoEnabled::class);

// SVG Test route (for debugging)
\Illuminate\Support\Facades\Route::get('/svg-test', function () {
    return view('svg-test');
});

// Dashboard Routes (for testing)
\Illuminate\Support\Facades\Route::middleware(['auth', 'profile.complete'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/dashboard', function () {
        return view('dashboard', ['user' => auth()->user()]);
    })->name('dashboard');

    \Illuminate\Support\Facades\Route::get('/student-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'student']);
    })->name('student-dashboard');

    \Illuminate\Support\Facades\Route::get('/parent-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'parent']);
    })->name('parent-dashboard');

    \Illuminate\Support\Facades\Route::get('/teacher-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'teacher']);
    })->name('teacher-dashboard');

    \Illuminate\Support\Facades\Route::get('/school-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'school']);
    })->name('school-dashboard');
});

\Illuminate\Support\Facades\Route::get('form', Form::class);

// Test route for media manager debugging
\Illuminate\Support\Facades\Route::get('/test-media-manager', function () {
    $folders = \App\Models\MediaManager\Folder::withoutGlobalScopes()->get();
    return response()->json([
        'folders_count' => $folders->count(),
        'folders' => $folders->map(function ($folder) {
            return [
                'id' => $folder->id,
                'name' => $folder->name,
                'collection' => $folder->collection,
                'custom_properties' => $folder->custom_properties,
            ];
        }),
    ]);
});

// Test route for filament context debugging
\Illuminate\Support\Facades\Route::get('/test-filament-context', function () {
    return response()->json([
        'has_filament' => function_exists('filament'),
        'current_panel' => filament() ? filament()->getCurrentPanel()?->getId() : null,
        'has_tenant' => filament() ? filament()->hasTenancy() : null,
        'current_tenant' => filament() ? filament()->getTenant() : null,
        'user' => auth()->user()?->only(['id', 'name', 'email']),
        'user_team' => auth()->user()?->team?->only(['id', 'name', 'slug']),
    ]);
});

// Debug route that simulates being inside a tenant context
\Illuminate\Support\Facades\Route::get('/debug-tenant/{team}', function (\App\Models\Team $team) {
    // Simulate being logged in as the first user of this team
    $user = $team->users()->first();
    if ($user) {
        auth()->login($user);
    }

    return response()->json([
        'team' => $team->only(['id', 'name', 'slug']),
        'user' => auth()->user()?->only(['id', 'name', 'email']),
        'folders_in_team' => \App\Models\MediaManager\Folder::withoutGlobalScopes()
            ->where('team_id', $team->id)
            ->get(['id', 'name', 'collection', 'team_id', 'owner_id']),
        'media_manager_url' => "/backend/{$team->slug}/folders",
    ]);
})->where('team', '[0-9]+');

// Debug route to check available teams
\Illuminate\Support\Facades\Route::get('/debug-teams', function () {
    return response()->json([
        'all_teams' => \App\Models\Team::all(['id', 'name', 'slug']),
        'current_user' => auth()->user()?->only(['id', 'name', 'email', 'team_id']),
        'current_user_team' => auth()->user()?->team?->only(['id', 'name', 'slug']),
        'current_user_roles' => auth()->user()?->roles?->pluck('name'),
    ]);
});

// Test route to check if media manager resources are accessible
\Illuminate\Support\Facades\Route::get('/test-media-manager', function () {
    return response()->json([
        'media_resource_exists' => class_exists(\App\Filament\Resources\MediaManager\MediaResource::class),
        'folder_resource_exists' => class_exists(\App\Filament\Resources\MediaManager\FolderResource::class),
        'service_provider_loaded' => class_exists(\TomatoPHP\FilamentMediaManager\FilamentMediaManagerServiceProvider::class),
        'translations_available' => trans('filament-media-manager::messages.media.meta.delete-media'),
        'views_available' => view()->exists('filament-media-manager::pages.folders'),
    ]);
});

// Debug route to check uploaded media files
\Illuminate\Support\Facades\Route::get('/debug-media-files', function () {
    $media = \App\Models\MediaManager\Media::withoutGlobalScopes()
        ->latest()
        ->take(10)
        ->get();

    $mediaInfo = $media->map(function ($item) {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'file_name' => $item->file_name,
            'disk' => $item->disk,
            'full_path' => $item->getPath(),
            'full_url' => $item->getFullUrl(),
            'file_exists' => file_exists($item->getPath()),
            'storage_path' => storage_path('app/' . $item->disk . '/' . $item->id . '/' . $item->file_name),
            'public_path' => public_path('storage/' . $item->id . '/' . $item->file_name),
            'created_at' => $item->created_at,
            'team_id' => $item->team_id,
            'created_by' => $item->created_by,
        ];
    });

    return response()->json([
        'total_media_count' => \App\Models\MediaManager\Media::withoutGlobalScopes()->count(),
        'recent_uploads' => $mediaInfo,
        'storage_disk_config' => config('filesystems.disks'),
        'default_disk' => config('filesystems.default'),
        'media_library_disk' => config('media-library.disk_name'),
        'storage_link_exists' => is_link(public_path('storage')),
        'storage_link_target' => is_link(public_path('storage')) ? readlink(public_path('storage')) : 'No link',
        'storage_app_public_exists' => is_dir(storage_path('app/public')),
        'storage_app_public_writable' => is_writable(storage_path('app/public')),
    ]);
});

// Debug route to fix storage link if needed
\Illuminate\Support\Facades\Route::get('/fix-storage-link', function () {
    $publicStoragePath = public_path('storage');
    $storageAppPublicPath = storage_path('app/public');

    $result = [
        'before' => [
            'link_exists' => is_link($publicStoragePath),
            'target' => is_link($publicStoragePath) ? readlink($publicStoragePath) : 'No link',
            'storage_dir_exists' => is_dir($storageAppPublicPath),
        ]
    ];

    // Remove existing link/directory if it exists
    if (file_exists($publicStoragePath)) {
        if (is_link($publicStoragePath)) {
            unlink($publicStoragePath);
        } else {
            rmdir($publicStoragePath);
        }
    }

    // Create the storage directory if it doesn't exist
    if (!is_dir($storageAppPublicPath)) {
        mkdir($storageAppPublicPath, 0755, true);
    }

    // Create the symbolic link
    $linkCreated = symlink($storageAppPublicPath, $publicStoragePath);

    $result['after'] = [
        'link_created' => $linkCreated,
        'link_exists' => is_link($publicStoragePath),
        'target' => is_link($publicStoragePath) ? readlink($publicStoragePath) : 'No link',
        'storage_dir_exists' => is_dir($storageAppPublicPath),
    ];

    return response()->json($result);
});
