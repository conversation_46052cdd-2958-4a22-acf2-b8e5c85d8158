<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('books', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->nullable()->constrained('teams')->nullOnDelete();
            $table->foreignId('subject_id')->constrained('subjects')->cascadeOnDelete();
            $table->string('title');
            $table->string('author');
            $table->text('description')->nullable();
            $table->string('isbn')->nullable(); // International Standard Book Number
            $table->string('publisher')->nullable();
            $table->date('published_date')->nullable();
            $table->string('edition')->nullable();
            $table->integer('pages')->nullable();
            $table->string('language')->default('English');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Indexes
            $table->index(['team_id', 'subject_id']);
            $table->index(['team_id', 'is_active']);
            $table->index('isbn');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('books');
    }
};
