<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Social authentication fields
            $table->string('provider')->nullable()->after('email_verified_at');
            $table->string('provider_id')->nullable()->after('provider');
            $table->text('provider_token')->nullable()->after('provider_id');
            $table->text('provider_refresh_token')->nullable()->after('provider_token');

            // Phone authentication fields
            $table->string('phone')->nullable()->unique()->after('email');
            $table->timestamp('phone_verified_at')->nullable()->after('phone');

            // Avatar field for social profiles
            $table->string('avatar')->nullable()->after('provider_refresh_token');

            // Make email nullable for phone-only registration
            $table->string('email')->nullable()->change();

            // Add indexes for performance
            $table->index(['provider', 'provider_id']);
            $table->index('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['provider', 'provider_id']);
            $table->dropIndex(['phone']);

            // Drop columns
            $table->dropColumn([
                'provider',
                'provider_id',
                'provider_token',
                'provider_refresh_token',
                'phone',
                'phone_verified_at',
                'avatar'
            ]);

            // Make email required again
            $table->string('email')->nullable(false)->change();
        });
    }
};
